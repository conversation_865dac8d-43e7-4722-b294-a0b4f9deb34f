<?php

use App\Http\Middleware\CheckRole;
use App\Http\Middleware\EnsureEmailIsVerified;
use App\Http\Middleware\PermissionMiddleware;
use App\Http\Middleware\AdminMiddleware;
use App\Http\Middleware\CacheControlMiddleware;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Request;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Spatie\Permission\Exceptions\UnauthorizedException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
        apiPrefix: 'api/v1', // Recommended API versioning
    )
    ->withProviders([
        \L5Swagger\L5SwaggerServiceProvider::class,
    ])
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->api(prepend: [
            \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
            \Illuminate\Routing\Middleware\ThrottleRequests::class . ':api',

        ]);

        $middleware->alias([
            'verified' => EnsureEmailIsVerified::class,
            'role' => CheckRole::class,
            'admin' => AdminMiddleware::class,
            'cache.control' => CacheControlMiddleware::class,
        ]);

        // Global middleware
        // $middleware->append(\Illuminate\Routing\Middleware\SubstituteBindings::class);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // Standard JSON response format
        $exceptions->render(function (Throwable $e, Request $request) {
            if ($request->is('api/*')) {
                $statusCode = method_exists($e, 'getStatusCode')
                    ? $e->getStatusCode()
                    : 500;

                $response = [
                    'success' => false,
                    'message' => $e->getMessage(),
                    'errors' => [],
                    'code' => $statusCode,
                    'timestamp' => now()->toISOString(),
                ];

                // Add validation errors if available
                if ($e instanceof ValidationException) {
                    $response['errors'] = $e->errors();
                    $response['message'] = 'Validation failed';
                    $statusCode = 422;
                }

                // Customize specific exception messages
                switch (true) {
                    case $e instanceof AuthenticationException:
                        $response['message'] = 'Unauthenticated';
                        $statusCode = 401;
                        break;

                    case $e instanceof UnauthorizedException:
                        $response['message'] = 'Insufficient permissions';
                        $statusCode = 403;
                        $response['required'] = [
                            'roles' => $e->getRequiredRoles(),
                            'permissions' => $e->getRequiredPermissions(),
                        ];
                        break;

                    case $e instanceof NotFoundHttpException:
                        $response['message'] = 'Resource not found';
                        $statusCode = 404;
                        break;

                    case $e instanceof MethodNotAllowedHttpException:
                        $response['message'] = 'Method not allowed';
                        $statusCode = 405;
                        break;
                }

                // Hide sensitive error details in production
                if (app()->environment('production') && $statusCode === 500) {
                    $response['message'] = 'Server Error';
                }

                return response()->json($response, $statusCode);
            }
        });
    })->create();
