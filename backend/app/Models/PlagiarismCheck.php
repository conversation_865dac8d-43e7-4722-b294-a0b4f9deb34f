<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PlagiarismCheck extends Model
{
    use HasFactory;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id',
        'manuscript_id',
        'user_id',
        'status',
        'similarity_percentage',
        'report_path',
        'completed_at'
    ];

    protected $casts = [
        'similarity_percentage' => 'decimal:2',
        'completed_at' => 'datetime'
    ];

    /**
     * Get the manuscript that was checked
     */
    public function manuscript(): BelongsTo
    {
        return $this->belongsTo(Manuscript::class);
    }

    /**
     * Get the user who initiated the check
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
