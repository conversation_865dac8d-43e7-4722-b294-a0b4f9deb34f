<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use App\Notifications\ManuscriptStatusChanged;
use App\Notifications\ManuscriptSubmitted;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Manuscript extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'title',
        'abstract',
        'keywords',
        'translated_abstract',
        'revision_notes',
        'file_path',
        'language',
        'status',
        'payment_status',
        'author_id',
        'issue_id',
        'authors',
        'submission_id',
        'doi',
        'published_date',
        'resubmitted_at',
        'is_direct_published',
        'featured'
    ];

    // Temporary property to track status changes (not saved to database)
    public $statusChangeData = null;

    protected $casts = [
        'keywords' => 'array',
        'authors' => 'array',
        'published_date' => 'datetime',
        'is_direct_published' => 'boolean',
        'featured' => 'boolean'
    ];

    /**
     * Activity log configuration
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['title', 'status', 'payment_status', 'author_id', 'issue_id'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn(string $eventName) => "Manuscript {$eventName}: {$this->title}");
    }

    // Relationships
    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    public function getAuthorsAttribute($value)
    {
        // convert to json if not already
        if (!is_array($value)) {
            $value = json_decode($value, true);
        }
        return $value;
    }

    public function reviewers()
    {
        return $this->belongsToMany(User::class, 'manuscript_reviewer', 'manuscript_id', 'reviewer_id')
            ->withPivot(['assigned_at', 'completed_at'])
            ->withTimestamps();
    }

    public function issue()
    {
        return $this->belongsTo(Issue::class);
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    public function payment()
    {
        return $this->hasOne(Payment::class);
    }

    public function versions()
    {
        return $this->hasMany(ManuscriptVersion::class)->orderBy('version_number', 'desc');
    }

    // Generate submission ID and DOI on creation
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            // Generate submission ID using the manuscript's creation year
            $year = $model->created_at ? $model->created_at->year : now()->year;
            $count = Manuscript::whereYear('created_at', $year)->count() + 1;

            $model->submission_id = 'JEMS-' . $year . '-' . str_pad(
                $count,
                3, '0', STR_PAD_LEFT
            );

            // Generate DOI if not provided
            if (!$model->doi) {
                $model->doi = '10.xxxx/JEMS.' . $year . '.' . str_pad(
                    $count,
                    3, '0', STR_PAD_LEFT
                );
            }
        });

        static::created(function ($manuscript) {
            // Send submission confirmation email to author
            if ($manuscript->author && !$manuscript->is_direct_published) {
                $manuscript->author->notify(new ManuscriptSubmitted($manuscript));
            }
        });

        static::updating(function ($manuscript) {
            // Check if status is changing
            if ($manuscript->isDirty('status')) {
                $oldStatus = $manuscript->getOriginal('status');
                $newStatus = $manuscript->status;

                // Store the status change in a temporary property
                $manuscript->statusChangeData = [
                    'old' => $oldStatus,
                    'new' => $newStatus
                ];
            }
        });

        static::updated(function ($manuscript) {
            // Send notification if status changed
            if (isset($manuscript->statusChangeData) && $manuscript->author) {
                $oldStatus = $manuscript->statusChangeData['old'];
                $newStatus = $manuscript->statusChangeData['new'];

                $manuscript->author->notify(new ManuscriptStatusChanged($manuscript, $oldStatus, $newStatus));
            }
        });
    }
}
