<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Issue extends Model
{
    use HasFactory;

    protected $fillable = [
        'volume_id',
        'type',
        'number',
        'published',
        'is_current',
        'publication_date',
        'submission_deadline',
        'review_deadline'
    ];

    protected $casts = [
        'submission_deadline' => 'date',
        'review_deadline' => 'date',
        'publication_date' => 'date',
        'published' => 'boolean',
        'is_current' => 'boolean'
    ];

    // Relationships
    public function volume()
    {
        return $this->belongsTo(Volume::class);
    }

    public function manuscripts()
    {
        return $this->hasMany(Manuscript::class);
    }

    // Scopes
    public function scopeCurrent($query)
    {
        return $query->where('is_current', true);
    }

    public function scopeAcceptingSubmissions($query)
    {
        return $query->where('submission_deadline', '>', now());
    }

    public function scopeJanuary($query)
    {
        return $query->where('type', 'January');
    }

    public function scopeJune($query)
    {
        return $query->where('type', 'June');
    }

    /**
     * Boot method to ensure only one issue can be current at a time
     */
    protected static function boot()
    {
        parent::boot();

        static::updating(function ($issue) {
            // If this issue is being set as current, unset all other current issues
            if ($issue->is_current && $issue->isDirty('is_current')) {
                static::where('id', '!=', $issue->id)->update(['is_current' => false]);
            }
        });

        static::creating(function ($issue) {
            // If this new issue is being set as current, unset all other current issues
            if ($issue->is_current) {
                static::where('id', '!=', $issue->id ?? 0)->update(['is_current' => false]);
            }
        });
    }
}
