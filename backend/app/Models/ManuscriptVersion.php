<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ManuscriptVersion extends Model
{
    use HasFactory;

    protected $fillable = [
        'manuscript_id',
        'version_number',
        'title',
        'abstract',
        'keywords',
        'translated_abstract',
        'file_path',
        'authors',
        'revision_notes',
        'status',
        'submitted_at',
    ];

    protected $casts = [
        'keywords' => 'array',
        'authors' => 'array',
        'submitted_at' => 'datetime',
    ];

    public function manuscript()
    {
        return $this->belongsTo(Manuscript::class);
    }
}
