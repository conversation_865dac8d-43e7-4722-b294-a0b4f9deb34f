<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Review extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'manuscript_id',
        'reviewer_id',
        'recommendation',
        'feedback',
        'confidential_comments',
        'submitted_at',
        'completed_at'
    ];

    protected $casts = [
        'submitted_at' => 'datetime',
        'completed_at' => 'datetime'
    ];

    /**
     * Activity log configuration
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['manuscript_id', 'reviewer_id', 'recommendation', 'submitted_at', 'completed_at'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn(string $eventName) => "Review {$eventName} for manuscript #{$this->manuscript_id}");
    }

    // Relationships
    public function manuscript()
    {
        return $this->belongsTo(Manuscript::class);
    }

    public function reviewer()
    {
        return $this->belongsTo(User::class, 'reviewer_id');
    }

    // Scopes
    public function scopeSubmitted($query)
    {
        return $query->whereNotNull('submitted_at');
    }

    public function scopePending($query)
    {
        return $query->whereNull('submitted_at');
    }

    public function scopeCompleted($query)
    {
        return $query->whereNotNull('completed_at');
    }

    public function scopeInProgress($query)
    {
        return $query->whereNotNull('submitted_at')->whereNull('completed_at');
    }

    // Helper methods
    public function isSubmitted()
    {
        return !is_null($this->submitted_at);
    }

    public function isCompleted()
    {
        return !is_null($this->completed_at);
    }

    public function getStatusAttribute()
    {
        if (!$this->isSubmitted()) {
            return 'pending';
        }

        if ($this->isCompleted()) {
            return 'completed';
        }

        return 'in_progress';
    }
}
