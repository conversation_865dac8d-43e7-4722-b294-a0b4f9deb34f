<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PublicContent extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'title',
        'content',
        'language',
        'status',
        'featured',
        'priority',
        'created_by'
    ];

    protected $casts = [
        'title' => 'array',
        'content' => 'array',
        'featured' => 'boolean',
        'priority' => 'integer'
    ];

    // Relationships
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeAnnouncements($query)
    {
        return $query->where('type', 'announcement');
    }

    public function scopeGuidelines($query)
    {
        return $query->where('type', 'guidelines');
    }

    public function scopeEditorials($query)
    {
        return $query->where('type', 'editorial');
    }

    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    public function scopeByLanguage($query, $language)
    {
        return $query->where('language', $language);
    }
}
