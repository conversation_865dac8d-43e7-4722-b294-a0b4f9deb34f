<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Volume extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'number',
        'year',
        'published',
        'publication_date',
        'description',
        'cover_image',
    ];

    protected $casts = [
        'published' => 'boolean',
        'publication_date' => 'datetime',
    ];

    // Relationships
    public function issues()
    {
        return $this->hasMany(Issue::class);
    }

    public function manuscripts()
    {
        return $this->hasMany(Manuscript::class);
    }

    // Scopes
    public function scopePublished($query)
    {
        return $query->where('published', true);
    }
}
