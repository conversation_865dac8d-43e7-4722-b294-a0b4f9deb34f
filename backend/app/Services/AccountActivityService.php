<?php

namespace App\Services;

use App\Models\UserDevice;
use App\Notifications\AccountActivity;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON><PERSON>\Agent\Agent;

class AccountActivityService
{
    protected $agent;

    public function __construct()
    {
        $this->agent = new Agent();
    }

    public function detectAndNotify($request)
    {
        if (!Auth::check()) {
            return;
        }

        $user = Auth::user();
        $deviceInfo = [
            'device' => $this->agent->device(),
            'browser' => $this->agent->browser(),
            'browser_version' => $this->agent->version($this->agent->browser()),
            'platform' => $this->agent->platform(),
            'platform_version' => $this->agent->version($this->agent->platform()),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ];

        // Get location from IP (you might want to use a service like MaxMind GeoIP2)
        $location = null; // Implement IP geolocation here

        // Store or update device information
        $device = $this->storeDeviceInfo($user, $deviceInfo, $request);

        // Detect suspicious activity
        $suspiciousActivity = $this->detectSuspiciousActivity($request, $deviceInfo, $device);

        if ($suspiciousActivity) {
            $user->notify(new AccountActivity(
                $suspiciousActivity,
                $deviceInfo,
                $location
            ));
        }
    }

    protected function storeDeviceInfo($user, $deviceInfo, $request)
    {
        return UserDevice::updateOrCreate(
            [
                'user_id' => $user->id,
                'ip_address' => $deviceInfo['ip'],
            ],
            [
                'device_type' => $deviceInfo['device'],
                'browser' => $deviceInfo['browser'],
                'browser_version' => $deviceInfo['browser_version'],
                'platform' => $deviceInfo['platform'],
                'platform_version' => $deviceInfo['platform_version'],
                'user_agent' => $deviceInfo['user_agent'],
                'last_used_at' => now(),
            ]
        );
    }

    protected function detectSuspiciousActivity($request, $deviceInfo, $device)
    {
        // Check for login from new device
        if ($request->is('api/v1/auth/login')) {
            if (!$device->is_trusted) {
                return 'Login from new device';
            }
        }

        // Check for password change
        if ($request->is('api/v1/auth/password') && $request->isMethod('put')) {
            return 'Password changed';
        }

        // Check for email change
        if ($request->is('api/v1/auth/email') && $request->isMethod('put')) {
            return 'Email address changed';
        }

        // Add more suspicious activity checks here

        return null;
    }
}
