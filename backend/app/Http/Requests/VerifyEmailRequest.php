<?php

namespace App\Http\Requests;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\URL;

class VerifyEmailRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id' => ['required', 'integer'],
            'hash' => ['required', 'string'],
        ];
    }

    /**
     * Get the user that should be verified.
     */
    public function getVerificationUser(): ?User
    {
        $user = User::find($this->id);

        if (!$user) {
            return null;
        }

        if (!hash_equals(
            (string) $this->hash,
            sha1($user->getEmailForVerification())
        )) {
            return null;
        }

        return $user;
    }
}
