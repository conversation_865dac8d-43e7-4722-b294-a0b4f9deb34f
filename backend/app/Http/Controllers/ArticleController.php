<?php

namespace App\Http\Controllers;

use App\Models\Article;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class ArticleController extends Controller
{
    // ... existing methods ...

    public function directPublish(Request $request)
    {
        // Check if user has permission
        if (!Auth::user()->hasRole(['admin', 'editor'])) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'abstract' => 'required|string',
            'keywords' => 'required|string',
            'authors' => 'required|array',
            'authors.*.name' => 'required|string',
            'authors.*.email' => 'required|email',
            'authors.*.affiliation' => 'required|string',
            'volume' => 'required|string',
            'issue' => 'required|string',
            'doi' => 'required|string|unique:articles,doi',
            'publishedDate' => 'required|date',
            'manuscript' => 'required|file|mimes:pdf|max:10240'
        ]);

        try {
            // Handle file upload
            $file = $request->file('manuscript');
            $filename = Str::slug($request->title) . '-' . time() . '.pdf';
            $path = $file->storeAs('articles', $filename, 'public');

            // Create article
            $article = Article::create([
                'title' => $request->title,
                'abstract' => $request->abstract,
                'keywords' => explode(',', $request->keywords),
                'volume' => $request->volume,
                'issue' => $request->issue,
                'doi' => $request->doi,
                'published_date' => $request->publishedDate,
                'status' => 'published',
                'manuscript_path' => $path,
                'published_by' => Auth::id(),
                'is_direct_published' => true
            ]);

            // Handle authors
            foreach ($request->authors as $authorData) {
                // Check if author exists
                $author = User::where('email', $authorData['email'])->first();

                if (!$author) {
                    // Create new author if doesn't exist
                    $author = User::create([
                        'name' => $authorData['name'],
                        'email' => $authorData['email'],
                        'affiliation' => $authorData['affiliation'],
                        'role' => 'author'
                    ]);
                }

                // Attach author to article
                $article->authors()->attach($author->id);
            }

            return response()->json([
                'message' => 'Article published successfully',
                'article' => $article->load('authors')
            ], 201);

        } catch (\Exception $e) {
            // Clean up file if article creation fails
            if (isset($path)) {
                Storage::disk('public')->delete($path);
            }

            return response()->json([
                'message' => 'Failed to publish article',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
