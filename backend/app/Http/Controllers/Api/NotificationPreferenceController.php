<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\UserNotificationPreference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class NotificationPreferenceController extends Controller
{
    /**
     * Get the authenticated user's notification preferences.
     */
    public function show()
    {
        $preferences = Auth::user()->notificationPreferences;

        if (!$preferences) {
            $preferences = Auth::user()->notificationPreferences()->create([
                'login_alerts' => true,
                'password_changes' => true,
                'email_changes' => true,
                'new_devices' => true,
            ]);
        }

        return response()->json($preferences);
    }

    /**
     * Update the authenticated user's notification preferences.
     */
    public function update(Request $request)
    {
        $validated = $request->validate([
            'login_alerts' => 'required|boolean',
            'password_changes' => 'required|boolean',
            'email_changes' => 'required|boolean',
            'new_devices' => 'required|boolean',
        ]);

        try {
            DB::beginTransaction();

            $preferences = Auth::user()->notificationPreferences;

            if (!$preferences) {
                $preferences = Auth::user()->notificationPreferences()->create($validated);
            } else {
                $preferences->update($validated);
            }

            DB::commit();

            // Refresh the model to ensure we have the latest data
            $preferences->refresh();

            return response()->json($preferences);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Failed to update preferences'], 500);
        }
    }
}
