<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\UserDevice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserDeviceController extends Controller
{
    /**
     * List all devices for the authenticated user.
     */
    public function index()
    {
        $devices = Auth::user()->devices()->orderByDesc('last_used_at')->get();
        return response()->json($devices);
    }

    /**
     * Trust a device.
     */
    public function trust(UserDevice $device)
    {
        if ($device->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }
        $device->is_trusted = true;
        $device->save();
        return response()->json(['message' => 'Device trusted successfully']);
    }

    /**
     * Untrust a device.
     */
    public function untrust(UserDevice $device)
    {
        if ($device->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }
        $device->is_trusted = false;
        $device->save();
        return response()->json(['message' => 'Device untrusted successfully']);
    }

    /**
     * Delete a device.
     */
    public function destroy(UserDevice $device)
    {
        if ($device->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }
        $device->delete();
        return response()->json(['message' => 'Device deleted successfully']);
    }
}
