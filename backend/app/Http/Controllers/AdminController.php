<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\Manuscript;
use App\Models\Review;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use OpenApi\Annotations as OA;

/**
 * @OA\Tag(
 *     name="Admin",
 *     description="Admin management endpoints"
 * )
 */
class AdminController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/admin/payments",
     *     summary="Get all payments for admin",
     *     tags={"Admin"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Payments retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="array", @OA\Items(
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="manuscript_id", type="integer"),
     *                 @OA\Property(property="amount", type="number"),
     *                 @OA\Property(property="currency", type="string"),
     *                 @OA\Property(property="status", type="string"),
     *                 @OA\Property(property="payment_method", type="string"),
     *                 @OA\Property(property="created_at", type="string"),
     *                 @OA\Property(property="manuscript", type="object"),
     *                 @OA\Property(property="user", type="object")
     *             ))
     *         )
     *     )
     * )
     */
    public function getPayments(): JsonResponse
    {
        try {
            $payments = Payment::with(['manuscript.user'])
                ->orderBy('created_at', 'desc')
                ->get();

            $formattedPayments = $payments->map(function ($payment) {
                return [
                    'id' => $payment->id,
                    'manuscript_id' => $payment->manuscript_id,
                    'manuscript_title' => $payment->manuscript->title ?? 'N/A',
                    'author_name' => $payment->manuscript->user->name ?? 'N/A',
                    'author_email' => $payment->manuscript->user->email ?? 'N/A',
                    'amount' => $payment->amount,
                    'currency' => $payment->currency,
                    'status' => $payment->status,
                    'payment_method' => $payment->payment_method ?? 'N/A',
                    'transaction_id' => $payment->transaction_id,
                    'created_at' => $payment->created_at,
                    'updated_at' => $payment->updated_at
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $formattedPayments
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payments',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/admin/payment-stats",
     *     summary="Get payment statistics for admin",
     *     tags={"Admin"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Payment statistics retrieved successfully"
     *     )
     * )
     */
    public function getPaymentStats(): JsonResponse
    {
        try {
            $stats = [
                'total_payments' => Payment::count(),
                'total_revenue' => Payment::where('status', 'paid')->sum('amount'),
                'pending_payments' => Payment::where('status', 'pending')->count(),
                'failed_payments' => Payment::where('status', 'failed')->count(),
                'paid_payments' => Payment::where('status', 'paid')->count(),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/admin/manuscripts",
     *     summary="Get all manuscripts for admin",
     *     tags={"Admin"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Manuscripts retrieved successfully"
     *     )
     * )
     */
    public function getManuscripts(): JsonResponse
    {
        try {
            $manuscripts = Manuscript::with(['author', 'reviews'])
                ->orderBy('created_at', 'desc')
                ->get();

            $formattedManuscripts = $manuscripts->map(function ($manuscript) {
                return [
                    'id' => $manuscript->id,
                    'submission_id' => $manuscript->submission_id,
                    'title' => $manuscript->title,
                    'author' => $manuscript->author->name ?? 'N/A',
                    'author_email' => $manuscript->author->email ?? 'N/A',
                    'status' => $manuscript->status,
                    'submitted_at' => $manuscript->created_at,
                    'updated_at' => $manuscript->updated_at,
                    'reviewers_count' => $manuscript->reviews->count(),
                    'payment_status' => $manuscript->payment_status
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $formattedManuscripts
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve manuscripts',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/admin/manuscript-stats",
     *     summary="Get manuscript statistics for admin",
     *     tags={"Admin"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Manuscript statistics retrieved successfully"
     *     )
     * )
     */
    public function getManuscriptStats(): JsonResponse
    {
        try {
            $stats = [
                'total' => Manuscript::count(),
                'submitted' => Manuscript::where('status', 'submitted')->count(),
                'under_review' => Manuscript::where('status', 'under_review')->count(),
                'accepted' => Manuscript::where('status', 'accepted')->count(),
                'published' => Manuscript::where('status', 'published')->count(),
                'rejected' => Manuscript::where('status', 'rejected')->count(),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve manuscript statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/admin/reviews",
     *     summary="Get all reviews for admin",
     *     tags={"Admin"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Reviews retrieved successfully"
     *     )
     * )
     */
    public function getReviews(): JsonResponse
    {
        try {
            $reviews = Review::with(['manuscript', 'reviewer'])
                ->orderBy('created_at', 'desc')
                ->get();

            $formattedReviews = $reviews->map(function ($review) {
                // Determine status based on submitted_at and recommendation
                $status = 'pending';
                if ($review->submitted_at) {
                    $status = 'completed';
                } elseif ($review->recommendation) {
                    $status = 'in_progress';
                }

                return [
                    'id' => $review->id,
                    'manuscript_id' => $review->manuscript_id,
                    'manuscript_title' => $review->manuscript->title ?? 'N/A',
                    'reviewer_name' => $review->reviewer->name ?? 'N/A',
                    'reviewer_email' => $review->reviewer->email ?? 'N/A',
                    'status' => $status,
                    'assigned_at' => $review->created_at,
                    'due_date' => null, // Not available in current schema
                    'completed_at' => $review->submitted_at,
                    'rating' => null, // Not available in current schema
                    'recommendation' => $review->recommendation
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $formattedReviews
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve reviews',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/admin/review-stats",
     *     summary="Get review statistics for admin",
     *     tags={"Admin"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Review statistics retrieved successfully"
     *     )
     * )
     */
    public function getReviewStats(): JsonResponse
    {
        try {
            $stats = [
                'total' => Review::count(),
                'pending' => Review::whereNull('submitted_at')->whereNull('recommendation')->count(),
                'in_progress' => Review::whereNull('submitted_at')->whereNotNull('recommendation')->count(),
                'completed' => Review::whereNotNull('submitted_at')->count(),
                'overdue' => 0, // Not available without due_date column
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve review statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/admin/manuscripts/{id}",
     *     summary="Delete a manuscript",
     *     tags={"Admin"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Manuscript deleted successfully"
     *     )
     * )
     */
    public function deleteManuscript($id): JsonResponse
    {
        try {
            $manuscript = Manuscript::findOrFail($id);
            $manuscript->delete();

            return response()->json([
                'success' => true,
                'message' => 'Manuscript deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete manuscript',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/admin/reviews/{id}",
     *     summary="Delete a review assignment",
     *     tags={"Admin"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Review assignment deleted successfully"
     *     )
     * )
     */
    public function deleteReview($id): JsonResponse
    {
        try {
            $review = Review::findOrFail($id);
            $review->delete();

            return response()->json([
                'success' => true,
                'message' => 'Review assignment deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete review assignment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/admin/dashboard-stats",
     *     summary="Get dashboard statistics for admin",
     *     tags={"Admin"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Dashboard statistics retrieved successfully"
     *     )
     * )
     */
    public function getDashboardStats(): JsonResponse
    {
        try {
            $stats = [
                'manuscripts' => [
                    'total' => Manuscript::count(),
                    'submitted' => Manuscript::where('status', 'submitted')->count(),
                    'under_review' => Manuscript::where('status', 'under_review')->count(),
                    'accepted' => Manuscript::where('status', 'accepted')->count(),
                    'published' => Manuscript::where('status', 'published')->count(),
                    'rejected' => Manuscript::where('status', 'rejected')->count(),
                ],
                'payments' => [
                    'total' => Payment::count(),
                    'paid' => Payment::where('status', 'paid')->count(),
                    'pending' => Payment::where('status', 'pending')->count(),
                    'failed' => Payment::where('status', 'failed')->count(),
                    'total_revenue' => Payment::where('status', 'paid')->sum('amount'),
                ],
                'reviews' => [
                    'total' => Review::count(),
                    'pending' => Review::whereNull('submitted_at')->whereNull('recommendation')->count(),
                    'in_progress' => Review::whereNull('submitted_at')->whereNotNull('recommendation')->count(),
                    'completed' => Review::whereNotNull('submitted_at')->count(),
                    'overdue' => 0, // Not available without due_date column
                ],
                'users' => [
                    'total' => User::count(),
                    'authors' => User::where('role', 'author')->count(),
                    'reviewers' => User::where('role', 'reviewer')->count(),
                    'editors' => User::where('role', 'editor')->count(),
                    'admins' => User::where('role', 'admin')->count(),
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve dashboard statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Put(
     *     path="/api/v1/admin/manuscripts/{id}/status",
     *     summary="Update manuscript status",
     *     tags={"Admin"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="string", enum={"submitted", "under_review", "accepted", "published", "rejected"})
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Manuscript status updated successfully"
     *     )
     * )
     */
    public function updateManuscriptStatus($id, Request $request): JsonResponse
    {
        try {
            $request->validate([
                'status' => 'required|string|in:submitted,under_review,accepted,published,rejected'
            ]);

            $manuscript = Manuscript::findOrFail($id);
            $manuscript->status = $request->status;
            $manuscript->save();

            return response()->json([
                'success' => true,
                'message' => 'Manuscript status updated successfully',
                'data' => $manuscript
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update manuscript status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/admin/logs",
     *     summary="Get system logs for admin",
     *     tags={"Admin"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="level",
     *         in="query",
     *         description="Log level filter",
     *         @OA\Schema(type="string", enum={"debug", "info", "warning", "error", "critical"})
     *     ),
     *     @OA\Parameter(
     *         name="module",
     *         in="query",
     *         description="Module filter",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="from",
     *         in="query",
     *         description="Start date (Y-m-d H:i:s)",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="to",
     *         in="query",
     *         description="End date (Y-m-d H:i:s)",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="limit",
     *         in="query",
     *         description="Number of logs to return",
     *         @OA\Schema(type="integer", default=100)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="System logs retrieved successfully"
     *     )
     * )
     */
    public function getSystemLogs(Request $request): JsonResponse
    {
        try {
            $level = $request->get('level');
            $module = $request->get('module');
            $from = $request->get('from');
            $to = $request->get('to');
            $limit = $request->get('limit', 100);
            $search = $request->get('search');

            // Get log file path
            $logPath = storage_path('logs/laravel.log');

            if (!file_exists($logPath)) {
                return response()->json([
                    'success' => true,
                    'data' => [],
                    'message' => 'No log file found'
                ]);
            }

            // Read and parse log file
            $logs = $this->parseLogFile($logPath, [
                'level' => $level,
                'module' => $module,
                'from' => $from,
                'to' => $to,
                'search' => $search,
                'limit' => $limit
            ]);

            return response()->json([
                'success' => true,
                'data' => $logs,
                'total' => count($logs)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve system logs',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/admin/analytics",
     *     summary="Get system analytics for admin",
     *     tags={"Admin"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="period",
     *         in="query",
     *         description="Analytics period",
     *         @OA\Schema(type="string", enum={"7d", "30d", "90d", "12m"}, default="30d")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="System analytics retrieved successfully"
     *     )
     * )
     */
    public function getSystemAnalytics(Request $request): JsonResponse
    {
        try {
            $period = $request->get('period', '30d');

            // Calculate date range based on period
            $endDate = now();
            $startDate = match($period) {
                '7d' => $endDate->copy()->subDays(7),
                '30d' => $endDate->copy()->subDays(30),
                '90d' => $endDate->copy()->subDays(90),
                '12m' => $endDate->copy()->subMonths(12),
                default => $endDate->copy()->subDays(30)
            };

            $analytics = [
                'period' => $period,
                'date_range' => [
                    'start' => $startDate->toDateString(),
                    'end' => $endDate->toDateString()
                ],
                'users' => $this->getUserAnalytics($startDate, $endDate),
                'manuscripts' => $this->getManuscriptAnalytics($startDate, $endDate),
                'reviews' => $this->getReviewAnalytics($startDate, $endDate),
                'payments' => $this->getPaymentAnalytics($startDate, $endDate),
                'system' => $this->getSystemMetrics($startDate, $endDate),
                'traffic' => $this->getTrafficAnalytics($startDate, $endDate)
            ];

            return response()->json([
                'success' => true,
                'data' => $analytics
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve system analytics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/admin/logs/clear",
     *     summary="Clear system logs",
     *     tags={"Admin"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=false,
     *         @OA\JsonContent(
     *             @OA\Property(property="older_than", type="string", description="Clear logs older than (e.g., '7d', '30d')")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Logs cleared successfully"
     *     )
     * )
     */
    public function clearLogs(Request $request): JsonResponse
    {
        try {
            $olderThan = $request->get('older_than');
            $logPath = storage_path('logs/laravel.log');

            if (!file_exists($logPath)) {
                return response()->json([
                    'success' => true,
                    'message' => 'No log file to clear'
                ]);
            }

            if ($olderThan) {
                // Archive old logs instead of deleting everything
                $archivePath = storage_path('logs/archived_' . date('Y-m-d_H-i-s') . '.log');
                copy($logPath, $archivePath);
                file_put_contents($logPath, '');
            } else {
                // Clear all logs
                file_put_contents($logPath, '');
            }

            return response()->json([
                'success' => true,
                'message' => 'Logs cleared successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear logs',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/admin/logs/export",
     *     summary="Export system logs",
     *     tags={"Admin"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="format",
     *         in="query",
     *         description="Export format",
     *         @OA\Schema(type="string", enum={"csv", "json", "txt"}, default="csv")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Logs exported successfully"
     *     )
     * )
     */
    public function exportLogs(Request $request)
    {
        try {
            $format = $request->get('format', 'csv');
            $logPath = storage_path('logs/laravel.log');

            if (!file_exists($logPath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No log file found'
                ], 404);
            }

            $logs = $this->parseLogFile($logPath);
            $filename = 'system_logs_' . date('Y-m-d_H-i-s') . '.' . $format;

            switch ($format) {
                case 'json':
                    $content = json_encode($logs, JSON_PRETTY_PRINT);
                    $mimeType = 'application/json';
                    break;
                case 'csv':
                    $content = $this->convertLogsToCsv($logs);
                    $mimeType = 'text/csv';
                    break;
                default:
                    $content = file_get_contents($logPath);
                    $mimeType = 'text/plain';
            }

            return response($content)
                ->header('Content-Type', $mimeType)
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export logs',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    // Helper methods for log parsing and analytics

    private function parseLogFile($logPath, $filters = [])
    {
        $logs = [];
        $handle = fopen($logPath, 'r');

        if (!$handle) {
            return $logs;
        }

        $lineNumber = 0;
        while (($line = fgets($handle)) !== false) {
            $lineNumber++;

            // Parse Laravel log format: [timestamp] environment.level: message
            if (preg_match('/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\] \w+\.(\w+): (.+)/', $line, $matches)) {
                $timestamp = $matches[1];
                $level = $matches[2];
                $message = trim($matches[3]);

                // Extract module from message if possible
                $module = $this->extractModuleFromMessage($message);

                $log = [
                    'id' => $lineNumber,
                    'timestamp' => $timestamp,
                    'level' => $level,
                    'module' => $module,
                    'message' => $message,
                    'raw' => trim($line)
                ];

                // Apply filters
                if ($this->passesFilters($log, $filters)) {
                    $logs[] = $log;
                }
            }
        }

        fclose($handle);

        // Sort by timestamp descending and limit
        usort($logs, function($a, $b) {
            return strtotime($b['timestamp']) - strtotime($a['timestamp']);
        });

        if (isset($filters['limit'])) {
            $logs = array_slice($logs, 0, $filters['limit']);
        }

        return $logs;
    }

    private function extractModuleFromMessage($message)
    {
        // Try to extract module from common patterns
        if (strpos($message, 'App\\Http\\Controllers\\') !== false) {
            preg_match('/App\\\\Http\\\\Controllers\\\\(\w+)Controller/', $message, $matches);
            return isset($matches[1]) ? $matches[1] : 'Controller';
        }

        if (strpos($message, 'authentication') !== false || strpos($message, 'login') !== false) {
            return 'Authentication';
        }

        if (strpos($message, 'database') !== false || strpos($message, 'query') !== false) {
            return 'Database';
        }

        if (strpos($message, 'email') !== false || strpos($message, 'mail') !== false) {
            return 'Email';
        }

        if (strpos($message, 'api') !== false || strpos($message, 'request') !== false) {
            return 'API';
        }

        return 'System';
    }

    private function passesFilters($log, $filters)
    {
        if (isset($filters['level']) && $log['level'] !== $filters['level']) {
            return false;
        }

        if (isset($filters['module']) && stripos($log['module'], $filters['module']) === false) {
            return false;
        }

        if (isset($filters['search']) && stripos($log['message'], $filters['search']) === false) {
            return false;
        }

        if (isset($filters['from']) && strtotime($log['timestamp']) < strtotime($filters['from'])) {
            return false;
        }

        if (isset($filters['to']) && strtotime($log['timestamp']) > strtotime($filters['to'])) {
            return false;
        }

        return true;
    }

    private function convertLogsToCsv($logs)
    {
        $csv = "Timestamp,Level,Module,Message\n";
        foreach ($logs as $log) {
            $csv .= sprintf('"%s","%s","%s","%s"' . "\n",
                $log['timestamp'],
                $log['level'],
                $log['module'],
                str_replace('"', '""', $log['message'])
            );
        }
        return $csv;
    }

    private function getUserAnalytics($startDate, $endDate)
    {
        return [
            'total' => User::count(),
            'new_registrations' => User::whereBetween('created_at', [$startDate, $endDate])->count(),
            'active_users' => User::whereBetween('last_login_at', [$startDate, $endDate])->count(),
            'by_role' => User::selectRaw('role, COUNT(*) as count')
                ->groupBy('role')
                ->pluck('count', 'role')
                ->toArray(),
            'growth_chart' => $this->getGrowthChart(User::class, $startDate, $endDate)
        ];
    }

    private function getManuscriptAnalytics($startDate, $endDate)
    {
        return [
            'total' => Manuscript::count(),
            'new_submissions' => Manuscript::whereBetween('created_at', [$startDate, $endDate])->count(),
            'by_status' => Manuscript::selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray(),
            'growth_chart' => $this->getGrowthChart(Manuscript::class, $startDate, $endDate),
            'avg_processing_time' => $this->getAverageProcessingTime($startDate, $endDate)
        ];
    }

    private function getReviewAnalytics($startDate, $endDate)
    {
        return [
            'total' => Review::count(),
            'completed' => Review::whereNotNull('submitted_at')
                ->whereBetween('submitted_at', [$startDate, $endDate])
                ->count(),
            'pending' => Review::whereNull('submitted_at')->count(),
            'avg_review_time' => $this->getAverageReviewTime($startDate, $endDate)
        ];
    }

    private function getPaymentAnalytics($startDate, $endDate)
    {
        return [
            'total_revenue' => Payment::where('status', 'paid')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->sum('amount'),
            'total_payments' => Payment::whereBetween('created_at', [$startDate, $endDate])->count(),
            'by_status' => Payment::selectRaw('status, COUNT(*) as count')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray(),
            'revenue_chart' => $this->getRevenueChart($startDate, $endDate)
        ];
    }

    private function getSystemMetrics($startDate, $endDate)
    {
        $logPath = storage_path('logs/laravel.log');
        $errorCount = 0;
        $warningCount = 0;

        if (file_exists($logPath)) {
            $logs = $this->parseLogFile($logPath, [
                'from' => $startDate->toDateTimeString(),
                'to' => $endDate->toDateTimeString()
            ]);

            foreach ($logs as $log) {
                if ($log['level'] === 'error') $errorCount++;
                if ($log['level'] === 'warning') $warningCount++;
            }
        }

        return [
            'uptime' => '99.9%', // This would come from monitoring service
            'error_count' => $errorCount,
            'warning_count' => $warningCount,
            'storage_used' => $this->getStorageUsage(),
            'memory_usage' => $this->getMemoryUsage()
        ];
    }

    private function getTrafficAnalytics($startDate, $endDate)
    {
        // This would typically come from web analytics service
        // For now, return mock data based on user activity
        return [
            'page_views' => User::whereBetween('last_login_at', [$startDate, $endDate])->count() * 15,
            'unique_visitors' => User::whereBetween('last_login_at', [$startDate, $endDate])->count(),
            'bounce_rate' => '35%',
            'avg_session_duration' => '8m 32s',
            'top_pages' => [
                ['page' => '/dashboard', 'views' => 1250],
                ['page' => '/manuscripts', 'views' => 890],
                ['page' => '/submit', 'views' => 650],
                ['page' => '/reviews', 'views' => 420]
            ]
        ];
    }

    private function getGrowthChart($model, $startDate, $endDate)
    {
        $data = [];
        $current = $startDate->copy();

        while ($current <= $endDate) {
            $nextPeriod = $current->copy()->addDay();
            $count = $model::whereBetween('created_at', [$current, $nextPeriod])->count();

            $data[] = [
                'date' => $current->toDateString(),
                'count' => $count
            ];

            $current = $nextPeriod;
        }

        return $data;
    }

    private function getRevenueChart($startDate, $endDate)
    {
        $data = [];
        $current = $startDate->copy();

        while ($current <= $endDate) {
            $nextPeriod = $current->copy()->addDay();
            $revenue = Payment::where('status', 'paid')
                ->whereBetween('created_at', [$current, $nextPeriod])
                ->sum('amount');

            $data[] = [
                'date' => $current->toDateString(),
                'revenue' => $revenue
            ];

            $current = $nextPeriod;
        }

        return $data;
    }

    private function getAverageProcessingTime($startDate, $endDate)
    {
        // Calculate average time from submission to acceptance/rejection
        $manuscripts = Manuscript::whereBetween('created_at', [$startDate, $endDate])
            ->whereIn('status', ['accepted', 'rejected', 'published'])
            ->get();

        if ($manuscripts->isEmpty()) {
            return 0;
        }

        $totalDays = 0;
        foreach ($manuscripts as $manuscript) {
            $totalDays += $manuscript->created_at->diffInDays($manuscript->updated_at);
        }

        return round($totalDays / $manuscripts->count(), 1);
    }

    private function getAverageReviewTime($startDate, $endDate)
    {
        $reviews = Review::whereNotNull('submitted_at')
            ->whereBetween('submitted_at', [$startDate, $endDate])
            ->get();

        if ($reviews->isEmpty()) {
            return 0;
        }

        $totalDays = 0;
        foreach ($reviews as $review) {
            $totalDays += $review->created_at->diffInDays($review->submitted_at);
        }

        return round($totalDays / $reviews->count(), 1);
    }

    private function getStorageUsage()
    {
        $manuscriptsPath = storage_path('app/manuscripts');
        $size = 0;

        if (is_dir($manuscriptsPath)) {
            $iterator = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($manuscriptsPath)
            );

            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $size += $file->getSize();
                }
            }
        }

        return [
            'used_bytes' => $size,
            'used_mb' => round($size / 1024 / 1024, 2),
            'used_gb' => round($size / 1024 / 1024 / 1024, 2)
        ];
    }

    private function getMemoryUsage()
    {
        return [
            'current' => memory_get_usage(true),
            'peak' => memory_get_peak_usage(true),
            'limit' => ini_get('memory_limit')
        ];
    }
}
