<?php

namespace App\Http\Controllers;

use App\Http\Resources\ReviewResource;
use App\Models\Manuscript;
use App\Models\Review;
use App\Models\User;
use Illuminate\Http\Request;
use App\Notifications\ReviewerAssigned;
use Illuminate\Support\Facades\Auth;
use App\Notifications\ReviewSubmitted;

class ReviewController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/manuscripts/{manuscript_id}/reviews",
     *     summary="List reviews for a manuscript",
     *     tags={"Reviews"},
     *     @OA\Parameter(
     *         name="manuscript_id",
     *         in="path",
     *         description="Manuscript ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="List of reviews for the manuscript"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function index(Manuscript $manuscript)
    {
        $query = $manuscript->reviews()
            ->with('reviewer')
            ->when(
                Auth::user()->role === 'reviewer',
                fn($q) => $q->where('reviewer_id', Auth::id())
            );

        return ReviewResource::collection($query->get());
    }

    /**
     * @OA\Post(
     *     path="/api/v1/manuscripts/{manuscript_id}/reviews",
     *     summary="Submit a review for a manuscript",
     *     tags={"Reviews"},
     *     @OA\Parameter(
     *         name="manuscript_id",
     *         in="path",
     *         description="Manuscript ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 required={"recommendation", "feedback"},
     *                 @OA\Property(property="recommendation", type="string", enum={"accept", "minor", "major", "reject"}),
     *                 @OA\Property(property="feedback", type="string"),
     *                 @OA\Property(property="confidential_comments", type="string", nullable=true)
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="Review submitted successfully"),
     *     @OA\Response(response=422, description="Review already submitted or validation errors"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function store(Request $request, Manuscript $manuscript)
    {
        $request->validate([
            'recommendation' => ['required', 'in:accept,minor,major,reject'],
            'feedback' => ['required', 'string'],
            'confidential_comments' => ['nullable', 'string'],
        ]);

        // Check if reviewer is assigned to this manuscript
        if (!$manuscript->reviewers()->where('reviewer_id', Auth::id())->exists()) {
            return response()->json([
                'message' => 'You are not assigned to review this manuscript.'
            ], 403);
        }

        // Create or update the review
        $review = Review::updateOrCreate(
            [
                'manuscript_id' => $manuscript->id,
                'reviewer_id' => Auth::id(),
            ],
            [
                'recommendation' => $request->recommendation,
                'feedback' => $request->feedback,
                'confidential_comments' => $request->confidential_comments,
                'submitted_at' => now(),
                'updated_at' => now(),
            ]
        );

        // Notify the editor that a review has been submitted
        if ($manuscript->editor) {
            $manuscript->editor->notify(new ReviewSubmitted($manuscript, $review));
        }

        $this->updateManuscriptStatus($manuscript);

        return new ReviewResource($review->load('reviewer'));
    }

    /**
     * @OA\Get(
     *     path="/api/v1/manuscripts/{manuscript_id}/reviews/{id}",
     *     summary="Get a specific review for a manuscript",
     *     tags={"Reviews"},
     *     @OA\Parameter(
     *         name="manuscript_id",
     *         in="path",
     *         description="Manuscript ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Review ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="Review details"),
     *     @OA\Response(response=404, description="Review not found"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function show(Manuscript $manuscript, Review $review)
    {
        return new ReviewResource($review->load('reviewer'));
    }

    /**
     * @OA\Post(
     *     path="/api/v1/manuscripts/{manuscript}/reviewers",
     *     summary="Assign reviewers to a manuscript",
     *     tags={"Reviews"},
     *     @OA\Parameter(
     *         name="manuscript",
     *         in="path",
     *         description="Manuscript ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"reviewer_ids"},
     *             @OA\Property(
     *                 property="reviewer_ids",
     *                 type="array",
     *                 @OA\Items(type="integer"),
     *                 description="Array of reviewer user IDs"
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="Reviewers assigned successfully"),
     *     @OA\Response(response=422, description="Validation errors"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function assignReviewers(Request $request, Manuscript $manuscript)
    {
        $request->validate([
            'reviewer_ids' => ['required', 'array', 'min:1'],
            'reviewer_ids.*' => ['required', 'integer', 'exists:users,id']
        ]);

        // Delete existing assignments
        $manuscript->reviewers()->detach();

        // Create new assignments
        $assignments = collect($request->reviewer_ids)->mapWithKeys(function ($reviewerId) {
            return [$reviewerId => ['assigned_at' => now()]];
        });

        $manuscript->reviewers()->attach($assignments);

        // Update manuscript status to under_review
        $manuscript->update(['status' => 'under_review']);

        // Send notifications to assigned reviewers
        $reviewers = User::whereIn('id', $request->reviewer_ids)->get();
        foreach ($reviewers as $reviewer) {
            $reviewer->notify(new ReviewerAssigned($manuscript));
        }

        return response()->json([
            'message' => 'Reviewers assigned successfully',
            'data' => $manuscript->load('reviewers')
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/manuscripts/{manuscript_id}/reviews/{id}/complete",
     *     summary="Mark a review as completed",
     *     tags={"Reviews"},
     *     @OA\Parameter(
     *         name="manuscript_id",
     *         in="path",
     *         description="Manuscript ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Review ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="Review marked as completed"),
     *     @OA\Response(response=403, description="Not authorized to complete this review"),
     *     @OA\Response(response=422, description="Review not submitted yet"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function markAsCompleted(Request $request, Manuscript $manuscript, Review $review)
    {
        // Check if the review belongs to the current user
        if ($review->reviewer_id !== Auth::id()) {
            return response()->json([
                'message' => 'You are not authorized to complete this review.'
            ], 403);
        }

        // Check if review is submitted
        if (!$review->isSubmitted()) {
            return response()->json([
                'message' => 'Review must be submitted before it can be marked as completed.'
            ], 422);
        }

        // Check if already completed
        if ($review->isCompleted()) {
            return response()->json([
                'message' => 'Review is already marked as completed.'
            ], 422);
        }

        $review->update([
            'completed_at' => now(),
        ]);

        $this->updateManuscriptStatus($manuscript);

        return new ReviewResource($review->load('reviewer'));
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/manuscripts/{manuscript_id}/reviews/{id}/complete",
     *     summary="Unmark a review as completed (mark as in progress)",
     *     tags={"Reviews"},
     *     @OA\Parameter(
     *         name="manuscript_id",
     *         in="path",
     *         description="Manuscript ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Review ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="Review marked as in progress"),
     *     @OA\Response(response=403, description="Not authorized to modify this review"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function markAsInProgress(Request $request, Manuscript $manuscript, Review $review)
    {
        // Check if the review belongs to the current user
        if ($review->reviewer_id !== Auth::id()) {
            return response()->json([
                'message' => 'You are not authorized to modify this review.'
            ], 403);
        }

        $review->update([
            'completed_at' => null,
        ]);

        $this->updateManuscriptStatus($manuscript);

        return new ReviewResource($review->load('reviewer'));
    }

    /**
     * @OA\Internal(
     *     summary="Update manuscript status based on reviews",
     *     tags={"Reviews"},
     *     description="This method is used to update the manuscript's status based on the completed reviews."
     * )
     */
    protected function updateManuscriptStatus(Manuscript $manuscript)
    {
        // Only consider completed reviews for manuscript status updates
        $completedReviews = $manuscript->reviews()->completed()->get();
        $totalAssignedReviewers = $manuscript->reviewers()->count();

        // If we don't have at least 2 completed reviews, don't change status
        if ($completedReviews->count() < 2) {
            return;
        }

        $recommendations = $completedReviews->pluck('recommendation');

        if ($recommendations->every(fn($r) => $r === 'accept')) {
            $manuscript->update(['status' => 'payment_pending']);
        } elseif ($recommendations->contains('reject')) {
            $manuscript->update(['status' => 'rejected']);
        } else {
            $manuscript->update(['status' => 'revisions_required']);
        }
    }

    /**
     * @OA\Put(
     *     path="/api/v1/manuscripts/{manuscript_id}/reviews/{id}",
     *     summary="Update an existing review for a manuscript",
     *     tags={"Reviews"},
     *     @OA\Parameter(
     *         name="manuscript_id",
     *         in="path",
     *         description="Manuscript ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Review ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 required={"recommendation", "feedback"},
     *                 @OA\Property(property="recommendation", type="string", enum={"accept", "minor", "major", "reject"}),
     *                 @OA\Property(property="feedback", type="string"),
     *                 @OA\Property(property="confidential_comments", type="string", nullable=true)
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="Review updated successfully"),
     *     @OA\Response(response=403, description="Not authorized to update this review"),
     *     @OA\Response(response=422, description="Validation errors"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function update(Request $request, Manuscript $manuscript, Review $review)
    {
        // Check if the review belongs to the current user
        if ($review->reviewer_id !== Auth::id()) {
            return response()->json([
                'message' => 'You are not authorized to update this review.'
            ], 403);
        }

        $request->validate([
            'recommendation' => ['required', 'in:accept,minor,major,reject'],
            'feedback' => ['required', 'string'],
            'confidential_comments' => ['nullable', 'string'],
        ]);

        $review->update([
            'recommendation' => $request->recommendation,
            'feedback' => $request->feedback,
            'confidential_comments' => $request->confidential_comments,
            'submitted_at' => now(),
        ]);

        $this->updateManuscriptStatus($manuscript);

        return new ReviewResource($review->load('reviewer'));
    }
}
