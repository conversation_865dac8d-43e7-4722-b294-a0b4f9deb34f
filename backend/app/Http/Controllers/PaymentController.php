<?php

namespace App\Http\Controllers;

use App\Http\Resources\PaymentResource;
use App\Models\Manuscript;
use App\Models\Payment;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    public function store(Request $request, Manuscript $manuscript)
    {
        // Validate the payment data
        $validatedData = $request->validate([
            'amount' => 'required|numeric|min:0',
            'currency' => 'required|string|max:3',
            'payment_method' => 'required|string|max:50',
            'payment_date' => 'required|date',
            'transaction_id' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:500'
        ]);

        // Check if payment is appropriate - allow for accepted manuscripts or payment_pending
        if (!in_array($manuscript->status, ['payment_pending', 'accepted', 'review_completed'])) {
            return response()->json([
                'message' => 'Payment recording is not available for manuscripts with status: ' . $manuscript->status
            ], 422);
        }

        // Check if payment already exists
        if ($manuscript->payment) {
            return response()->json([
                'message' => 'Payment has already been recorded for this manuscript'
            ], 422);
        }

        // Create the payment record
        $payment = $manuscript->payment()->create([
            'amount' => $validatedData['amount'],
            'currency' => $validatedData['currency'],
            'payment_method' => $validatedData['payment_method'],
            'payment_date' => $validatedData['payment_date'],
            'transaction_id' => $validatedData['transaction_id'],
            'notes' => $validatedData['notes'],
            'status' => 'paid',
            'paid_at' => now(),
            'gateway_response' => ['recorded_by_editor' => true],
        ]);

        // Update manuscript status
        $manuscript->update([
            'payment_status' => 'paid'
        ]);

        return new PaymentResource($payment);
    }

    public function show(Manuscript $manuscript)
    {
        if (!$manuscript->payment) {
            return response()->json([
                'message' => 'No payment found for this manuscript'
            ], 404);
        }

        return new PaymentResource($manuscript->payment);
    }

    public function update(Request $request, Manuscript $manuscript)
    {
        // Validate the payment data
        $validatedData = $request->validate([
            'amount' => 'required|numeric|min:0',
            'currency' => 'required|string|max:3',
            'payment_method' => 'required|string|max:50',
            'payment_date' => 'required|date',
            'transaction_id' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:500'
        ]);

        if (!$manuscript->payment) {
            return response()->json([
                'message' => 'No payment found for this manuscript'
            ], 404);
        }

        // Update the payment record
        $manuscript->payment->update([
            'amount' => $validatedData['amount'],
            'currency' => $validatedData['currency'],
            'payment_method' => $validatedData['payment_method'],
            'payment_date' => $validatedData['payment_date'],
            'transaction_id' => $validatedData['transaction_id'],
            'notes' => $validatedData['notes'],
            'gateway_response' => array_merge(
                $manuscript->payment->gateway_response ?? [],
                ['last_updated_by_editor' => true, 'updated_at' => now()]
            ),
        ]);

        return new PaymentResource($manuscript->payment->fresh());
    }

    public function destroy(Manuscript $manuscript)
    {
        if (!$manuscript->payment) {
            return response()->json([
                'message' => 'No payment found for this manuscript'
            ], 404);
        }

        // Store payment info for response
        $paymentInfo = [
            'manuscript_id' => $manuscript->id,
            'submission_id' => $manuscript->submission_id,
            'amount' => $manuscript->payment->amount,
            'currency' => $manuscript->payment->currency
        ];

        // Delete the payment
        $manuscript->payment->delete();

        // Update manuscript payment status back to pending
        $manuscript->update([
            'payment_status' => 'pending'
        ]);

        return response()->json([
            'message' => 'Payment deleted successfully',
            'payment_info' => $paymentInfo
        ]);
    }
}
