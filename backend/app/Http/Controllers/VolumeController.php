<?php

namespace App\Http\Controllers;

use App\Http\Resources\VolumeResource;
use App\Models\Volume;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class VolumeController extends ApiController
{
    /**
     * @OA\Get(
     *     path="/api/v1/volumes",
     *     summary="List volumes",
     *     tags={"Volumes"},
     *     @OA\Response(response=200, description="List of volumes"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function index()
    {
        $volumes = Volume::query()
            ->with('issues')
            ->orderBy('year', 'desc')
            ->paginate(10);

        return VolumeResource::collection($volumes);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/volumes",
     *     summary="Create a new volume",
     *     tags={"Volumes"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 required={"number", "year"},
     *                 @OA\Property(property="number", type="integer"),
     *                 @OA\Property(property="year", type="integer", format="int32", description="4-digit year"),
     *             )
     *         )
     *     ),
     *     @OA\Response(response=201, description="Volume created successfully"),
     *     @OA\Response(response=422, description="Validation errors"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'number' => 'required|integer',
            'year' => 'required|integer',
            'description' => 'nullable|string|max:1000',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
        ]);

        if ($request->hasFile('cover_image')) {
            $file = $request->file('cover_image');
            $extension = $file->getClientOriginalExtension();
            $filename = 'volume-' . $validated['number'] . '-' . Str::random(10) . '.' . $extension;

            $path = $file->storeAs('volumes/covers', $filename, 'public');
            $validated['cover_image'] = Storage::url($path);
        }

        $volume = Volume::create($validated);
        return new VolumeResource($volume);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/volumes/{id}",
     *     summary="Get volume details",
     *     tags={"Volumes"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Volume ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="Volume details"),
     *     @OA\Response(response=404, description="Volume not found"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function show(Volume $volume)
    {
        return new VolumeResource($volume->load('issues'));
    }

    /**
     * @OA\Put(
     *     path="/api/v1/volumes/{id}",
     *     summary="Update volume details",
     *     tags={"Volumes"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Volume ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 @OA\Property(property="published", type="boolean"),
     *                 @OA\Property(property="publication_date", type="string", format="date"),
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="Volume details updated"),
     *     @OA\Response(response=422, description="Validation errors"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function update(Request $request, Volume $volume)
    {
        $validated = $request->validate([
            'number' => 'sometimes|required|integer',
            'year' => 'sometimes|required|integer',
            'description' => 'nullable|string|max:1000',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
            'published' => 'sometimes|boolean',
        ]);

        if ($request->hasFile('cover_image')) {
            // Delete old cover image if exists
            if ($volume->cover_image) {
                $oldPath = str_replace('/storage/', '', $volume->cover_image);
                Storage::disk('public')->delete($oldPath);
            }

            $file = $request->file('cover_image');
            $extension = $file->getClientOriginalExtension();
            $filename = 'volume-' . $volume->number . '-' . Str::random(10) . '.' . $extension;

            $path = $file->storeAs('volumes/covers', $filename, 'public');
            $validated['cover_image'] = Storage::url($path);
        }

        $volume->update($validated);
        return new VolumeResource($volume);
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/volumes/{id}",
     *     summary="Delete a volume",
     *     tags={"Volumes"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Volume ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=204, description="Volume deleted successfully"),
     *     @OA\Response(response=404, description="Volume not found"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function destroy(Volume $volume)
    {
        try {
            // Delete cover image if exists
            if ($volume->cover_image) {
                $path = str_replace('/storage/', '', $volume->cover_image);
                Storage::disk('public')->delete($path);
            }

            $volume->delete();
            return $this->success(
                null,
                message: 'Volume deleted successfully',
                status: 204
            );
        } catch (\Exception $e) {
            return $this->error(
                'Failed to delete volume',
                status: 500
            );
        }
    }
}
