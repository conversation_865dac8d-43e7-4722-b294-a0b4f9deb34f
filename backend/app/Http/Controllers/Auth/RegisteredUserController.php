<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\ApiController;
use App\Http\Controllers\Controller;
use App\Http\Requests\RegisteredUserRequest;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;

class RegisteredUserController extends ApiController
{
    /**
     * @OA\Post(
     *     path="/api/register",
     *     summary="Register a new user",
     *     tags={"Authentication"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"name","email","password","role"},
     *             @OA\Property(property="name", type="string", example="John Doe"),
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *             @OA\Property(property="password", type="string", format="password", example="password123"),
     *             @OA\Property(property="role", type="string", example="author"),
     *             @OA\Property(property="affiliation", type="string", example="University of Example"),
     *             @OA\Property(property="country", type="string", example="Cameroon"),
     *             @OA\Property(
     *                 property="expertise",
     *                 type="array",
     *                 @OA\Items(type="string"),
     *                 example={"Computer Science", "Engineering"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="User registered successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="user", type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="John Doe"),
     *                     @OA\Property(property="email", type="string", example="<EMAIL>"),
     *                     @OA\Property(property="role", type="string", example="author"),
     *                     @OA\Property(property="affiliation", type="string", example="University of Example"),
     *                     @OA\Property(property="country", type="string", example="Cameroon"),
     *                     @OA\Property(property="expertise", type="array", @OA\Items(type="string"))
     *                 ),
     *                 @OA\Property(property="token", type="string", example="1|XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX")
     *             ),
     *             @OA\Property(property="message", type="string", example="User registered successfully.")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="errors", type="object")
     *         )
     *     )
     * )
     */

    public function store(RegisteredUserRequest $request)
    {
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'affiliation' => $request->affiliation,
            'country' => $request->country,
            'expertise' => $request->expertise,
        ]);

        $token = $user->createToken('auth_token')->plainTextToken;

        // Send verification email
        $user->sendEmailVerificationNotification();

        return $this->success([
            'user' => $user,
            'token' => $token,
        ], 'User registered successfully. Please check your email to verify your account.', 201);
    }
}
