<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\ApiController;
use App\Services\AccountActivityService;
use Illuminate\Http\Request;
use Illuminate\Auth\Events\Verified;
use App\Models\User;

class AuthController extends ApiController
{
    protected $accountActivityService;

    public function __construct(AccountActivityService $accountActivityService)
    {
        $this->accountActivityService = $accountActivityService;
    }

    /**
     * Verify the user's email address.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyEmail(Request $request)
    {
        $user = User::find($request->id);

        if (!$user) {
            return $this->error('Invalid verification link', 400);
        }

        if (!hash_equals(
            (string) $request->hash,
            sha1($user->getEmailForVerification())
        )) {
            return $this->error('Invalid verification link', 400);
        }

        if ($user->hasVerifiedEmail()) {
            return $this->error('Email already verified', 400);
        }

        if ($user->markEmailAsVerified()) {
            event(new Verified($user));

            // Detect and notify about suspicious activity
            $this->accountActivityService->detectAndNotify($request);

            return $this->success(null, 'Email verified successfully');
        }

        return $this->error('Email verification failed', 500);
    }
}
