<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\ApiController;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\AccountActivityService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LoginController extends ApiController
{
    protected $accountActivityService;

    public function __construct(AccountActivityService $accountActivityService)
    {
        $this->accountActivityService = $accountActivityService;
    }

    /**
     * @OA\Post(
     *     path="/api/v1/login",
     *     summary="Login a user",
     *     tags={"Authentication"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"email","password"},
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *             @OA\Property(property="password", type="string", format="password", example="password123")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="User logged in successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="user", type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="John Doe"),
     *                     @OA\Property(property="email", type="string", example="<EMAIL>"),
     *                     @OA\Property(property="role", type="string", example="author")
     *                 ),
     *                 @OA\Property(property="token", type="string", example="1|XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX")
     *             ),
     *             @OA\Property(property="message", type="string", example="User logged in successfully.")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Invalid credentials",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Invalid credentials")
     *         )
     *     )
     * )
     */

    public function store(Request $request)
    {
        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
        ]);

        if (!Auth::attempt($credentials)) {
            return response()->json([
                'message' => 'Invalid credentials'
            ], 401);
        }

        $user = User::where('email', $request->email)->first();
        $user->tokens()->delete();
        $token = $user->createToken('auth_token', ['*'])->plainTextToken;

        $user->last_login_at = now();
        $user->last_login_ip = request()->ip();
        $user->last_login_user_agent = request()->userAgent();
        $user->last_login_location = request()->ip();
        $user->last_login_country = request()->ip();
        $user->last_login_city = request()->ip();
        $user->last_login_region = request()->ip();
        $user->last_login_latitude = request()->ip();
        $user->last_login_longitude = request()->ip();
        $user->save();

        // Detect and notify about suspicious activity
        $this->accountActivityService->detectAndNotify($request);

        return $this->success([
            'user' => $user,
            'token' => $token,
        ], 'User logged in successfully.');
    }

    /**
     * @OA\Post(
     *     path="/api/v1/auth/refresh",
     *     summary="Refresh authentication token",
     *     tags={"Authentication"},
     *     security={{"sanctum": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Token refreshed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="user", type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="John Doe"),
     *                     @OA\Property(property="email", type="string", example="<EMAIL>"),
     *                     @OA\Property(property="role", type="string", example="author")
     *                 ),
     *                 @OA\Property(property="token", type="string", example="1|XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX")
     *             ),
     *             @OA\Property(property="message", type="string", example="Token refreshed successfully.")
     *         )
     *     )
     * )
     */
    public function refresh(Request $request)
    {
        $user = $request->user();

        // Delete old tokens
        $user->tokens()->delete();

        // Create new token
        $token = $user->createToken('auth_token', ['*'])->plainTextToken;

        return $this->success([
            'user' => $user,
            'token' => $token,
        ], 'Token refreshed successfully.');
    }

    /**
     * @OA\Post(
     *     path="/api/v1/logout",
     *     summary="Logout a user",
     *     tags={"Authentication"},
     *     security={{"sanctum": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Logged out successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Logged out successfully")
     *         )
     *     )
     * )
     */

    public function destroy(Request $request)
    {
        $request->user()->tokens()->delete();

        return response()->json([
            'message' => 'Logged out successfully'
        ]);
    }
}
