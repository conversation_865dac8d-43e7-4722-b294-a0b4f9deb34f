<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\ApiController;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;

class SocialAuthController extends ApiController
{
    /**
     * Redirect the user to the Google authentication page.
     */
    public function redirectToGoogle()
    {
        return Socialite::driver('google')
            ->scopes(['email', 'profile'])
            ->redirect();
    }

    /**
     * Handle Google authentication
     */
    public function handleGoogleCallback(Request $request)
    {
        \Log::info('Google callback request', $request->all());
        try {
            $googleUser = Socialite::driver('google')->user();

            // Check if user exists
            $user = User::where('email', $googleUser->email)->first();

            if (!$user) {
                // Create new user
                $user = User::create([
                    'name' => $googleUser->name,
                    'email' => $googleUser->email,
                    'password' => Hash::make(Str::random(24)),
                    'role' => 'author', // Default role
                    'email_verified_at' => now(), // Google emails are verified
                    'google_id' => $googleUser->id,
                ]);
            } else {
                // Update Google ID if not set
                if (!$user->google_id) {
                    $user->google_id = $googleUser->id;
                    $user->save();
                }
            }

            // Generate token
            $token = $user->createToken('auth_token')->plainTextToken;

            // Redirect to frontend with token and user info
            $frontendUrl = config('app.frontend_url', 'http://localhost:5173');
            return redirect()->away("{$frontendUrl}/auth/callback?token={$token}&name=" . urlencode($user->name) . "&email=" . urlencode($user->email) . "&role=" . urlencode($user->role));
        } catch (\Exception $e) {
            \Log::error('Google OAuth error: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
                'request' => $request->all(),
                'env' => [
                    'GOOGLE_CLIENT_ID' => env('GOOGLE_CLIENT_ID'),
                    'GOOGLE_REDIRECT_URI' => env('GOOGLE_REDIRECT_URI'),
                ],
            ]);
            return redirect()->away(config('app.frontend_url', 'http://localhost:5173') . '/login?error=' . urlencode('Failed to authenticate with Google.'));
        }
    }

    /**
     * Redirect the user to the ORCID authentication page.
     */
    public function redirectToOrcid()
    {
        return Socialite::driver('orcid')->redirect();
    }

    /**
     * Handle ORCID authentication
     */
    public function handleOrcidCallback(Request $request)
    {
        try {
            $orcidUser = Socialite::driver('orcid')->user();

            $user = User::where('email', $orcidUser->email)->first();
            if (!$user) {
                $user = User::create([
                    'name' => $orcidUser->name ?? 'ORCID User',
                    'email' => $orcidUser->email,
                    'password' => Hash::make(Str::random(24)),
                    'role' => 'author',
                    'email_verified_at' => now(),
                    'orcid_id' => $orcidUser->id,
                ]);
            } else {
                if (!$user->orcid_id) {
                    $user->orcid_id = $orcidUser->id;
                    $user->save();
                }
            }

            $token = $user->createToken('auth_token')->plainTextToken;
            $frontendUrl = config('app.frontend_url', 'http://localhost:5173');
            return redirect()->away("{$frontendUrl}/auth/callback?token={$token}&name=" . urlencode($user->name) . "&email=" . urlencode($user->email) . "&role=" . urlencode($user->role));
        } catch (\Exception $e) {
            return redirect()->away(config('app.frontend_url', 'http://localhost:5173') . '/login?error=' . urlencode('Failed to authenticate with ORCID.'));
        }
    }
}
