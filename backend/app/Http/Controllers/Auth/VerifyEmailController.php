<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\ApiController;
use App\Models\User;
use Illuminate\Auth\Events\Verified;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\URL;

class VerifyEmailController extends ApiController
{
    // swagger doc
    /**
     * @OA\Post(
     *     path="/api/verify-email",
     *     summary="Verify Email",
     *     description="Verify the email address of the user",
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="id", type="string", example="1"),
     *             @OA\Property(property="hash", type="string", example="hash")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Email verified successfully"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Invalid verification link"
     *     )
     * )
     */
    public function __invoke(Request $request)
{
    // get the id and hash from the request

    $id = $request->query('id');
    $hash = $request->query('hash');

    if (! $id || ! $hash) {
        return $this->error('Invalid verification link parameters', 400);
    }

    $user = User::find($id);


    if (! $user) {
        return $this->error('User not found', 404);
    }

    if (! hash_equals(sha1($user->getEmailForVerification()), $hash)) {
        return $this->error('Invalid hash in verification link', 400);
    }

    if ($user->hasVerifiedEmail()) {
        return $this->success(null, 'Email already verified');
    }

    $user->markEmailAsVerified();
    event(new Verified($user));

    return $this->success(null, 'Email verified successfully');
}

    public function resend(Request $request)
    {
        $request->validate([
            'email' => ['required', 'email'],
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return $this->error('User not found', 404);
        }

        if ($user->hasVerifiedEmail()) {
            return $this->error('Email already verified', 400);
        }

        $user->sendEmailVerificationNotification();

        return $this->success(null, 'Verification email sent successfully');
    }
}
