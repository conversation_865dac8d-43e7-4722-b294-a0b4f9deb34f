<?php

namespace App\Http\Controllers;

use App\Http\Resources\IssueResource;
use App\Models\Issue;
use App\Models\Volume;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class IssueController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/issues",
     *     summary="List issues",
     *     tags={"Issues"},
     *     @OA\Parameter(
     *         name="volume_id",
     *         in="query",
     *         description="Filter by volume ID",
     *         required=false,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="type",
     *         in="query",
     *         description="Filter by type (January or June)",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(response=200, description="List of issues"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function index()
    {
        $issues = Issue::query()
            ->with('volume')
            ->when(request('volume_id'), fn($q) => $q->where('volume_id', request('volume_id')))
            ->when(request('type'), fn($q) => $q->where('type', request('type')))
            ->orderBy('publication_date', 'desc')
            ->paginate(10);

        return IssueResource::collection($issues);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/issues",
     *     summary="Create a new issue",
     *     tags={"Issues"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"volume_id","number","type","submission_deadline"},
     *             @OA\Property(property="volume_id", type="integer", example=1),
     *             @OA\Property(property="number", type="integer", example=1),
     *             @OA\Property(property="type", type="string", example="January"),
     *             @OA\Property(property="submission_deadline", type="string", format="date", example="2025-05-31")
     *         )
     *     ),
     *     @OA\Response(response=201, description="Issue created successfully"),
     *     @OA\Response(response=422, description="Validation error or duplicate issue number"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'volume_id' => ['required', 'exists:volumes,id'],
            'number' => ['required', 'integer', 'min:1'],
            'type' => ['required', 'in:January,June'],
            'submission_deadline' => ['required', 'date'],
            'review_deadline' => ['nullable', 'date', 'after:submission_deadline'],
        ]);

        // Check if issue number already exists for this volume
        $exists = Issue::where('volume_id', $data['volume_id'])
            ->where('number', $data['number'])
            ->exists();

        if ($exists) {
            return response()->json([
                'message' => 'This issue number already exists for the selected volume'
            ], 422);
        }

        $issue = Issue::create($data);

        return (new IssueResource($issue->load('volume')))->response()->setStatusCode(201);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/issues/{id}",
     *     summary="Get a specific issue",
     *     tags={"Issues"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Issue ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="Issue details"),
     *     @OA\Response(response=404, description="Issue not found"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function show(Issue $issue)
    {
        return new IssueResource($issue->load('volume'));
    }

    /**
     * @OA\Put(
     *     path="/api/v1/issues/{id}",
     *     summary="Update an existing issue",
     *     tags={"Issues"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Issue ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             @OA\Property(property="published", type="boolean", example=true),
     *             @OA\Property(property="is_current", type="boolean", example=true),
     *             @OA\Property(property="publication_date", type="string", format="date", example="2025-06-15"),
     *             @OA\Property(property="submission_deadline", type="string", format="date", example="2025-05-30")
     *         )
     *     ),
     *     @OA\Response(response=200, description="Issue updated successfully"),
     *     @OA\Response(response=404, description="Issue not found"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function update(Request $request, Issue $issue)
    {
        // If only updating published status or is_current, use minimal validation
        if ($request->has('published') && count($request->all()) <= 2) {
            $data = $request->validate([
                'published' => 'required|boolean',
                'publication_date' => 'nullable|date'
            ]);

            // Set publication date to today if publishing and not provided
            if ($data['published'] && !isset($data['publication_date'])) {
                $data['publication_date'] = now()->toDateString();
            }

            $issue->update($data);
            return new IssueResource($issue->load('volume'));
        }

        // Full update validation for editing issue details
        $data = $request->validate([
            'volume_id' => ['sometimes', 'required', 'exists:volumes,id'],
            'number' => ['sometimes', 'required', 'integer', 'min:1'],
            'type' => ['sometimes', 'required', 'in:January,June'],
            'submission_deadline' => ['sometimes', 'required', 'date'],
            'review_deadline' => ['nullable', 'date', 'after:submission_deadline'],
            'published' => ['sometimes', 'boolean'],
            'is_current' => ['sometimes', 'boolean'],
            'publication_date' => ['nullable', 'date']
        ]);

        // Check for duplicate issue number only if number or volume_id is being updated
        if (isset($data['volume_id']) || isset($data['number'])) {
            $volumeId = $data['volume_id'] ?? $issue->volume_id;
            $number = $data['number'] ?? $issue->number;

            $exists = Issue::where('volume_id', $volumeId)
                ->where('number', $number)
                ->where('id', '!=', $issue->id)
                ->exists();

            if ($exists) {
                return response()->json([
                    'message' => 'This issue number already exists for the selected volume'
                ], 422);
            }
        }

        $issue->update($data);

        return new IssueResource($issue->load('volume'));
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/issues/{id}",
     *     summary="Delete an issue",
     *     tags={"Issues"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Issue ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="Issue deleted successfully"),
     *     @OA\Response(response=404, description="Issue not found"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function destroy(Issue $issue)
    {
        $issue->delete();

        return response()->json([
            'message' => 'Issue deleted successfully'
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/issues/current",
     *     summary="Get the current issue",
     *     tags={"Issues"},
     *     @OA\Response(
     *         response=200,
     *         description="Current issue with manuscripts",
     *         @OA\JsonContent(
     *             @OA\Property(property="issue", type="object"),
     *             @OA\Property(property="manuscripts", type="array", @OA\Items(type="object"))
     *         )
     *     ),
     *     @OA\Response(response=404, description="No current issue found")
     * )
     */
    public function getCurrentIssue()
    {
        // First try to find an issue explicitly marked as current
        $currentIssue = Issue::with(['volume', 'manuscripts' => function($query) {
                $query->where('status', 'published')
                      ->with(['author'])
                      ->orderBy('published_at', 'desc');
            }])
            ->where('is_current', true)
            ->first();

        // If no issue is marked as current, fall back to the latest published issue
        if (!$currentIssue) {
            $currentIssue = Issue::with(['volume', 'manuscripts' => function($query) {
                    $query->where('status', 'published')
                          ->with(['author'])
                          ->orderBy('published_at', 'desc');
                }])
                ->where('published', true)
                ->orderBy('publication_date', 'desc')
                ->first();
        }

        if (!$currentIssue) {
            return response()->json([
                'message' => 'No current issue found'
            ], 404);
        }

        return response()->json([
            'issue' => new IssueResource($currentIssue),
            'manuscripts' => $currentIssue->manuscripts->map(function($manuscript) {
                return [
                    'id' => $manuscript->id,
                    'title' => $manuscript->title,
                    'abstract' => $manuscript->abstract,
                    'keywords' => $manuscript->keywords,
                    'authors' => $manuscript->authors,
                    'author' => [
                        'name' => $manuscript->author->name,
                        'affiliation' => $manuscript->author->affiliation
                    ],
                    'doi' => $manuscript->doi,
                    'published_at' => $manuscript->published_at,
                    'featured' => $manuscript->featured ?? false,
                    'language' => $manuscript->language
                ];
            })
        ]);
    }

    /**
     * @OA\Put(
     *     path="/api/v1/issues/{id}/set-current",
     *     summary="Set an issue as the current issue",
     *     tags={"Issues"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Issue ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="Issue set as current successfully"),
     *     @OA\Response(response=404, description="Issue not found"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function setCurrent(Issue $issue)
    {
        $issue->update(['is_current' => true]);

        return response()->json([
            'message' => 'Issue set as current successfully',
            'issue' => new IssueResource($issue->load('volume'))
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/volumes/{volume_id}/issues",
     *     summary="Get issues for a specific volume",
     *     tags={"Issues"},
     *     @OA\Parameter(
     *         name="volume_id",
     *         in="path",
     *         description="Volume ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="List of issues for the volume"),
     *     @OA\Response(response=404, description="Volume not found"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function getVolumeIssues($volumeId)
    {
        $volume = Volume::findOrFail($volumeId);
        $issues = $volume->issues()
            ->orderBy('publication_date', 'desc')
            ->get();

        return IssueResource::collection($issues);
    }

    /**
     * @OA\Put(
     *     path="/api/v1/issues/{id}/publish",
     *     summary="Publish an issue",
     *     tags={"Issues"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Issue ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="Issue published successfully"),
     *     @OA\Response(response=404, description="Issue not found"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function publish(Issue $issue)
    {
        $issue->update([
            'published' => true,
            'publication_date' => $issue->publication_date ?? now()->toDateString()
        ]);

        return response()->json([
            'message' => 'Issue published successfully',
            'data' => new IssueResource($issue->load('volume'))
        ]);
    }
}
