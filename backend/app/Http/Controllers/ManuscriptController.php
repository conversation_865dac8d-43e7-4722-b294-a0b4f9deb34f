<?php

namespace App\Http\Controllers;

use App\Http\Resources\ManuscriptResource;
use App\Models\Issue;
use App\Models\Manuscript;
use App\Notifications\ManuscriptPublished;
use App\Notifications\PaymentReminder;
use App\Notifications\ReviewDeadlineReminder;
use App\Notifications\EditorAssigned;
use App\Notifications\ReviewSubmitted;
use App\Notifications\ManuscriptSubmitted;
use App\Notifications\ManuscriptStatusChanged;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class ManuscriptController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/manuscripts",
     *     summary="List manuscripts",
     *     tags={"Manuscripts"},
     *     @OA\Parameter(
     *         name="issue_id",
     *         in="query",
     *         description="Filter by issue ID",
     *         required=false,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by manuscript status",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", default=10, minimum=1, maximum=100)
     *     ),
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number",
     *         required=false,
     *         @OA\Schema(type="integer", default=1, minimum=1)
     *     ),
     *     @OA\Response(response=200, description="List of manuscripts"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function index()
    {
        $perPage = min(max((int)request('per_page', 10), 1), 100); // Between 1 and 100, default 10

        $query = Manuscript::query()
            ->with(['author', 'issue.volume', 'reviewers', 'reviews.reviewer'])
            ->when(
                Auth::user()->role === 'author',
                fn($q) => $q->where('author_id', Auth::id())
            )
            ->when(
                Auth::user()->role === 'reviewer',
                fn($q) => $q->whereHas('reviewers', fn($q) => $q->where('reviewer_id', Auth::id()))
            )
            ->when(request('issue_id'), fn($q) => $q->where('issue_id', request('issue_id')))
            ->when(request('status'), fn($q) => $q->where('status', request('status')))
            ->orderBy('created_at', 'desc');

        return ManuscriptResource::collection($query->paginate($perPage));
    }

    /**
     * @OA\Post(
     *     path="/api/v1/manuscripts",
     *     summary="Submit a new manuscript",
     *     tags={"Manuscripts"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 required={"title", "abstract", "keywords", "language", "file", "issue_id"},
     *                 @OA\Property(property="title", type="string", example="Research on AI"),
     *                 @OA\Property(property="abstract", type="string", example="This study explores..."),
     *                 @OA\Property(
     *                     property="keywords",
     *                     type="array",
     *                     @OA\Items(type="string", example="AI")
     *                 ),
     *                 @OA\Property(property="authors", type="array", @OA\Items(type="string", example="John Doe")),
     *                 @OA\Property(property="translated_abstract", type="string", example="Ceci est une étude..."),
     *                 @OA\Property(property="language", type="string", enum={"en", "fr"}),
     *                 @OA\Property(property="file", type="file", format="binary"),
     *                 @OA\Property(property="issue_id", type="integer", example=2)
     *             )
     *         )
     *     ),
     *     @OA\Response(response=201, description="Manuscript created successfully"),
     *     @OA\Response(response=422, description="Validation error or submission deadline passed"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => ['required', 'string', 'max:255'],
            'abstract' => ['required', 'string', 'max:1000'],
            'translated_abstract' => ['nullable', 'string', 'max:1000'],
            'language' => ['required', 'in:en,fr'],
            'file' => ['required', 'file', 'mimes:doc,docx,pdf,txt', 'max:10240'],
            'issue_id' => ['required', 'exists:issues,id'],
            'authors' => ['required'],
        ]);

        Log::info($request->all());

        $issue = Issue::findOrFail($request->issue_id);

        // if (now()->gt($issue->submission_deadline)) {
        //     return response()->json([
        //         'message' => 'Submission deadline for this issue has passed'
        //     ], 422);
        // }

        $filePath = $request->file('file')->store('manuscripts');

        $manuscript = Manuscript::create([
            'title' => $request->title,
            'abstract' => $request->abstract,
            'keywords' => $request->keywords,
            'translated_abstract' => $request->translated_abstract,
            'language' => $request->language,
            'file_path' => $filePath,
            'author_id' => Auth::id(),
            'issue_id' => $issue->id,
            'authors' => $request->authors,
        ]);

        // Notify the author that their manuscript has been submitted
        $manuscript->author->notify(new ManuscriptSubmitted($manuscript));

        return (new ManuscriptResource($manuscript->load(['author', 'issue.volume'])))->response()->setStatusCode(201);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/manuscripts/{id}",
     *     summary="Get a specific manuscript",
     *     tags={"Manuscripts"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Manuscript ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="Manuscript details"),
     *     @OA\Response(response=404, description="Manuscript not found"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function show(Manuscript $manuscript)
    {
        return new ManuscriptResource($manuscript->load(['author', 'issue.volume', 'reviewers', 'reviews.reviewer', 'versions']));
    }

    /**
     * @OA\Put(
     *     path="/api/v1/manuscripts/{id}",
     *     summary="Update a submitted manuscript",
     *     tags={"Manuscripts"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Manuscript ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 @OA\Property(property="title", type="string", example="Updated research title"),
     *                 @OA\Property(property="abstract", type="string", example="Updated abstract..."),
     *                 @OA\Property(
     *                     property="keywords",
     *                     type="array",
     *                     @OA\Items(type="string", example="Innovation")
     *                 ),
     *                 @OA\Property(property="translated_abstract", type="string", example="Abstract traduit..."),
     *                 @OA\Property(property="file", type="file", format="binary")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="Manuscript updated successfully"),
     *     @OA\Response(response=422, description="Cannot modify after review started"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function update(Request $request, Manuscript $manuscript)
    {
        if ($manuscript->status !== 'submitted') {
            return response()->json([
                'message' => 'Manuscript cannot be modified after review has started'
            ], 422);
        }

        $data = $request->validate([
            'title' => ['sometimes', 'string', 'max:255'],
            'abstract' => ['sometimes', 'string', 'max:1000'],
            'keywords' => ['sometimes', 'array', 'max:5'],
            'keywords.*' => ['string', 'max:50'],
            'translated_abstract' => ['nullable', 'string', 'max:1000'],
            'file' => ['sometimes', 'file', 'mimes:doc,docx', 'max:10240'],
        ]);

        if (isset($data['file'])) {
            Storage::delete($manuscript->file_path);
            $data['file_path'] = $data['file']->store('manuscripts');
            unset($data['file']);
        }

        $manuscript->update($data);

        return new ManuscriptResource($manuscript->fresh()->load(['author', 'issue.volume']));
    }

    /**
     * @OA\Post(
     *     path="/api/v1/manuscripts/{id}/resubmit",
     *     summary="Resubmit a manuscript with revisions",
     *     tags={"Manuscripts"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Manuscript ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 @OA\Property(property="title", type="string", example="Updated research title"),
     *                 @OA\Property(property="abstract", type="string", example="Updated abstract..."),
     *                 @OA\Property(
     *                     property="keywords",
     *                     type="array",
     *                     @OA\Items(type="string", example="Innovation")
     *                 ),
     *                 @OA\Property(property="translated_abstract", type="string", example="Abstract traduit..."),
     *                 @OA\Property(property="file", type="file", format="binary", description="Revised manuscript file"),
     *                 @OA\Property(property="revision_notes", type="string", example="Changes made in response to reviewer feedback...")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="Manuscript resubmitted successfully"),
     *     @OA\Response(response=422, description="Manuscript is not in revisions_required status"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function resubmit(Request $request, Manuscript $manuscript)
    {
        // Check if the manuscript is owned by the authenticated user
        if ($manuscript->author_id !== Auth::id()) {
            return response()->json([
                'message' => 'You are not authorized to resubmit this manuscript'
            ], 403);
        }

        // Check if manuscript requires revisions
        if ($manuscript->status !== 'revisions_required') {
            return response()->json([
                'message' => 'Manuscript can only be resubmitted when revisions are required'
            ], 422);
        }

        $data = $request->validate([
            'title' => ['sometimes', 'string', 'max:255'],
            'abstract' => ['sometimes', 'string', 'max:1000'],
            'keywords' => ['sometimes', 'array', 'max:5'],
            'keywords.*' => ['string', 'max:50'],
            'translated_abstract' => ['nullable', 'string', 'max:1000'],
            'file' => ['required', 'file', 'mimes:doc,docx,pdf', 'max:10240'],
            'revision_notes' => ['required', 'string', 'max:2000'],
        ]);

        // Get the next version number
        $nextVersion = $manuscript->versions()->max('version_number') + 1;
        if ($nextVersion === 1) {
            // If this is the first version being saved, create version 1 from current data
            \App\Models\ManuscriptVersion::create([
                'manuscript_id' => $manuscript->id,
                'version_number' => 1,
                'title' => $manuscript->title,
                'abstract' => $manuscript->abstract,
                'keywords' => $manuscript->keywords,
                'translated_abstract' => $manuscript->translated_abstract,
                'file_path' => $manuscript->file_path,
                'authors' => $manuscript->authors,
                'revision_notes' => null,
                'status' => $manuscript->status,
                'submitted_at' => $manuscript->created_at,
            ]);
            $nextVersion = 2;
        }

        // Handle file upload for new version
        $newFilePath = null;
        if (isset($data['file'])) {
            $newFilePath = $data['file']->store('manuscripts');
            unset($data['file']);
        }

        // Create new version record
        \App\Models\ManuscriptVersion::create([
            'manuscript_id' => $manuscript->id,
            'version_number' => $nextVersion,
            'title' => $data['title'] ?? $manuscript->title,
            'abstract' => $data['abstract'] ?? $manuscript->abstract,
            'keywords' => $data['keywords'] ?? $manuscript->keywords,
            'translated_abstract' => $data['translated_abstract'] ?? $manuscript->translated_abstract,
            'file_path' => $newFilePath,
            'authors' => $manuscript->authors,
            'revision_notes' => $data['revision_notes'],
            'status' => 'under_review',
            'submitted_at' => now(),
        ]);

        // Update manuscript with new data and status
        $updateData = [
            'status' => 'under_review',
            'resubmitted_at' => now(),
            'revision_notes' => $data['revision_notes'],
        ];

        if (isset($data['title'])) $updateData['title'] = $data['title'];
        if (isset($data['abstract'])) $updateData['abstract'] = $data['abstract'];
        if (isset($data['keywords'])) $updateData['keywords'] = $data['keywords'];
        if (isset($data['translated_abstract'])) $updateData['translated_abstract'] = $data['translated_abstract'];
        if ($newFilePath) $updateData['file_path'] = $newFilePath;

        $manuscript->update($updateData);

        // Log the resubmission
        Log::info("Manuscript {$manuscript->id} resubmitted as version {$nextVersion} by author {$manuscript->author_id}");

        return new ManuscriptResource($manuscript->fresh()->load(['author', 'issue.volume', 'versions']));
    }

    /**
     * Get manuscripts assigned to a reviewer
     *
     * @OA\Get(
     *     path="/api/v1/manuscripts/assigned-reviews",
     *     summary="Get manuscripts assigned to a reviewer",
     *     tags={"Manuscripts"},
     *     @OA\Parameter(
     *         name="reviewer_id",
     *         in="query",
     *         description="ID of the reviewer (optional, defaults to current user)",
     *         required=false,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by manuscript status",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of assigned manuscripts",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/ManuscriptResource"))
     *         )
     *     )
     * )
     */
    public function getAssignedReviews(Request $request)
    {
        $reviewerId = $request->query('reviewer_id', Auth::id());
        Log::info("Reviewer Id: " . $reviewerId);

        // Check if user has permission to view other reviewers' assignments
        if ($reviewerId !== Auth::id() && !in_array(Auth::user()->role, ['admin', 'editor'])) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Initialize the query
        $query = Manuscript::with([
            'author',
            'issue.volume',
            'reviewers',
            'reviews' => function ($query) use ($reviewerId) {
                $query->where('reviewer_id', $reviewerId);
            },
            'reviews.reviewer'
        ])
        ->whereHas('reviewers', function ($query) use ($reviewerId) {
            $query->where('reviewer_id', $reviewerId);
        })
        ->orderBy('created_at', 'desc');

        return ManuscriptResource::collection($query->paginate(10));
    }

    /**
     * Get manuscripts for the current author
     *
     * @OA\Get(
     *     path="/api/v1/author/manuscripts",
     *     summary="Get manuscripts for the current author",
     *     tags={"Manuscripts"},
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by manuscript status",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of author's manuscripts",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/ManuscriptResource"))
     *         )
     *     )
     * )
     */
    public function getAuthorManuscripts(Request $request)
    {
        $query = Manuscript::with([
            'author',
            'issue.volume',
            'reviewers',
            'reviews.reviewer'
        ])
        ->where('author_id', Auth::id())
        ->when($request->query('status'), function ($query, $status) {
            $query->where('status', $status);
        })
        ->orderBy('created_at', 'desc');

        return ManuscriptResource::collection($query->paginate(10));
    }

    /**
     * Direct publish a manuscript
     *
     * @OA\Post(
     *     path="/api/v1/manuscripts/direct-publish",
     *     summary="Direct publish a manuscript",
     *     tags={"Manuscripts"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 @OA\Property(property="title", type="string"),
     *                 @OA\Property(property="abstract", type="string"),
     *                 @OA\Property(property="keywords", type="string"),
     *                 @OA\Property(property="authors", type="string", description="JSON array of authors"),
     *                 @OA\Property(property="manuscript", type="file", format="binary"),
     *                 @OA\Property(property="volume", type="string"),
     *                 @OA\Property(property="issue", type="string"),
     *                 @OA\Property(property="doi", type="string"),
     *                 @OA\Property(property="publishedDate", type="string", format="date")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=201, description="Manuscript published successfully"),
     *     @OA\Response(response=403, description="Unauthorized"),
     *     @OA\Response(response=422, description="Validation error"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function directPublish(Request $request)
    {
        // Check if user has permission
        if (!in_array(Auth::user()->role, ['admin', 'editor'])) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'abstract' => 'required|string',
            'keywords' => 'required|string',
            'authors' => 'required|string', // JSON string of authors array
            'manuscript' => 'required|file|mimes:pdf|max:10240',
            'volume' => 'required|string',
            'issue' => 'required|string',
            'doi' => 'nullable|string|unique:manuscripts,doi',
            'publishedDate' => 'required|date',
            'featured' => 'boolean'
        ]);

        try {
            // Handle file upload
            $file = $request->file('manuscript');
            $filename = Str::slug($request->title) . '-' . time() . '.pdf';
            $path = $file->storeAs('manuscripts', $filename, 'public');

            // Create manuscript
            $manuscript = Manuscript::create([
                'title' => $request->title,
                'abstract' => $request->abstract,
                'keywords' => explode(',', $request->keywords),
                'file_path' => $path,
                'status' => 'published',
                'language' => 'en', // Default to English
                'authors' => json_decode($request->authors, true),
                'author_id' => Auth::id(),
                'doi' => $request->doi,
                'volume_id' => $request->volume,
                'issue_id' => $request->issue,
                'published_date' => $request->publishedDate,
                'is_direct_published' => true,
                'featured' => $request->featured ?? false
            ]);

            return (new ManuscriptResource($manuscript->load(['author'])))->response()->setStatusCode(201);

        } catch (\Exception $e) {
            // Clean up file if manuscript creation fails
            if (isset($path)) {
                Storage::disk('public')->delete($path);
            }

            return response()->json([
                'message' => 'Failed to publish manuscript',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get published manuscripts
     *
     * @OA\Get(
     *     path="/api/v1/manuscripts/published",
     *     summary="Get published manuscripts",
     *     tags={"Manuscripts"},
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         description="Search term",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="category",
     *         in="query",
     *         description="Filter by category",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="year",
     *         in="query",
     *         description="Filter by year",
     *         required=false,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="issue_id",
     *         in="query",
     *         description="Filter by issue ID",
     *         required=false,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of manuscripts to return",
     *         required=false,
     *         @OA\Schema(type="integer", default=10)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of published manuscripts",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/ManuscriptResource"))
     *         )
     *     )
     * )
     */
    public function getPublishedManuscripts(Request $request)
    {
        $query = Manuscript::query()
            ->with(['author', 'issue.volume'])
            ->where('status', 'published')
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('abstract', 'like', "%{$search}%")
                      ->orWhere('keywords', 'like', "%{$search}%")
                      ->orWhereHas('author', function ($q) use ($search) {
                          $q->where('name', 'like', "%{$search}%");
                      });
                });
            })
            ->when($request->category, function ($query, $category) {
                $query->where('category', $category);
            })
            ->when($request->year, function ($query, $year) {
                $query->whereYear('published_at', $year);
            })
            ->when($request->issue_id, function ($query, $issueId) {
                $query->where('issue_id', $issueId);
            })
            ->orderBy('published_at', 'desc');

        $perPage = $request->query('per_page', 10);
        $perPage = min($perPage, 50); // Limit maximum items per page to 50

        return ManuscriptResource::collection($query->paginate($perPage));
    }

    /**
     * Get all manuscripts for a specific issue
     *
     * @OA\Get(
     *     path="/api/v1/manuscripts/by-issue/{issue}",
     *     summary="Get all manuscripts for a specific issue",
     *     tags={"Manuscripts"},
     *     @OA\Parameter(
     *         name="issue",
     *         in="path",
     *         description="Issue ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of manuscripts in the issue",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/ManuscriptResource"))
     *         )
     *     )
     * )
     */
    public function getManuscriptsByIssue(Issue $issue)
    {
        $manuscripts = $issue->manuscripts()
            ->with(['author', 'issue.volume'])
            ->where('status', 'published')
            ->get();

        return ManuscriptResource::collection($manuscripts);
    }

    /**
     * @OA\Put(
     *     path="/api/v1/manuscripts/{id}/status",
     *     summary="Update manuscript status",
     *     tags={"Manuscripts"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Manuscript ID"
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"status"},
     *             @OA\Property(property="status", type="string", enum={"submitted", "under_review", "accepted", "rejected", "revisions_required", "payment_pending", "published"})
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Status updated successfully",
     *         @OA\JsonContent(ref="#/components/schemas/ManuscriptResource")
     *     )
     * )
     */
    public function updateStatus(Request $request, Manuscript $manuscript)
    {
        $request->validate([
            'status' => 'required|in:submitted,under_review,accepted,rejected,revisions_required,payment_pending,published'
        ]);

        $oldStatus = $manuscript->status;
        $manuscript->update(['status' => $request->status]);

        // Notify the author about the status change
        $manuscript->author->notify(new ManuscriptStatusChanged($manuscript, $oldStatus, $request->status));

        // If status changed to accepted, set payment_pending and send payment reminder
        if ($request->status === 'accepted') {
            $manuscript->update(['payment_status' => 'pending']);
            $manuscript->author->notify(new PaymentReminder($manuscript));
        }

        // If status changed to published, set published_at timestamp and notify author
        if ($request->status === 'published') {
            $manuscript->update(['published_at' => now()]);
            $manuscript->author->notify(new ManuscriptPublished($manuscript));
        }

        // If status changed to under_review, notify assigned editor
        if ($request->status === 'under_review' && $manuscript->editor) {
            $manuscript->editor->notify(new EditorAssigned($manuscript));
        }

        Log::info("Manuscript {$manuscript->id} status changed from {$oldStatus} to {$request->status}");

        return new ManuscriptResource($manuscript->load(['author', 'issue.volume', 'reviewers', 'reviews.reviewer']));
    }

    /**
     * @OA\Post(
     *     path="/api/v1/manuscripts/{id}/publish",
     *     summary="Publish a manuscript",
     *     tags={"Manuscripts"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Manuscript ID"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Manuscript published successfully",
     *         @OA\JsonContent(ref="#/components/schemas/ManuscriptResource")
     *     )
     * )
     */
    public function publish(Request $request, Manuscript $manuscript)
    {
        // Check if manuscript is ready for publishing
        if ($manuscript->status !== 'accepted' || $manuscript->payment_status !== 'paid') {
            return response()->json([
                'message' => 'Manuscript must be accepted and payment completed before publishing'
            ], 422);
        }

        $manuscript->update([
            'status' => 'published',
            'published_at' => now()
        ]);

        // Notify the author that their manuscript has been published
        $manuscript->author->notify(new ManuscriptPublished($manuscript));

        Log::info("Manuscript {$manuscript->id} has been published");

        return new ManuscriptResource($manuscript->load(['author', 'issue.volume', 'reviewers', 'reviews.reviewer']));
    }

    // download
    /**
     * @OA\Get(
     *     path="/api/v1/manuscripts/{id}/download",
     *     summary="Download a manuscript",
     *     tags={"Manuscripts"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Manuscript ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="Manuscript downloaded successfully"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function download(Manuscript $manuscript)
    {
        try {
            // Check if file exists in storage
            if (!Storage::disk('public')->exists($manuscript->file_path)) {
                Log::error('File not found at path: ' . $manuscript->file_path);
                return response()->json([
                    'message' => 'File not found'
                ], 404);
            }

            // Get the full path to the file
            $fullPath = Storage::disk('public')->path($manuscript->file_path);

            // Get the original filename for download
            $originalName = basename($manuscript->file_path);

            // Return the file for download
            return response()->download($fullPath, $originalName);
        } catch (\Exception $e) {
            Log::error('Download error: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error downloading file'
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/manuscripts/{id}/versions/{version}/download",
     *     summary="Download a specific version of a manuscript",
     *     tags={"Manuscripts"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Manuscript ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="version",
     *         in="path",
     *         description="Version ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="Version downloaded successfully"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function downloadVersion(Manuscript $manuscript, \App\Models\ManuscriptVersion $version)
    {
        try {
            // Check if the version belongs to the manuscript
            if ($version->manuscript_id !== $manuscript->id) {
                return response()->json([
                    'message' => 'Version not found for this manuscript'
                ], 404);
            }

            // Check if file exists in storage
            if (!Storage::disk('public')->exists($version->file_path)) {
                Log::error('Version file not found at path: ' . $version->file_path);
                return response()->json([
                    'message' => 'File not found'
                ], 404);
            }

            // Get the full path to the file
            $fullPath = Storage::disk('public')->path($version->file_path);

            // Get the original filename for download
            $originalName = basename($version->file_path);

            // Return the file for download
            return response()->download($fullPath, "v{$version->version_number}_{$originalName}");
        } catch (\Exception $e) {
            Log::error('Version download error: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error downloading file'
            ], 500);
        }
    }
}
