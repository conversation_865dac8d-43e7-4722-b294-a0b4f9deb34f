<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * @OA\Info(
 *     title="Jeps Journal API",
 *     version="1.0.0",
 *     description="API documentation for the Jeps Journal system.",
 *     @OA\Contact(
 *         email="<EMAIL>"
 *     )
 * )
 */
class ApiController extends Controller
{
    protected function success($data = null, $message = null, $status = 200)
    {
        return response()->json([
            'success' => true,
            'data' => $data,
            'message' => $message,
        ], $status);
    }

    protected function error($message = null, $status = 400)
    {
        return response()->json([
            'success' => false,
            'message' => $message,
        ], $status);
    }

    protected function notFound($message = null, $status = 404)
    {
        return response()->json([
            'success' => false,
            'message' => $message,
        ], $status);
    }

    protected function unauthorized($message = null, $status = 401)
    {
        return response()->json([
            'success' => false,
            'message' => $message,
        ], $status);
    }

    protected function forbidden($message = null, $status = 403)
    {
        return response()->json([
            'success' => false,
            'message' => $message,
        ], $status);
    }

    protected function validationError($errors, $status = 422)
    {
        return response()->json([
            'success' => false,
            'errors' => $errors,
        ], $status);
    }

    protected function noContent($message = null, $status = 204)
    {
        return response()->json([
            'success' => true,
            'message' => $message,
        ], $status);
    }

    protected function created($data = null, $message = null, $status = 201)
    {
        return response()->json([
            'success' => true,
            'data' => $data,
            'message' => $message,
        ], $status);
    }

    protected function updated($data = null, $message = null, $status = 200)
    {
        return response()->json([
            'success' => true,
            'data' => $data,
            'message' => $message,
        ], $status);
    }

    protected function deleted($message = null, $status = 200)
    {
        return response()->json([
            'success' => true,
            'message' => $message,
        ], $status);
    }

    protected function conflict($message = null, $status = 409)
    {
        return response()->json([
            'success' => false,
            'message' => $message,
        ], $status);
    }

    protected function tooManyRequests($message = null, $status = 429)
    {
        return response()->json([
            'success' => false,
            'message' => $message,
        ], $status);
    }

    protected function internalServerError($error = null, $status = 500)
    {
        Log::error($error);

        return response()->json([
            'success' => false,
            'message' => $error->getMessage(),
        ], $status);
    }
}
