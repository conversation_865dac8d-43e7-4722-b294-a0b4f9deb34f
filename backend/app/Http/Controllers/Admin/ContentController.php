<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiController;
use App\Models\PublicContent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class ContentController extends ApiController
{
    /**
     * Get all content items with pagination and filtering
     */
    public function index(Request $request)
    {
        try {
            $query = PublicContent::query();

            // Filter by type if provided
            if ($request->has('type')) {
                $query->where('type', $request->type);
            }

            // Filter by language if provided
            if ($request->has('language')) {
                $query->where('language', $request->language);
            }

            // Search in title and content
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->whereRaw("JSON_UNQUOTE(JSON_EXTRACT(title, '$.en')) LIKE ?", ["%{$search}%"])
                      ->orWhereRaw("JSON_UNQUOTE(JSON_EXTRACT(title, '$.fr')) LIKE ?", ["%{$search}%"])
                      ->orWhereRaw("JSON_UNQUOTE(JSON_EXTRACT(content, '$.en')) LIKE ?", ["%{$search}%"])
                      ->orWhereRaw("JSON_UNQUOTE(JSON_EXTRACT(content, '$.fr')) LIKE ?", ["%{$search}%"]);
                });
            }

            $perPage = $request->get('per_page', 15);
            $content = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $content->items(),
                'meta' => [
                    'current_page' => $content->currentPage(),
                    'last_page' => $content->lastPage(),
                    'per_page' => $content->perPage(),
                    'total' => $content->total(),
                ],
                'content_types' => $this->getContentTypeStats()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch content',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get content by type (public endpoint)
     */
    public function getByType(Request $request, $type)
    {
        try {
            $language = $request->get('language', 'en');

            $cacheKey = "content_{$type}_{$language}";

            $content = Cache::remember($cacheKey, now()->addHours(1), function () use ($type, $language) {
                return PublicContent::where('type', $type)
                    ->where('language', $language)
                    ->orderBy('created_at', 'desc')
                    ->get()
                    ->map(function ($item) {
                        return [
                            'id' => $item->id,
                            'title' => $item->title,
                            'content' => $item->content,
                            'type' => $item->type,
                            'language' => $item->language,
                            'created_at' => $item->created_at,
                            'updated_at' => $item->updated_at
                        ];
                    });
            });

            return response()->json([
                'success' => true,
                'data' => $content
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch content',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store new content
     */
    public function store(Request $request)
    {
        // Check if user has editor or admin role
        if (!in_array(Auth::user()->role, ['admin', 'editor'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Editor or Admin access required.'
            ], 403);
        }

        try {
            $validator = Validator::make($request->all(), [
                'type' => 'required|in:announcement,editorial,guidelines,homepage,about,contact,policies',
                'language' => 'required|in:en,fr',
                'title.en' => 'required|string|max:255',
                'title.fr' => 'nullable|string|max:255',
                'content.en' => 'required|string',
                'content.fr' => 'nullable|string',
                'status' => 'nullable|in:draft,published',
                'featured' => 'boolean',
                'priority' => 'integer|min:0|max:100'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $content = PublicContent::create([
                'type' => $request->type,
                'language' => $request->language,
                'title' => $request->title,
                'content' => $request->content,
                'status' => $request->get('status', 'published'),
                'featured' => $request->get('featured', false),
                'priority' => $request->get('priority', 0),
                'created_by' => Auth::id()
            ]);

            // Clear cache for this content type
            $this->clearContentCache($request->type, $request->language);

            return response()->json([
                'success' => true,
                'message' => 'Content created successfully',
                'data' => $content
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create content',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show specific content item
     */
    public function show($id)
    {
        try {
            $content = PublicContent::findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $content
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Content not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update content
     */
    public function update(Request $request, $id)
    {
        try {
            $content = PublicContent::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'type' => 'sometimes|in:announcement,editorial,guidelines,homepage,about,contact,policies',
                'language' => 'sometimes|in:en,fr',
                'title.en' => 'sometimes|string|max:255',
                'title.fr' => 'nullable|string|max:255',
                'content.en' => 'sometimes|string',
                'content.fr' => 'nullable|string',
                'status' => 'nullable|in:draft,published',
                'featured' => 'boolean',
                'priority' => 'integer|min:0|max:100'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $oldType = $content->type;
            $oldLanguage = $content->language;

            $content->update($request->only([
                'type', 'language', 'title', 'content', 'status', 'featured', 'priority'
            ]));

            // Clear cache for old and new content types
            $this->clearContentCache($oldType, $oldLanguage);
            if ($request->has('type') && $request->type !== $oldType) {
                $this->clearContentCache($request->type, $content->language);
            }
            if ($request->has('language') && $request->language !== $oldLanguage) {
                $this->clearContentCache($content->type, $request->language);
            }

            return response()->json([
                'success' => true,
                'message' => 'Content updated successfully',
                'data' => $content->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update content',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete content
     */
    public function destroy($id)
    {
        try {
            $content = PublicContent::findOrFail($id);
            $type = $content->type;
            $language = $content->language;

            $content->delete();

            // Clear cache
            $this->clearContentCache($type, $language);

            return response()->json([
                'success' => true,
                'message' => 'Content deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete content',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk operations
     */
    public function bulkAction(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'action' => 'required|in:delete,publish,unpublish,feature,unfeature',
                'ids' => 'required|array',
                'ids.*' => 'integer|exists:public_contents,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $contents = PublicContent::whereIn('id', $request->ids)->get();
            $affectedTypes = $contents->pluck('type')->unique();
            $affectedLanguages = $contents->pluck('language')->unique();

            switch ($request->action) {
                case 'delete':
                    PublicContent::whereIn('id', $request->ids)->delete();
                    break;
                case 'publish':
                    PublicContent::whereIn('id', $request->ids)->update(['status' => 'published']);
                    break;
                case 'unpublish':
                    PublicContent::whereIn('id', $request->ids)->update(['status' => 'draft']);
                    break;
                case 'feature':
                    PublicContent::whereIn('id', $request->ids)->update(['featured' => true]);
                    break;
                case 'unfeature':
                    PublicContent::whereIn('id', $request->ids)->update(['featured' => false]);
                    break;
            }

            // Clear cache for affected types and languages
            foreach ($affectedTypes as $type) {
                foreach ($affectedLanguages as $language) {
                    $this->clearContentCache($type, $language);
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Bulk {$request->action} completed successfully",
                'affected_count' => count($request->ids)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Bulk operation failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get content statistics
     */
    public function getStats()
    {
        try {
            $stats = [
                'total_content' => PublicContent::count(),
                'by_type' => PublicContent::selectRaw('type, COUNT(*) as count')
                    ->groupBy('type')
                    ->pluck('count', 'type'),
                'by_language' => PublicContent::selectRaw('language, COUNT(*) as count')
                    ->groupBy('language')
                    ->pluck('count', 'language'),
                'by_status' => PublicContent::selectRaw('status, COUNT(*) as count')
                    ->groupBy('status')
                    ->pluck('count', 'status'),
                'featured_count' => PublicContent::where('featured', true)->count(),
                'recent_count' => PublicContent::where('created_at', '>=', now()->subDays(7))->count()
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear content cache for specific type and language
     */
    private function clearContentCache($type, $language)
    {
        Cache::forget("content_{$type}_{$language}");
        Cache::forget("content_{$type}_en");
        Cache::forget("content_{$type}_fr");
    }

    /**
     * Get content type statistics
     */
    private function getContentTypeStats()
    {
        return PublicContent::selectRaw('type, COUNT(*) as count')
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();
    }
}
