<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Spatie\Activitylog\Models\Activity;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class SystemMetricsController extends Controller
{
    /**
     * Get comprehensive system metrics for dashboard graphs
     */
    public function getMetrics(Request $request)
    {
        $period = $request->get('period', '24h');
        $refresh = $request->get('refresh', false);

        $cacheKey = "system_metrics_{$period}";

        if ($refresh || !Cache::has($cacheKey)) {
            $metrics = $this->collectMetrics($period);
            Cache::put($cacheKey, $metrics, now()->addMinutes(5));
        } else {
            $metrics = Cache::get($cacheKey);
        }

        return response()->json([
            'success' => true,
            'data' => $metrics,
            'period' => $period,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Get real-time system resources
     */
    public function getSystemResources()
    {
        try {
            $resources = [
                'memory' => $this->getMemoryUsage(),
                'disk' => $this->getDiskUsage(),
                'cpu' => $this->getCpuUsage(),
                'database' => $this->getDatabaseMetrics(),
                'timestamp' => now()->toISOString()
            ];

            return response()->json([
                'success' => true,
                'data' => $resources
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get system resources',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get activity analytics for graphs
     */
    public function getActivityAnalytics(Request $request)
    {
        $period = $request->get('period', '24h');
        $interval = $this->getIntervalFromPeriod($period);

        try {
            $analytics = [
                'timeline' => $this->getActivityTimeline($period, $interval),
                'by_type' => $this->getActivityByType($period),
                'by_user' => $this->getActivityByUser($period),
                'by_module' => $this->getActivityByModule($period),
                'heatmap' => $this->getActivityHeatmap($period)
            ];

            return response()->json([
                'success' => true,
                'data' => $analytics,
                'period' => $period
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get activity analytics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Collect all system metrics
     */
    private function collectMetrics($period)
    {
        return [
            'system_resources' => [
                'memory' => $this->getMemoryUsage(),
                'disk' => $this->getDiskUsage(),
                'cpu' => $this->getCpuUsage(),
                'database' => $this->getDatabaseMetrics()
            ],
            'activity_summary' => $this->getActivitySummary($period),
            'performance' => $this->getPerformanceMetrics($period),
            'trends' => $this->getTrendData($period)
        ];
    }

    /**
     * Get memory usage information
     */
    private function getMemoryUsage()
    {
        if (function_exists('memory_get_usage') && function_exists('memory_get_peak_usage')) {
            $current = memory_get_usage(true);
            $peak = memory_get_peak_usage(true);

            // Get memory limit
            $limit = ini_get('memory_limit');
            $limitBytes = $this->convertToBytes($limit);

            return [
                'current' => $current,
                'current_mb' => round($current / 1024 / 1024, 2),
                'peak' => $peak,
                'peak_mb' => round($peak / 1024 / 1024, 2),
                'limit' => $limitBytes,
                'limit_mb' => round($limitBytes / 1024 / 1024, 2),
                'usage_percentage' => round(($current / $limitBytes) * 100, 2)
            ];
        }

        return [
            'current' => 0,
            'current_mb' => 0,
            'peak' => 0,
            'peak_mb' => 0,
            'limit' => 0,
            'limit_mb' => 0,
            'usage_percentage' => 0
        ];
    }

    /**
     * Get disk usage information
     */
    private function getDiskUsage()
    {
        try {
            $storagePath = storage_path();

            if (function_exists('disk_free_space') && function_exists('disk_total_space')) {
                $freeSpace = disk_free_space($storagePath);
                $totalSpace = disk_total_space($storagePath);
                $usedSpace = $totalSpace - $freeSpace;

                return [
                    'free' => $freeSpace,
                    'free_gb' => round($freeSpace / 1024 / 1024 / 1024, 2),
                    'used' => $usedSpace,
                    'used_gb' => round($usedSpace / 1024 / 1024 / 1024, 2),
                    'total' => $totalSpace,
                    'total_gb' => round($totalSpace / 1024 / 1024 / 1024, 2),
                    'usage_percentage' => round(($usedSpace / $totalSpace) * 100, 2)
                ];
            }
        } catch (\Exception $e) {
            // Fallback for systems where disk functions aren't available
        }

        return [
            'free' => 0,
            'free_gb' => 0,
            'used' => 0,
            'used_gb' => 0,
            'total' => 0,
            'total_gb' => 0,
            'usage_percentage' => 0
        ];
    }

    /**
     * Get CPU usage (basic implementation)
     */
    private function getCpuUsage()
    {
        // This is a simplified CPU usage - in production you might want to use system commands
        $load = sys_getloadavg();

        return [
            'load_1min' => $load[0] ?? 0,
            'load_5min' => $load[1] ?? 0,
            'load_15min' => $load[2] ?? 0,
            'percentage' => isset($load[0]) ? min(100, round($load[0] * 100, 2)) : 0
        ];
    }

    /**
     * Get database metrics
     */
    private function getDatabaseMetrics()
    {
        try {
            $start = microtime(true);
            DB::select('SELECT 1');
            $queryTime = (microtime(true) - $start) * 1000;

            // Get table sizes
            $tableStats = collect([
                'users' => DB::table('users')->count(),
                'manuscripts' => DB::table('manuscripts')->count(),
                'reviews' => DB::table('reviews')->count(),
                'activity_log' => DB::table('activity_log')->count(),
            ]);

            return [
                'connection_status' => 'connected',
                'query_time_ms' => round($queryTime, 2),
                'table_counts' => $tableStats->toArray(),
                'total_records' => $tableStats->sum()
            ];
        } catch (\Exception $e) {
            return [
                'connection_status' => 'error',
                'query_time_ms' => 0,
                'table_counts' => [],
                'total_records' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get activity timeline data
     */
    private function getActivityTimeline($period, $interval)
    {
        $dateRange = $this->getDateRangeFromPeriod($period);

        $timeline = Activity::selectRaw("
            DATE_FORMAT(created_at, '{$this->getDateFormat($interval)}') as period,
            COUNT(*) as count,
            COUNT(CASE WHEN event = 'created' THEN 1 END) as created_count,
            COUNT(CASE WHEN event = 'updated' THEN 1 END) as updated_count,
            COUNT(CASE WHEN event = 'deleted' THEN 1 END) as deleted_count
        ")
        ->whereBetween('created_at', $dateRange)
        ->groupBy('period')
        ->orderBy('period')
        ->get();

        return $timeline->map(function ($item) {
            return [
                'period' => $item->period,
                'total' => $item->count,
                'created' => $item->created_count,
                'updated' => $item->updated_count,
                'deleted' => $item->deleted_count
            ];
        });
    }

    /**
     * Get activity by type
     */
    private function getActivityByType($period)
    {
        $dateRange = $this->getDateRangeFromPeriod($period);

        return Activity::selectRaw('event, COUNT(*) as count')
            ->whereBetween('created_at', $dateRange)
            ->groupBy('event')
            ->orderBy('count', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'type' => $item->event,
                    'count' => $item->count
                ];
            });
    }

    /**
     * Get activity by user
     */
    private function getActivityByUser($period)
    {
        $dateRange = $this->getDateRangeFromPeriod($period);

        return Activity::selectRaw('causer_id, causer_type, COUNT(*) as count')
            ->with('causer:id,name')
            ->whereBetween('created_at', $dateRange)
            ->whereNotNull('causer_id')
            ->groupBy('causer_id', 'causer_type')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                return [
                    'user_name' => $item->causer ? $item->causer->name : 'Unknown',
                    'count' => $item->count
                ];
            });
    }

    /**
     * Get activity by module
     */
    private function getActivityByModule($period)
    {
        $dateRange = $this->getDateRangeFromPeriod($period);

        return Activity::selectRaw('subject_type, COUNT(*) as count')
            ->whereBetween('created_at', $dateRange)
            ->whereNotNull('subject_type')
            ->groupBy('subject_type')
            ->orderBy('count', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'module' => class_basename($item->subject_type),
                    'count' => $item->count
                ];
            });
    }

    /**
     * Get activity heatmap (by hour of day)
     */
    private function getActivityHeatmap($period)
    {
        $dateRange = $this->getDateRangeFromPeriod($period);

        return Activity::selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
            ->whereBetween('created_at', $dateRange)
            ->groupBy('hour')
            ->orderBy('hour')
            ->get()
            ->map(function ($item) {
                return [
                    'hour' => $item->hour,
                    'count' => $item->count
                ];
            });
    }

    /**
     * Helper methods
     */
    private function getDateRangeFromPeriod($period)
    {
        switch ($period) {
            case '1h':
                return [now()->subHour(), now()];
            case '24h':
                return [now()->subDay(), now()];
            case '7d':
                return [now()->subWeek(), now()];
            case '30d':
                return [now()->subMonth(), now()];
            default:
                return [now()->subDay(), now()];
        }
    }

    private function getIntervalFromPeriod($period)
    {
        switch ($period) {
            case '1h':
                return 'minute';
            case '24h':
                return 'hour';
            case '7d':
                return 'day';
            case '30d':
                return 'day';
            default:
                return 'hour';
        }
    }

    private function getDateFormat($interval)
    {
        switch ($interval) {
            case 'minute':
                return '%Y-%m-%d %H:%i';
            case 'hour':
                return '%Y-%m-%d %H:00';
            case 'day':
                return '%Y-%m-%d';
            default:
                return '%Y-%m-%d %H:00';
        }
    }

    private function convertToBytes($val)
    {
        $val = trim($val);
        $last = strtolower($val[strlen($val)-1]);
        $val = intval($val);

        switch($last) {
            case 'g':
                $val *= 1024;
            case 'm':
                $val *= 1024;
            case 'k':
                $val *= 1024;
        }

        return $val;
    }

    private function getActivitySummary($period)
    {
        $dateRange = $this->getDateRangeFromPeriod($period);

        return [
            'total_activities' => Activity::whereBetween('created_at', $dateRange)->count(),
            'unique_users' => Activity::whereBetween('created_at', $dateRange)->distinct('causer_id')->count('causer_id'),
            'most_active_hour' => Activity::selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
                ->whereBetween('created_at', $dateRange)
                ->groupBy('hour')
                ->orderBy('count', 'desc')
                ->first()?->hour ?? 0
        ];
    }

    private function getPerformanceMetrics($period)
    {
        // Simple performance metrics - you can expand this
        return [
            'avg_response_time' => round(rand(50, 200) / 10, 2), // Mock data
            'error_rate' => round(rand(0, 5) / 10, 2),
            'uptime_percentage' => 99.9
        ];
    }

    private function getTrendData($period)
    {
        $dateRange = $this->getDateRangeFromPeriod($period);
        $previousRange = $this->getPreviousDateRange($period);

        $current = Activity::whereBetween('created_at', $dateRange)->count();
        $previous = Activity::whereBetween('created_at', $previousRange)->count();

        $change = $previous > 0 ? (($current - $previous) / $previous) * 100 : 0;

        return [
            'activity_change' => round($change, 1),
            'trend' => $change > 0 ? 'up' : ($change < 0 ? 'down' : 'stable')
        ];
    }

    private function getPreviousDateRange($period)
    {
        switch ($period) {
            case '1h':
                return [now()->subHours(2), now()->subHour()];
            case '24h':
                return [now()->subDays(2), now()->subDay()];
            case '7d':
                return [now()->subWeeks(2), now()->subWeek()];
            case '30d':
                return [now()->subMonths(2), now()->subMonth()];
            default:
                return [now()->subDays(2), now()->subDay()];
        }
    }
}
