<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Spatie\Activitylog\Models\Activity;
use Illuminate\Support\Facades\Validator;

class ActivityLogController extends Controller
{
    /**
     * Get activity logs with filtering and pagination
     */
    public function index(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'search' => 'sometimes|string|max:255',
            'level' => 'sometimes|string|in:INFO,WARNING,ERROR,DEBUG',
            'event' => 'sometimes|string|max:255',
            'subject_type' => 'sometimes|string|max:255',
            'causer_type' => 'sometimes|string|max:255',
            'date_from' => 'sometimes|date',
            'date_to' => 'sometimes|date|after_or_equal:date_from',
            'per_page' => 'sometimes|integer|min:1|max:100',
            'page' => 'sometimes|integer|min:1'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $query = Activity::with(['subject', 'causer']);

            // Search functionality
            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('description', 'LIKE', "%{$search}%")
                      ->orWhere('event', 'LIKE', "%{$search}%")
                      ->orWhere('subject_type', 'LIKE', "%{$search}%");
                });
            }

            // Filter by event type
            if ($request->has('event')) {
                $query->where('event', $request->event);
            }

            // Filter by subject type (model type)
            if ($request->has('subject_type')) {
                $query->where('subject_type', 'LIKE', "%{$request->subject_type}%");
            }

            // Filter by causer type
            if ($request->has('causer_type')) {
                $query->where('causer_type', 'LIKE', "%{$request->causer_type}%");
            }

            // Date range filtering
            if ($request->has('date_from')) {
                $query->where('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to')) {
                $query->where('created_at', '<=', $request->date_to . ' 23:59:59');
            }

            // Ordering
            $query->orderBy('created_at', 'desc');

            // Pagination
            $perPage = $request->get('per_page', 50);
            $activities = $query->paginate($perPage);

            // Transform the data for frontend
            $transformedActivities = $activities->getCollection()->map(function ($activity) {
                return [
                    'id' => $activity->id,
                    'event' => $activity->event,
                    'description' => $activity->description,
                    'subject_type' => $activity->subject_type,
                    'subject_id' => $activity->subject_id,
                    'causer_type' => $activity->causer_type,
                    'causer_id' => $activity->causer_id,
                    'causer_name' => $activity->causer ? $activity->causer->name : 'System',
                    'properties' => $activity->properties,
                    'level' => $this->determineLevel($activity),
                    'module' => $this->determineModule($activity),
                    'timestamp' => $activity->created_at->toISOString(),
                    'created_at' => $activity->created_at->format('Y-m-d H:i:s'),
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $transformedActivities,
                'pagination' => [
                    'current_page' => $activities->currentPage(),
                    'last_page' => $activities->lastPage(),
                    'per_page' => $activities->perPage(),
                    'total' => $activities->total(),
                    'from' => $activities->firstItem(),
                    'to' => $activities->lastItem(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch activity logs',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get activity log statistics
     */
    public function stats(Request $request)
    {
        try {
            $dateFrom = $request->get('date_from', now()->subDays(30)->format('Y-m-d'));
            $dateTo = $request->get('date_to', now()->format('Y-m-d'));

            $query = Activity::whereBetween('created_at', [$dateFrom, $dateTo . ' 23:59:59']);

            $stats = [
                'total_activities' => $query->count(),
                'by_event' => $query->selectRaw('event, COUNT(*) as count')
                    ->groupBy('event')
                    ->pluck('count', 'event')
                    ->toArray(),
                'by_subject_type' => $query->selectRaw('subject_type, COUNT(*) as count')
                    ->groupBy('subject_type')
                    ->pluck('count', 'subject_type')
                    ->toArray(),
                'recent_activity' => Activity::with(['subject', 'causer'])
                    ->orderBy('created_at', 'desc')
                    ->limit(10)
                    ->get()
                    ->map(function ($activity) {
                        return [
                            'description' => $activity->description,
                            'causer_name' => $activity->causer ? $activity->causer->name : 'System',
                            'created_at' => $activity->created_at->format('Y-m-d H:i:s'),
                        ];
                    }),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch activity log statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear old activity logs
     */
    public function clear(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'days' => 'sometimes|integer|min:1|max:365',
            'confirm' => 'required|boolean|accepted'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $days = $request->get('days', 30);
            $cutoffDate = now()->subDays($days);

            $deletedCount = Activity::where('created_at', '<', $cutoffDate)->delete();

            return response()->json([
                'success' => true,
                'message' => "Successfully deleted {$deletedCount} activity log entries older than {$days} days"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear activity logs',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export activity logs
     */
    public function export(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'format' => 'required|in:csv,json',
            'date_from' => 'sometimes|date',
            'date_to' => 'sometimes|date|after_or_equal:date_from',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $query = Activity::with(['subject', 'causer']);

            // Date range filtering
            if ($request->has('date_from')) {
                $query->where('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to')) {
                $query->where('created_at', '<=', $request->date_to . ' 23:59:59');
            }

            $activities = $query->orderBy('created_at', 'desc')->get();

            $exportData = $activities->map(function ($activity) {
                return [
                    'id' => $activity->id,
                    'event' => $activity->event,
                    'description' => $activity->description,
                    'subject_type' => $activity->subject_type,
                    'subject_id' => $activity->subject_id,
                    'causer_name' => $activity->causer ? $activity->causer->name : 'System',
                    'level' => $this->determineLevel($activity),
                    'module' => $this->determineModule($activity),
                    'created_at' => $activity->created_at->format('Y-m-d H:i:s'),
                ];
            });

            $format = $request->get('format', 'json');
            $filename = 'activity_logs_' . now()->format('Y-m-d_H-i-s');

            if ($format === 'csv') {
                $headers = [
                    'Content-Type' => 'text/csv',
                    'Content-Disposition' => "attachment; filename=\"{$filename}.csv\"",
                ];

                $callback = function() use ($exportData) {
                    $file = fopen('php://output', 'w');

                    // Write CSV headers
                    fputcsv($file, ['ID', 'Event', 'Description', 'Subject Type', 'Subject ID', 'User', 'Level', 'Module', 'Created At']);

                    // Write data rows
                    foreach ($exportData as $row) {
                        fputcsv($file, [
                            $row['id'],
                            $row['event'],
                            $row['description'],
                            $row['subject_type'],
                            $row['subject_id'],
                            $row['causer_name'],
                            $row['level'],
                            $row['module'],
                            $row['created_at']
                        ]);
                    }

                    fclose($file);
                };

                return response()->stream($callback, 200, $headers);
            } else {
                // JSON format
                return response()->json($exportData->toArray())
                    ->header('Content-Type', 'application/json')
                    ->header('Content-Disposition', "attachment; filename=\"{$filename}.json\"");
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export activity logs',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Determine log level based on activity
     */
    private function determineLevel($activity)
    {
        // Determine log level based on event type and subject
        $event = strtolower($activity->event);
        $subjectType = class_basename($activity->subject_type);

        if (in_array($event, ['deleted', 'failed'])) {
            return 'ERROR';
        }

        if (in_array($event, ['updated']) && in_array($subjectType, ['User', 'Payment'])) {
            return 'WARNING';
        }

        if (in_array($event, ['created', 'login'])) {
            return 'INFO';
        }

        return 'DEBUG';
    }

    /**
     * Determine module based on activity
     */
    private function determineModule($activity)
    {
        $subjectType = class_basename($activity->subject_type);

        switch ($subjectType) {
            case 'User':
                return 'User Management';
            case 'Manuscript':
                return 'Manuscript System';
            case 'Review':
                return 'Review System';
            case 'Payment':
                return 'Payment System';
            default:
                return 'System';
        }
    }
}
