<?php

namespace App\Http\Controllers;

use App\Models\Manuscript;
use App\Models\PlagiarismCheck;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

/**
 * @OA\Tag(
 *     name="Plagiarism",
 *     description="Plagiarism checking operations"
 * )
 */
class PlagiarismController extends ApiController
{
    /**
     * @OA\Post(
     *     path="/api/v1/plagiarism/check",
     *     summary="Check manuscript for plagiarism",
     *     tags={"Plagiarism"},
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"manuscript_id"},
     *             @OA\Property(property="manuscript_id", type="integer", description="ID of the manuscript to check")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Plagiarism check initiated",
     *         @OA\JsonContent(
     *             @OA\Property(property="check_id", type="string", description="Unique check ID"),
     *             @OA\Property(property="status", type="string", description="Check status")
     *         )
     *     )
     * )
     */
    public function check(Request $request)
    {
        $request->validate([
            'manuscript_id' => 'required|integer|exists:manuscripts,id'
        ]);

        $manuscript = Manuscript::findOrFail($request->manuscript_id);

        // Check if user has permission to check this manuscript
        if (!$request->user()->can('view', $manuscript)) {
            return $this->error('Unauthorized', 403);
        }

        // Create plagiarism check record
        $check = PlagiarismCheck::create([
            'id' => Str::uuid(),
            'manuscript_id' => $manuscript->id,
            'user_id' => $request->user()->id,
            'status' => 'processing',
            'similarity_percentage' => null,
            'report_path' => null
        ]);

        // In a real implementation, this would trigger an external plagiarism service
        // For now, we'll simulate a check with random results
        $this->simulatePlagiarismCheck($check);

        return $this->success([
            'check_id' => $check->id,
            'status' => $check->status
        ], 'Plagiarism check initiated');
    }

    /**
     * @OA\Get(
     *     path="/api/v1/plagiarism/result/{checkId}",
     *     summary="Get plagiarism check result",
     *     tags={"Plagiarism"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="checkId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="string"),
     *         description="Plagiarism check ID"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Plagiarism check result",
     *         @OA\JsonContent(
     *             @OA\Property(property="check_id", type="string"),
     *             @OA\Property(property="status", type="string"),
     *             @OA\Property(property="similarity_percentage", type="number"),
     *             @OA\Property(property="report_url", type="string")
     *         )
     *     )
     * )
     */
    public function getResult($checkId)
    {
        $check = PlagiarismCheck::findOrFail($checkId);

        // Check if user has permission to view this result
        if (!request()->user()->can('view', $check->manuscript)) {
            return $this->error('Unauthorized', 403);
        }

        $result = [
            'check_id' => $check->id,
            'status' => $check->status,
            'similarity_percentage' => $check->similarity_percentage,
            'created_at' => $check->created_at,
            'completed_at' => $check->completed_at
        ];

        if ($check->report_path) {
            $result['report_url'] = Storage::url($check->report_path);
        }

        return $this->success($result);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/plagiarism/history/{manuscriptId}",
     *     summary="Get plagiarism check history for manuscript",
     *     tags={"Plagiarism"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="manuscriptId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Manuscript ID"
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Plagiarism check history",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 @OA\Property(property="check_id", type="string"),
     *                 @OA\Property(property="status", type="string"),
     *                 @OA\Property(property="similarity_percentage", type="number"),
     *                 @OA\Property(property="created_at", type="string")
     *             )
     *         )
     *     )
     * )
     */
    public function getHistory($manuscriptId)
    {
        $manuscript = Manuscript::findOrFail($manuscriptId);

        // Check if user has permission to view this manuscript
        if (!request()->user()->can('view', $manuscript)) {
            return $this->error('Unauthorized', 403);
        }

        $checks = PlagiarismCheck::where('manuscript_id', $manuscriptId)
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($check) {
                return [
                    'check_id' => $check->id,
                    'status' => $check->status,
                    'similarity_percentage' => $check->similarity_percentage,
                    'created_at' => $check->created_at,
                    'completed_at' => $check->completed_at
                ];
            });

        return $this->success($checks);
    }

    /**
     * Simulate plagiarism check (for demo purposes)
     */
    private function simulatePlagiarismCheck(PlagiarismCheck $check)
    {
        // In a real implementation, this would be handled by a queue job
        // For now, we'll just set random results
        $check->update([
            'status' => 'completed',
            'similarity_percentage' => rand(0, 25), // Random percentage between 0-25%
            'completed_at' => now()
        ]);
    }
}
