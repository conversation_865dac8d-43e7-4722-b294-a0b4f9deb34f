<?php

namespace App\Http\Controllers;

use App\Http\Resources\UserResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/users",
     *     summary="List users",
     *     tags={"Users"},
     *     @OA\Parameter(
     *         name="role",
     *         in="query",
     *         description="Filter users by role",
     *         required=false,
     *         @OA\Schema(type="string", enum={"author", "reviewer", "editor", "admin"})
     *     ),
     *     @OA\Response(response=200, description="List of users"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function index()
    {
        $users = User::query()
            ->when(request('role'), fn($q, $role) => $q->where('role', $role))
            ->when(request('search'), function ($q, $search) {
                $q->where(function ($query) use ($search) {
                    $query->where('name', 'like', "%$search%")
                        ->orWhere('email', 'like', "%$search%")
                        ->orWhere('affiliation', 'like', "%$search%")
                        ->orWhere('country', 'like', "%$search%");
                });
            })
            ->paginate(10);

        return UserResource::collection($users);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/reviewers",
     *     summary="List available reviewers",
     *     tags={"Users"},
     *     @OA\Response(response=200, description="List of available reviewers"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function reviewers()
    {
        $reviewers = User::query()
            ->where('role', 'reviewer')
            ->select(['id', 'name', 'affiliation', 'expertise'])
            ->get();

        return UserResource::collection($reviewers);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/users/{id}",
     *     summary="Get user details",
     *     tags={"Users"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="User ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="User details"),
     *     @OA\Response(response=404, description="User not found"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function show(User $user)
    {
        return new UserResource($user);
    }

    /**
     * @OA\Put(
     *     path="/api/v1/users/{id}",
     *     summary="Update user details",
     *     tags={"Users"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="User ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 required={"name", "email"},
     *                 @OA\Property(property="name", type="string", maxLength=255),
     *                 @OA\Property(property="email", type="string", format="email", maxLength=255),
     *                 @OA\Property(property="role", type="string", enum={"author", "reviewer", "editor", "admin"}),
     *                 @OA\Property(property="affiliation", type="string", maxLength=255, nullable=true),
     *                 @OA\Property(property="country", type="string", maxLength=255, nullable=true),
     *                 @OA\Property(property="expertise", type="array", @OA\Items(type="string", maxLength=255), nullable=true)
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="User details updated"),
     *     @OA\Response(response=422, description="Validation errors"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function update(Request $request, User $user)
    {
        $data = $request->validate([
            'name' => ['sometimes', 'string', 'max:255'],
            'email' => ['sometimes', 'email', 'max:255', Rule::unique('users')->ignore($user)],
            'role' => ['sometimes', 'in:author,reviewer,editor,admin'],
            'affiliation' => ['nullable', 'string', 'max:255'],
            'country' => ['nullable', 'string', 'max:255'],
            'expertise' => ['nullable', 'array'],
            'expertise.*' => ['string', 'max:255'],
        ]);

        $user->update($data);

        return new UserResource($user->fresh());
    }

    /**
     * Update current user's profile
     */
    public function updateProfile(Request $request)
    {
        $user = $request->user();

        $data = $request->validate([
            'name' => ['sometimes', 'string', 'max:255'],
            'email' => ['sometimes', 'email', 'max:255', Rule::unique('users')->ignore($user)],
            'phone' => ['nullable', 'string', 'max:255'],
            'affiliation' => ['nullable', 'string', 'max:255'],
            'position' => ['nullable', 'string', 'max:255'],
            'education' => ['nullable', 'string', 'max:255'],
            'bio' => ['nullable', 'string'],
            'country' => ['nullable', 'string', 'max:255'],
            'expertise' => ['nullable', 'array'],
            'expertise.*' => ['string', 'max:255'],
            'preferred_language' => ['nullable', 'string', 'max:10']
        ]);

        $user->update($data);

        return response()->json([
            'message' => 'Profile updated successfully',
            'data' => new UserResource($user->fresh())
        ]);
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/users/{id}",
     *     summary="Delete a user",
     *     tags={"Users"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="User ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="User deleted successfully"),
     *     @OA\Response(response=404, description="User not found"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function destroy(User $user)
    {
        $user->delete();

        return response()->json([
            'message' => 'User deleted successfully'
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/users/authors",
     *     summary="List all authors",
     *     tags={"Users"},
     *     @OA\Response(
     *         response=200,
     *         description="List of authors",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="John Doe"),
     *                     @OA\Property(property="email", type="string", example="<EMAIL>"),
     *                     @OA\Property(property="affiliation", type="string", example="University of Example"),
     *                     @OA\Property(property="country", type="string", example="Cameroon")
     *                 )
     *             )
     *         )
     *     ),
     *     security={{"sanctum":{}}}
     * )
     */
    public function authors()
    {
        $authors = User::where('role', 'author')
            ->select(['id', 'name', 'email', 'affiliation', 'country'])
            ->get();

        return response()->json(['data' => $authors]);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/users",
     *     summary="Create a new user (Admin only)",
     *     tags={"Users"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 required={"name", "email", "password", "role"},
     *                 @OA\Property(property="name", type="string", maxLength=255),
     *                 @OA\Property(property="email", type="string", format="email", maxLength=255),
     *                 @OA\Property(property="password", type="string", minLength=8),
     *                 @OA\Property(property="role", type="string", enum={"author", "reviewer", "editor", "admin"}),
     *                 @OA\Property(property="affiliation", type="string", maxLength=255, nullable=true),
     *                 @OA\Property(property="country", type="string", maxLength=255, nullable=true),
     *                 @OA\Property(property="expertise", type="array", @OA\Items(type="string", maxLength=255), nullable=true),
     *                 @OA\Property(property="preferred_language", type="string", maxLength=10, nullable=true)
     *             )
     *         )
     *     ),
     *     @OA\Response(response=201, description="User created successfully"),
     *     @OA\Response(response=422, description="Validation errors"),
     *     security={{"sanctum":{}}}
     * )
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'string', 'min:8'],
            'role' => ['required', 'in:author,reviewer,editor,admin'],
            'affiliation' => ['nullable', 'string', 'max:255'],
            'country' => ['nullable', 'string', 'max:255'],
            'expertise' => ['nullable', 'array'],
            'expertise.*' => ['string', 'max:255'],
            'preferred_language' => ['nullable', 'string', 'max:10']
        ]);

        $user = User::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
            'role' => $data['role'],
            'affiliation' => $data['affiliation'] ?? null,
            'country' => $data['country'] ?? null,
            'expertise' => $data['expertise'] ?? [],
            'preferred_language' => $data['preferred_language'] ?? 'en',
            'email_verified_at' => now() // Auto-verify admin-created users
        ]);

        return new UserResource($user);
    }
}
