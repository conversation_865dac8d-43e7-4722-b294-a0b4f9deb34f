<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class SettingsController extends Controller
{
    /**
     * Get all settings
     */
    public function index()
    {
        $settings = Setting::all()->groupBy('group');
        return response()->json($settings);
    }

    /**
     * Get settings by group
     */
    public function getByGroup(string $group)
    {
        $settings = Setting::where('group', $group)->get();
        return response()->json($settings);
    }

    /**
     * Get public settings
     */
    public function getPublic()
    {
        $settings = Setting::where('is_public', true)->get();
        return response()->json($settings);
    }

    /**
     * Update settings
     */
    public function update(Request $request)
    {
        $data = $request->validate([
            'settings' => 'required|array',
            'settings.*.key' => 'required|string',
            'settings.*.value' => 'nullable',
        ]);

        foreach ($data['settings'] as $setting) {
            Setting::set($setting['key'], $setting['value']);
        }

        // Clear settings cache
        Cache::forget('settings');

        return response()->json([
            'success' => true,
            'message' => 'Settings updated successfully'
        ]);
    }

    /**
     * Create a new setting
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'key' => 'required|string|unique:settings',
            'value' => 'required',
            'group' => 'required|string',
            'type' => 'required|string',
            'label' => 'required|string',
            'description' => 'nullable|string',
            'is_public' => 'boolean'
        ]);

        $setting = Setting::create($data);

        return response()->json($setting, 201);
    }

    /**
     * Delete a setting
     */
    public function destroy(string $key)
    {
        $setting = Setting::where('key', $key)->firstOrFail();
        $setting->delete();

        return response()->json([
            'message' => 'Setting deleted successfully'
        ]);
    }
}
