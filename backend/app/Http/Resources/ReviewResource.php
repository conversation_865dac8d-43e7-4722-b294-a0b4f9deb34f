<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReviewResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'recommendation' => $this->recommendation,
            'feedback' => $this->feedback,
            'confidential_comments' => $this->when(
                $request->user()?->can('view confidential comments', $this->resource) ||
                $request->user()?->id === $this->reviewer_id,
                $this->confidential_comments
            ),
            'submitted_at' => $this->submitted_at?->toISOString(),
            'completed_at' => $this->completed_at?->toISOString(),
            'status' => $this->status, // Uses the getStatusAttribute from model
            'is_submitted' => $this->isSubmitted(),
            'is_completed' => $this->isCompleted(),
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),

            // Relationships
            'manuscript' => new ManuscriptResource($this->whenLoaded('manuscript')),
            'reviewer' => $this->when(
                $request->user()?->can('view reviewer details'),
                new UserResource($this->reviewer)
            ),

            // Links
            'links' => [
                // 'self' => route('api.reviews.show', [
                //     'manuscript' => $this->manuscript_id,
                //     'review' => $this->id
                // ]),
            ],
        ];
    }
}
