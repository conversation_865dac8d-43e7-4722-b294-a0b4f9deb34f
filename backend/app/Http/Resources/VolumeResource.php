<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VolumeResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'number' => $this->number,
            'year' => $this->year,
            'published' => $this->published,
            'publication_date' => $this->publication_date?->toISOString(),
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),

            // Relationships
            'issues' => IssueResource::collection($this->whenLoaded('issues')),
            'manuscripts' => ManuscriptResource::collection($this->whenLoaded('manuscripts')),

            // Stats
            'stats' => $this->when(
                $request->user()?->can('view volume stats'),
                [
                    'manuscripts_count' => $this->manuscripts_count,
                    'published_issues' => $this->issues->where('published', true)->count(),
                ]
            ),
        ];
    }
}
