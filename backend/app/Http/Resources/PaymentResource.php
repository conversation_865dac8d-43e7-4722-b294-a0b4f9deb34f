<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PaymentResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'amount' => $this->amount,
            'currency' => $this->currency,
            'status' => $this->status,
            'paid_at' => $this->paid_at?->toISOString(),
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),

            // Relationships
            'manuscript' => new ManuscriptResource($this->whenLoaded('manuscript')),
            'payment_method' => $this->payment_method,
            'payment_date' => $this->payment_date,
            'transaction_id' => $this->transaction_id,
            'notes' => $this->notes,

            // Links
            'links' => [
                'receipt' => $this->receipt_url, // You would generate this URL
            ],
        ];
    }
}
