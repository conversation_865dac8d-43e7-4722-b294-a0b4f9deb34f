<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'affiliation' => $this->affiliation,
            'position' => $this->position,
            'education' => $this->education,
            'bio' => $this->bio,
            'country' => $this->country,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'expertise' => $this->expertise,
            'email_verified_at' => $this->email_verified_at,
            'role' => $this->role,

            // Links
            'links' => [
                // 'profile' => route('api.users.show', $this->id),
            ],
        ];
    }
}
