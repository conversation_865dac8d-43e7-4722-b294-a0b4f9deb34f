<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ManuscriptResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'submission_id' => $this->submission_id,
            'title' => $this->title,
            'abstract' => $this->abstract,
            'translated_abstract' => $this->translated_abstract,
            'keywords' => $this->keywords,
            'language' => $this->language,
            'status' => $this->status,
            'authors' => $this->authors,
            'payment_status' => $this->payment_status,
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
            'file_path' => $this->file_path,
            'published_at' => $this->published_at,

            // Relationships
            'author' => new UserResource($this->whenLoaded('author')),
            'issue' => new IssueResource($this->whenLoaded('issue')),
            'reviews' => ReviewResource::collection($this->whenLoaded('reviews')),
            'payment' => new PaymentResource($this->whenLoaded('payment')),
            'reviewers' => $this->whenLoaded('reviewers', function() {
                return $this->reviewers->map(function($reviewer) {
                    return [
                        'id' => $reviewer->id,
                        'name' => $reviewer->name,
                        'affiliation' => $reviewer->affiliation,
                        'pivot' => [
                            'assigned_at' => $reviewer->pivot->assigned_at,
                            'completed_at' => $reviewer->pivot->completed_at
                        ]
                    ];
                });
            }),
            'versions' => $this->whenLoaded('versions', function() {
                return $this->versions->map(function($version) {
                    return [
                        'id' => $version->id,
                        'version_number' => $version->version_number,
                        'title' => $version->title,
                        'abstract' => $version->abstract,
                        'keywords' => $version->keywords,
                        'translated_abstract' => $version->translated_abstract,
                        'file_path' => $version->file_path,
                        'authors' => $version->authors,
                        'revision_notes' => $version->revision_notes,
                        'status' => $version->status,
                        'submitted_at' => $version->submitted_at?->toISOString(),
                        'created_at' => $version->created_at?->toISOString(),
                    ];
                });
            }),

            // Links
            'links' => [
                'file' => route(name: 'api.manuscripts.download', parameters: $this->id),
            ],
        ];
    }
}
