<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class IssueResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'number' => $this->number,
            'published' => $this->published,
            'is_current' => $this->is_current,
            'publication_date' => $this->publication_date?->toISOString(),
            'submission_deadline' => $this->submission_deadline->toISOString(),
            'review_deadline' => $this->review_deadline?->toISOString(),
            'created_at' => $this->created_at->toISOString(),

            // Relationships
            'volume' => new VolumeResource($this->whenLoaded('volume')),
            'manuscripts' => ManuscriptResource::collection($this->whenLoaded('manuscripts')),
        ];
    }
}
