<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CacheControlMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$cacheOptions): Response
    {
        $response = $next($request);

        // Get cache option or default
        $cacheType = $cacheOptions[0] ?? 'default';

        // Apply cache headers based on type
        $this->applyCacheHeaders($response, $cacheType, $request);

        return $response;
    }

    /**
     * Apply appropriate cache headers based on content type and cache strategy
     */
    private function applyCacheHeaders(Response $response, string $cacheType, Request $request): void
    {
        // Skip cache headers for errors
        if ($response->getStatusCode() >= 400) {
            $response->headers->set('Cache-Control', 'no-cache, no-store, must-revalidate');
            $response->headers->set('Pragma', 'no-cache');
            $response->headers->set('Expires', '0');
            return;
        }

        // Define cache strategies
        $cacheStrategies = [
            'static' => [
                'max-age' => 86400, // 24 hours
                'public' => true,
                'etag' => true,
            ],
            'dynamic' => [
                'max-age' => 300, // 5 minutes
                'public' => true,
                'etag' => true,
            ],
            'private' => [
                'max-age' => 0,
                'private' => true,
                'etag' => false,
            ],
            'no-cache' => [
                'max-age' => 0,
                'no-cache' => true,
                'no-store' => true,
            ],
            'default' => [
                'max-age' => 600, // 10 minutes
                'public' => true,
                'etag' => true,
            ],
        ];

        $strategy = $cacheStrategies[$cacheType] ?? $cacheStrategies['default'];

        // Build Cache-Control header
        $cacheControl = [];

        if (isset($strategy['max-age'])) {
            $cacheControl[] = 'max-age=' . $strategy['max-age'];
        }

        if (isset($strategy['public']) && $strategy['public']) {
            $cacheControl[] = 'public';
        }

        if (isset($strategy['private']) && $strategy['private']) {
            $cacheControl[] = 'private';
        }

        if (isset($strategy['no-cache']) && $strategy['no-cache']) {
            $cacheControl[] = 'no-cache';
        }

        if (isset($strategy['no-store']) && $strategy['no-store']) {
            $cacheControl[] = 'no-store';
        }

        if (isset($strategy['must-revalidate']) && $strategy['must-revalidate']) {
            $cacheControl[] = 'must-revalidate';
        }

        // Set Cache-Control header
        if (!empty($cacheControl)) {
            $response->headers->set('Cache-Control', implode(', ', $cacheControl));
        }

        // Add ETag for cacheable responses
        if (isset($strategy['etag']) && $strategy['etag']) {
            $content = $response->getContent();
            if ($content) {
                $etag = '"' . md5($content) . '"';
                $response->headers->set('ETag', $etag);

                // Check if client has matching ETag
                if ($request->header('If-None-Match') === $etag) {
                    $response->setStatusCode(304);
                    $response->setContent('');
                    return;
                }
            }
        }

        // Add Last-Modified header for GET requests
        if ($request->isMethod('GET') && $cacheType !== 'no-cache') {
            $response->headers->set('Last-Modified', gmdate('D, d M Y H:i:s') . ' GMT');
        }

        // Add Vary header for better caching
        $response->headers->set('Vary', 'Accept, Accept-Encoding, Authorization');

        // Add security headers
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
    }
}
