<?php

namespace App\Http\Middleware;

use App\Notifications\AccountActivity;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Jen<PERSON>gers\Agent\Agent;

class DetectAccountActivity
{
    public function handle(Request $request, Closure $next)
    {
        if (Auth::check()) {
            $user = Auth::user();
            $agent = new Agent();

            $deviceInfo = [
                'device' => $agent->device(),
                'browser' => $agent->browser(),
                'ip' => $request->ip(),
            ];

            // Get location from IP (you might want to use a service like MaxMind GeoIP2)
            $location = null; // Implement IP geolocation here

            // Detect suspicious activity
            $suspiciousActivity = $this->detectSuspiciousActivity($user, $request, $deviceInfo);

            if ($suspiciousActivity) {
                $user->notify(new AccountActivity(
                    $suspiciousActivity,
                    $deviceInfo,
                    $location
                ));
            }
        }

        return $next($request);
    }

    protected function detectSuspiciousActivity($user, $request, $deviceInfo)
    {
        // Check for login from new device
        if ($request->is('api/v1/auth/login')) {

            // You might want to store known devices in the database
            $knownDevices = $user->knownDevices;
            if ($knownDevices->contains('device', $deviceInfo['device'])) {
                return null;
            }
            // and check against them here
            return 'Login from new device';
        }

        // Check for password change
        if ($request->is('api/v1/auth/password') && $request->isMethod('put')) {
            return 'Password changed';
        }

        // Check for email change
        if ($request->is('api/v1/auth/email') && $request->isMethod('put')) {
            return 'Email address changed';
        }

        // Add more suspicious activity checks here

        return null;
    }
}
