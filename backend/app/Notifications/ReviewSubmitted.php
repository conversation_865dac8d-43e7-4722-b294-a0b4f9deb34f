<?php

namespace App\Notifications;

use App\Models\Manuscript;
use App\Models\Review;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ReviewSubmitted extends Notification implements ShouldQueue
{
    use Queueable;

    protected $manuscript;
    protected $review;

    /**
     * Create a new notification instance.
     */
    public function __construct(Manuscript $manuscript, Review $review)
    {
        $this->manuscript = $manuscript;
        $this->review = $review;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $actionUrl = config('app.frontend_url') . '/editor/manuscripts/' . $this->manuscript->id;

        return (new MailMessage)
            ->subject('New Review Submitted')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('A new review has been submitted for the following manuscript:')
            ->line('**Manuscript Title:** ' . $this->manuscript->title)
            ->line('**Submission ID:** ' . $this->manuscript->submission_id)
            ->line('**Reviewer:** ' . $this->review->reviewer->name)
            ->line('**Review Date:** ' . $this->review->submitted_at->format('F j, Y'))
            ->line('**Recommendation:** ' . $this->review->recommendation)
            ->action('View Review', $actionUrl)
            ->line('Please review the submitted review and take appropriate action.')
            ->line('If you have any questions or concerns, please contact the editorial team.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'manuscript_id' => $this->manuscript->id,
            'manuscript_title' => $this->manuscript->title,
            'review_id' => $this->review->id,
            'reviewer_id' => $this->review->reviewer_id,
            'submitted_at' => $this->review->submitted_at,
        ];
    }
}
