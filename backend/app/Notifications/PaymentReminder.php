<?php

namespace App\Notifications;

use App\Models\Manuscript;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PaymentReminder extends Notification implements ShouldQueue
{
    use Queueable;

    protected $manuscript;
    protected $daysOverdue;

    /**
     * Create a new notification instance.
     */
    public function __construct(Manuscript $manuscript, int $daysOverdue = 0)
    {
        $this->manuscript = $manuscript;
        $this->daysOverdue = $daysOverdue;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $actionUrl = config('app.frontend_url') . '/author/submissions/' . $this->manuscript->id . '/payment';

        $message = (new MailMessage)
            ->subject('Payment Reminder for Manuscript Publication')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('This is a reminder about the pending payment for your manuscript publication.')
            ->line('**Manuscript Title:** ' . $this->manuscript->title)
            ->line('**Submission ID:** ' . $this->manuscript->submission_id)
            ->line('**Amount Due:** 75,000 XAF')
            ->action('Make Payment', $actionUrl);

        if ($this->daysOverdue > 0) {
            $message->line('Your payment is overdue by ' . $this->daysOverdue . ' days.');
        }

        return $message
            ->line('Please complete the payment to proceed with the publication process.')
            ->line('If you have already made the payment, please ignore this reminder.')
            ->line('For any payment-related queries, please contact our support team.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'manuscript_id' => $this->manuscript->id,
            'manuscript_title' => $this->manuscript->title,
            'days_overdue' => $this->daysOverdue,
        ];
    }
}
