<?php

namespace App\Notifications;

use App\Models\Manuscript;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ReviewDeadlineReminder extends Notification implements ShouldQueue
{
    use Queueable;

    protected $manuscript;
    protected $daysRemaining;

    /**
     * Create a new notification instance.
     */
    public function __construct(Manuscript $manuscript, int $daysRemaining)
    {
        $this->manuscript = $manuscript;
        $this->daysRemaining = $daysRemaining;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $actionUrl = config('app.frontend_url') . '/reviewer/manuscripts/' . $this->manuscript->id;

        return (new MailMessage)
            ->subject('Review Deadline Reminder')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('This is a reminder that you have ' . $this->daysRemaining . ' days remaining to complete your review.')
            ->line('**Manuscript Title:** ' . $this->manuscript->title)
            ->line('**Submission ID:** ' . $this->manuscript->submission_id)
            ->action('Complete Review', $actionUrl)
            ->line('Please ensure you submit your review before the deadline.')
            ->line('If you need more time, please contact the editorial team.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'manuscript_id' => $this->manuscript->id,
            'manuscript_title' => $this->manuscript->title,
            'days_remaining' => $this->daysRemaining,
        ];
    }
}
