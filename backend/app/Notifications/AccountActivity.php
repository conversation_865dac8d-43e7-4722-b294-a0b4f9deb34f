<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AccountActivity extends Notification implements ShouldQueue
{
    use Queueable;

    protected $activity;
    protected $deviceInfo;
    protected $location;

    /**
     * Create a new notification instance.
     */
    public function __construct($activity, $deviceInfo, $location = null)
    {
        $this->activity = $activity;
        $this->deviceInfo = $deviceInfo;
        $this->location = $location;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via($notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        $message = (new MailMessage)
            ->subject('Suspicious Account Activity Detected')
            ->greeting('Hello ' . $notifiable->name)
            ->line('We detected the following activity on your account:')
            ->line($this->activity)
            ->line('Device Information:')
            ->line('- Device: ' . $this->deviceInfo['device'])
            ->line('- Browser: ' . $this->deviceInfo['browser'])
            ->line('- IP Address: ' . $this->deviceInfo['ip']);

        if ($this->location) {
            $message->line('Location: ' . $this->location);
        }

        $message->line('If this was you, you can ignore this message.')
            ->line('If you did not perform this action, please secure your account immediately.')
            ->action('Secure Account', url('/account/security'))
            ->line('Thank you for using our application!');

        return $message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray($notifiable): array
    {
        return [
            'activity' => $this->activity,
            'device_info' => $this->deviceInfo,
            'location' => $this->location,
            'timestamp' => now(),
        ];
    }
}
