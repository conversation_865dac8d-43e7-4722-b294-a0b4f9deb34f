<?php

namespace App\Notifications;

use App\Models\Manuscript;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EditorAssigned extends Notification implements ShouldQueue
{
    use Queueable;

    protected $manuscript;

    /**
     * Create a new notification instance.
     */
    public function __construct(Manuscript $manuscript)
    {
        $this->manuscript = $manuscript;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $actionUrl = config('app.frontend_url') . '/editor/manuscripts/' . $this->manuscript->id;

        return (new MailMessage)
            ->subject('New Manuscript Assignment')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('You have been assigned as the editor for a new manuscript.')
            ->line('**Manuscript Title:** ' . $this->manuscript->title)
            ->line('**Submission ID:** ' . $this->manuscript->submission_id)
            ->line('**Authors: <AUTHORS>
            ->line('**Submission Date:** ' . $this->manuscript->created_at->format('F j, Y'))
            ->action('View Manuscript', $actionUrl)
            ->line('Please review the manuscript and assign appropriate reviewers.')
            ->line('If you have any questions or concerns, please contact the editorial team.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'manuscript_id' => $this->manuscript->id,
            'manuscript_title' => $this->manuscript->title,
            'assigned_at' => now(),
        ];
    }
}
