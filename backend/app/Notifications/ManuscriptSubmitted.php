<?php

namespace App\Notifications;

use App\Models\Manuscript;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ManuscriptSubmitted extends Notification implements ShouldQueue
{
    use Queueable;

    protected $manuscript;

    /**
     * Create a new notification instance.
     */
    public function __construct(Manuscript $manuscript)
    {
        $this->manuscript = $manuscript;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $actionUrl = config('app.frontend_url') . '/author/submissions/' . $this->manuscript->id;

        return (new MailMessage)
            ->subject('Manuscript Submission Confirmation')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('Thank you for submitting your manuscript to JEMS Journal.')
            ->line('**Title:** ' . $this->manuscript->title)
            ->line('**Submission ID:** ' . $this->manuscript->submission_id)
            ->line('**Submitted on:** ' . $this->manuscript->created_at->format('F j, Y'))
            ->action('View Submission', $actionUrl)
            ->line('Your manuscript will undergo an initial review by our editorial team.')
            ->line('We will notify you once the peer review process begins.')
            ->line('Thank you for choosing JEMS Journal for your research publication.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'manuscript_id' => $this->manuscript->id,
            'manuscript_title' => $this->manuscript->title,
            'submission_id' => $this->manuscript->submission_id,
        ];
    }
}
