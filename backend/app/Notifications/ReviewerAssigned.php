<?php

namespace App\Notifications;

use App\Models\Manuscript;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ReviewerAssigned extends Notification implements ShouldQueue
{
    use Queueable;

    protected $manuscript;

    /**
     * Create a new notification instance.
     */
    public function __construct(Manuscript $manuscript)
    {
        $this->manuscript = $manuscript;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $actionUrl = config('app.frontend_url') . '/reviewer/manuscripts/' . $this->manuscript->id;

        return (new MailMessage)
            ->subject('New Manuscript Review Assignment')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('You have been assigned to review a new manuscript.')
            ->line('**Title:** ' . $this->manuscript->title)
            ->line('**Submission ID:** ' . $this->manuscript->submission_id)
            ->line('**Abstract:** ' . substr($this->manuscript->abstract, 0, 200) . '...')
            ->action('Start Review', $actionUrl)
            ->line('Please complete your review within the specified deadline.')
            ->line('Thank you for your contribution to the peer review process.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'manuscript_id' => $this->manuscript->id,
            'manuscript_title' => $this->manuscript->title,
            'submission_id' => $this->manuscript->submission_id,
        ];
    }
}
