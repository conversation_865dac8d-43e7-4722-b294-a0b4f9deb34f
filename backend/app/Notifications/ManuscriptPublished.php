<?php

namespace App\Notifications;

use App\Models\Manuscript;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ManuscriptPublished extends Notification implements ShouldQueue
{
    use Queueable;

    protected $manuscript;

    /**
     * Create a new notification instance.
     */
    public function __construct(Manuscript $manuscript)
    {
        $this->manuscript = $manuscript;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $actionUrl = config('app.frontend_url') . '/articles/' . $this->manuscript->id;

        return (new MailMessage)
            ->subject('Your Manuscript Has Been Published')
            ->greeting('Congratulations ' . $notifiable->name . '!')
            ->line('Your manuscript has been successfully published in our journal.')
            ->line('**Manuscript Title:** ' . $this->manuscript->title)
            ->line('**Publication Date:** ' . $this->manuscript->published_at->format('F j, Y'))
            ->line('**DOI:** ' . $this->manuscript->doi)
            ->action('View Published Article', $actionUrl)
            ->line('Your article is now available to the public and can be cited using the provided DOI.')
            ->line('We appreciate your contribution to our journal and the academic community.')
            ->line('If you have any questions or need assistance, please contact our support team.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'manuscript_id' => $this->manuscript->id,
            'manuscript_title' => $this->manuscript->title,
            'published_at' => $this->manuscript->published_at,
            'doi' => $this->manuscript->doi,
        ];
    }
}
