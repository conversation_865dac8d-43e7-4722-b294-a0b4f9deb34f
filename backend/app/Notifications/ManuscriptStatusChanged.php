<?php

namespace App\Notifications;

use App\Models\Manuscript;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ManuscriptStatusChanged extends Notification implements ShouldQueue
{
    use Queueable;

    protected $manuscript;
    protected $oldStatus;
    protected $newStatus;

    /**
     * Create a new notification instance.
     */
    public function __construct(Manuscript $manuscript, $oldStatus, $newStatus)
    {
        $this->manuscript = $manuscript;
        $this->oldStatus = $oldStatus;
        $this->newStatus = $newStatus;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $statusMessages = [
            'under_review' => [
                'subject' => 'Your manuscript is now under review',
                'greeting' => 'Good news!',
                'line1' => 'Your manuscript "' . $this->manuscript->title . '" has been assigned to reviewers and is now under review.',
                'line2' => 'We will notify you once the review process is complete.',
                'action' => 'View Submission'
            ],
            'payment_pending' => [
                'subject' => 'Your manuscript has been accepted - Payment Required',
                'greeting' => 'Congratulations!',
                'line1' => 'Your manuscript "' . $this->manuscript->title . '" has been accepted for publication.',
                'line2' => 'Please complete the payment of 75,000 CFA to proceed with publication.',
                'action' => 'Make Payment'
            ],
            'accepted' => [
                'subject' => 'Your manuscript has been accepted for publication',
                'greeting' => 'Congratulations!',
                'line1' => 'Your manuscript "' . $this->manuscript->title . '" has been accepted for publication.',
                'line2' => 'Your manuscript will be published in the next available issue.',
                'action' => 'View Submission'
            ],
            'rejected' => [
                'subject' => 'Update on your manuscript submission',
                'greeting' => 'Thank you for your submission',
                'line1' => 'After careful review, we regret to inform you that your manuscript "' . $this->manuscript->title . '" has not been accepted for publication.',
                'line2' => 'You can view the reviewer feedback in your dashboard.',
                'action' => 'View Feedback'
            ],
            'revisions_required' => [
                'subject' => 'Revisions required for your manuscript',
                'greeting' => 'Action Required',
                'line1' => 'The reviewers have requested revisions for your manuscript "' . $this->manuscript->title . '".',
                'line2' => 'Please review the feedback and submit your revised manuscript.',
                'action' => 'View Feedback'
            ],
            'published' => [
                'subject' => 'Your manuscript has been published',
                'greeting' => 'Congratulations!',
                'line1' => 'Your manuscript "' . $this->manuscript->title . '" has been published.',
                'line2' => 'Your work is now available to the academic community.',
                'action' => 'View Publication'
            ]
        ];

        $message = $statusMessages[$this->newStatus] ?? [
            'subject' => 'Update on your manuscript submission',
            'greeting' => 'Hello',
            'line1' => 'Your manuscript "' . $this->manuscript->title . '" status has been updated.',
            'line2' => 'Please check your dashboard for more details.',
            'action' => 'View Submission'
        ];

        $actionUrl = config('app.frontend_url') . '/author/submissions/' . $this->manuscript->id;

        return (new MailMessage)
            ->subject($message['subject'])
            ->greeting($message['greeting'] . ' ' . $notifiable->name . '!')
            ->line($message['line1'])
            ->line($message['line2'])
            ->action($message['action'], $actionUrl)
            ->line('Submission ID: ' . $this->manuscript->submission_id)
            ->line('Thank you for choosing JEPS Journal for your research publication.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'manuscript_id' => $this->manuscript->id,
            'manuscript_title' => $this->manuscript->title,
            'old_status' => $this->oldStatus,
            'new_status' => $this->newStatus,
        ];
    }
}
