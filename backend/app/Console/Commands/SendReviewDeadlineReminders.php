<?php

namespace App\Console\Commands;

use App\Models\Manuscript;
use App\Notifications\ReviewDeadlineReminder;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class SendReviewDeadlineReminders extends Command
{
    protected $signature = 'reviews:send-deadline-reminders';
    protected $description = 'Send reminders to reviewers about approaching review deadlines';

    public function handle()
    {
        $manuscripts = Manuscript::where('status', 'under_review')
            ->whereHas('reviewers', function ($query) {
                $query->whereNull('completed_at');
            })
            ->with(['reviewers' => function ($query) {
                $query->whereNull('completed_at');
            }])
            ->get();

        foreach ($manuscripts as $manuscript) {
            foreach ($manuscript->reviewers as $reviewer) {
                $assignedAt = Carbon::parse($reviewer->pivot->assigned_at);
                $deadline = $assignedAt->addDays(14); // 2 weeks deadline
                $daysRemaining = now()->diffInDays($deadline, false);

                // Send reminders at 7 days, 3 days, and 1 day before deadline
                if (in_array($daysRemaining, [7, 3, 1])) {
                    $reviewer->notify(new ReviewDeadlineReminder($manuscript, $daysRemaining));
                    $this->info("Sent reminder to reviewer {$reviewer->id} for manuscript {$manuscript->id} with {$daysRemaining} days remaining");
                }
            }
        }

        $this->info('Review deadline reminders sent successfully');
    }
}
