<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Manuscript;

class SetRevisionStatus extends Command
{
    protected $signature = 'manuscript:set-revision {id}';
    protected $description = 'Set a manuscript to revisions_required status for testing';

    public function handle()
    {
        $id = $this->argument('id');
        $manuscript = Manuscript::find($id);

        if (!$manuscript) {
            $this->error("Manuscript with ID {$id} not found");
            return 1;
        }

        $manuscript->update(['status' => 'revisions_required']);

        $this->info("Updated manuscript '{$manuscript->title}' (ID: {$id}) to revisions_required status");
        $this->info("Author: {$manuscript->author->name} ({$manuscript->author->email})");
        $this->info("You can now test the resubmission feature!");

        return 0;
    }
}
