<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\File;

class ClearAllCaches extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:clear-all {--force : Force clear all caches without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all application caches (application, config, route, view, permission)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (!$this->option('force') && !$this->confirm('This will clear ALL caches. Continue?')) {
            $this->info('Cache clearing cancelled.');
            return;
        }

        $this->info('🧹 Clearing all application caches...');

        $hasErrors = false;

        try {
            // Clear application cache with fallback
            $this->info('Clearing application cache...');
            try {
                Cache::flush();
                $this->line('✅ Cache store flushed');
            } catch (\Exception $e) {
                $this->warn('⚠️  Cache store flush failed: ' . $e->getMessage());
                $this->info('Attempting manual cache directory clearing...');
                $this->clearCacheDirectories();
            }

            Artisan::call('cache:clear');
            $this->line('✅ Application cache cleared');

        } catch (\Exception $e) {
            $this->error('❌ Error clearing application cache: ' . $e->getMessage());
            $hasErrors = true;
        }

        try {
            // Clear config cache
            $this->info('Clearing config cache...');
            Artisan::call('config:clear');
            $this->line('✅ Config cache cleared');
        } catch (\Exception $e) {
            $this->error('❌ Error clearing config cache: ' . $e->getMessage());
            $hasErrors = true;
        }

        try {
            // Clear route cache
            $this->info('Clearing route cache...');
            Artisan::call('route:clear');
            $this->line('✅ Route cache cleared');
        } catch (\Exception $e) {
            $this->error('❌ Error clearing route cache: ' . $e->getMessage());
            $hasErrors = true;
        }

        try {
            // Clear view cache
            $this->info('Clearing view cache...');
            Artisan::call('view:clear');
            $this->line('✅ View cache cleared');
        } catch (\Exception $e) {
            $this->error('❌ Error clearing view cache: ' . $e->getMessage());
            $hasErrors = true;
        }

        try {
            // Clear compiled services and packages
            $this->info('Clearing compiled cache...');
            Artisan::call('clear-compiled');
            $this->line('✅ Compiled cache cleared');
        } catch (\Exception $e) {
            $this->error('❌ Error clearing compiled cache: ' . $e->getMessage());
            $hasErrors = true;
        }

        // Clear permission cache (if Spatie Permission is used)
        try {
            $this->info('Clearing permission cache...');
            Artisan::call('permission:cache-reset');
            $this->line('✅ Permission cache cleared');
        } catch (\Exception $e) {
            $this->warn('⚠️  Permission cache clearing skipped: ' . $e->getMessage());
        }

        // Clear opcache if enabled
        try {
            if (function_exists('opcache_reset') && opcache_get_status()) {
                $this->info('Clearing OPcache...');
                opcache_reset();
                $this->line('✅ OPcache cleared');
            }
        } catch (\Exception $e) {
            $this->warn('⚠️  OPcache clearing skipped: ' . $e->getMessage());
        }

        $this->newLine();

        if ($hasErrors) {
            $this->warn('⚠️  Some caches cleared with errors. Check output above for details.');
            return 1;
        } else {
            $this->info('🎉 All caches cleared successfully!');
            return 0;
        }
    }

    /**
     * Manually clear cache directories when database is unavailable
     */
    private function clearCacheDirectories(): void
    {
        $cacheDirectories = [
            storage_path('framework/cache/data'),
            storage_path('framework/sessions'),
            storage_path('framework/views'),
        ];

        foreach ($cacheDirectories as $directory) {
            if (File::exists($directory)) {
                try {
                    File::cleanDirectory($directory);
                    $this->line("✅ Cleared cache directory: {$directory}");
                } catch (\Exception $e) {
                    $this->warn("⚠️  Failed to clear directory {$directory}: " . $e->getMessage());
                }
            }
        }
    }
}
