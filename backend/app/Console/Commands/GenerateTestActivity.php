<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Manuscript;
use App\Models\Review;
use App\Models\Payment;
use Spatie\Activitylog\Models\Activity;

class GenerateTestActivity extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'activity:generate {--count=20 : Number of test activities to generate}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate test activity logs for demonstration purposes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $count = $this->option('count');

        $this->info("Generating {$count} test activity logs...");

        // Get some existing models to work with
        $users = User::limit(10)->get();
        $manuscripts = Manuscript::limit(5)->get();

        if ($users->isEmpty()) {
            $this->error('No users found. Please create some users first.');
            return 1;
        }

        $events = ['created', 'updated', 'deleted', 'login', 'logout'];
        $descriptions = [
            'User login attempt',
            'Manuscript submitted for review',
            'Review completed',
            'Payment processed',
            'User profile updated',
            'Manuscript status changed',
            'System backup created',
            'Cache cleared',
            'Email notification sent',
            'File uploaded'
        ];

        for ($i = 0; $i < $count; $i++) {
            $user = $users->random();
            $event = collect($events)->random();
            $description = collect($descriptions)->random();

            // Create activity manually using the Activity model
            Activity::create([
                'log_name' => 'default',
                'description' => $description,
                'subject_type' => collect([User::class, Manuscript::class])->random(),
                'subject_id' => rand(1, 100),
                'event' => $event,
                'causer_type' => User::class,
                'causer_id' => $user->id,
                'properties' => json_encode([
                    'attributes' => [
                        'test' => true,
                        'generated_at' => now()->toISOString()
                    ]
                ]),
                'created_at' => now()->subMinutes(rand(1, 1440)), // Random time in last 24 hours
                'updated_at' => now()
            ]);

            if ($i % 5 === 0) {
                $this->info("Generated " . ($i + 1) . " activities...");
            }
        }

        $this->info("Successfully generated {$count} test activity logs!");

        return 0;
    }
}
