<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Manuscript;
use App\Models\User;

try {
    // Find or create a test author
    $author = User::where('email', '<EMAIL>')->first();
    if (!$author) {
        $author = User::create([
            'name' => 'Test Author',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'author',
            'affiliation' => 'Test University',
            'country' => 'Test Country',
            'email_verified_at' => now(),
        ]);
        echo "Created test author with email: <EMAIL>\n";
    }

    // Find a manuscript to update or create one
    $manuscript = Manuscript::where('author_id', $author->id)->first();

    if (!$manuscript) {
        // Create a test manuscript
        $manuscript = Manuscript::create([
            'title' => 'Test Manuscript for Revisions',
            'abstract' => 'This is a test manuscript that requires revisions.',
            'keywords' => ['test', 'revision', 'manuscript'],
            'language' => 'en',
            'file_path' => 'manuscripts/test-file.pdf',
            'author_id' => $author->id,
            'issue_id' => 1, // Assuming issue 1 exists
            'authors' => ['Test Author'],
            'status' => 'revisions_required',
        ]);
        echo "Created new test manuscript\n";
    } else {
        // Update existing manuscript
        $manuscript->update([
            'status' => 'revisions_required',
            'title' => 'Test Manuscript for Revisions (Updated)',
        ]);
        echo "Updated existing manuscript\n";
    }

    echo "\n=== TEST SETUP COMPLETE ===\n";
    echo "Manuscript ID: {$manuscript->id}\n";
    echo "Manuscript Title: {$manuscript->title}\n";
    echo "Author: {$author->name} ({$author->email})\n";
    echo "Status: {$manuscript->status}\n";
    echo "\nTo test:\n";
    echo "1. Login with email: <EMAIL>, password: password\n";
    echo "2. Navigate to: /author/submissions/{$manuscript->id}\n";
    echo "3. You should see the 'Resubmit' button in the revisions required section\n";
    echo "\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}