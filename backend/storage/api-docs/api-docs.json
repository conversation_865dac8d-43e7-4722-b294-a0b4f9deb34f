{"openapi": "3.0.0", "info": {"title": "Jeps Journal API", "description": "API documentation for the Jeps Journal system.", "contact": {"email": "<EMAIL>"}, "version": "1.0.0"}, "paths": {"/api/v1/login": {"post": {"tags": ["Authentication"], "summary": "Login a user", "operationId": "c6ee041b95bf1c5e10daadce03e324a9", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "example": "password123"}}, "type": "object"}}}}, "responses": {"200": {"description": "User logged in successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"properties": {"user": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "role": {"type": "string", "example": "author"}}, "type": "object"}, "token": {"type": "string", "example": "1|XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"}}, "type": "object"}, "message": {"type": "string", "example": "User logged in successfully."}}, "type": "object"}}}}, "401": {"description": "Invalid credentials", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Invalid credentials"}}, "type": "object"}}}}}}}, "/api/v1/logout": {"post": {"tags": ["Authentication"], "summary": "Logout a user", "operationId": "f66d51be02cefe95aabfbd4ad6829be1", "responses": {"200": {"description": "Logged out successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Logged out successfully"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/register": {"post": {"tags": ["Authentication"], "summary": "Register a new user", "operationId": "18235ea6963e661c49bf381065779d6a", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["name", "email", "password", "role"], "properties": {"name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "example": "password123"}, "role": {"type": "string", "example": "author"}, "affiliation": {"type": "string", "example": "University of Example"}, "country": {"type": "string", "example": "Cameroon"}, "expertise": {"type": "array", "items": {"type": "string"}, "example": ["Computer Science", "Engineering"]}}, "type": "object"}}}}, "responses": {"201": {"description": "User registered successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"properties": {"user": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "role": {"type": "string", "example": "author"}, "affiliation": {"type": "string", "example": "University of Example"}, "country": {"type": "string", "example": "Cameroon"}, "expertise": {"type": "array", "items": {"type": "string"}}}, "type": "object"}, "token": {"type": "string", "example": "1|XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"}}, "type": "object"}, "message": {"type": "string", "example": "User registered successfully."}}, "type": "object"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "errors": {"type": "object"}}, "type": "object"}}}}}}}, "/api/verify-email": {"post": {"summary": "<PERSON><PERSON><PERSON>", "description": "Verify the email address of the user", "operationId": "d3036b98d95771fe5f4134fb52e61416", "requestBody": {"content": {"application/json": {"schema": {"properties": {"id": {"type": "string", "example": "1"}, "hash": {"type": "string", "example": "hash"}}, "type": "object"}}}}, "responses": {"200": {"description": "Email verified successfully"}, "400": {"description": "Invalid verification link"}}}}, "/api/v1/issues": {"get": {"tags": ["Issues"], "summary": "List issues", "operationId": "16dc7cf0c7e1b6aff132457b538235a9", "parameters": [{"name": "volume_id", "in": "query", "description": "Filter by volume ID", "required": false, "schema": {"type": "integer"}}, {"name": "type", "in": "query", "description": "Filter by type (January or June)", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "List of issues"}}, "security": [{"sanctum": []}]}, "post": {"tags": ["Issues"], "summary": "Create a new issue", "operationId": "0f079f55b11d08b188aee7fc0bc086c9", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["volume_id", "type", "submission_deadline"], "properties": {"volume_id": {"type": "integer", "example": 1}, "type": {"type": "string", "example": "January"}, "submission_deadline": {"type": "string", "format": "date", "example": "2025-05-31"}}, "type": "object"}}}}, "responses": {"201": {"description": "Issue created successfully"}, "422": {"description": "Validation error or duplicate issue type"}}, "security": [{"sanctum": []}]}}, "/api/v1/issues/{id}": {"get": {"tags": ["Issues"], "summary": "Get a specific issue", "operationId": "5a7f315c35dc02b40b8bb902da73ddc1", "parameters": [{"name": "id", "in": "path", "description": "Issue ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Issue details"}, "404": {"description": "Issue not found"}}, "security": [{"sanctum": []}]}, "put": {"tags": ["Issues"], "summary": "Update an existing issue", "operationId": "2b1fac48de713c7c91d404d745603a50", "parameters": [{"name": "id", "in": "path", "description": "Issue ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"published": {"type": "boolean", "example": true}, "publication_date": {"type": "string", "format": "date", "example": "2025-06-15"}, "submission_deadline": {"type": "string", "format": "date", "example": "2025-05-30"}}, "type": "object"}}}}, "responses": {"200": {"description": "Issue updated successfully"}, "404": {"description": "Issue not found"}}, "security": [{"sanctum": []}]}, "delete": {"tags": ["Issues"], "summary": "Delete an issue", "operationId": "f8691ee31ffb780ab5c1f6d6775359df", "parameters": [{"name": "id", "in": "path", "description": "Issue ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Issue deleted successfully"}, "404": {"description": "Issue not found"}}, "security": [{"sanctum": []}]}}, "/api/v1/manuscripts": {"get": {"tags": ["Manuscripts"], "summary": "List manuscripts", "operationId": "697ed5859f1cbcd33a3df4c0e5512090", "parameters": [{"name": "issue_id", "in": "query", "description": "Filter by issue ID", "required": false, "schema": {"type": "integer"}}, {"name": "status", "in": "query", "description": "Filter by manuscript status", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "List of manuscripts"}}, "security": [{"sanctum": []}]}, "post": {"tags": ["Manuscripts"], "summary": "Submit a new manuscript", "operationId": "0bc5b022787639f1c94857d622c7e3d1", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"required": ["title", "abstract", "keywords", "language", "file", "issue_id"], "properties": {"title": {"type": "string", "example": "Research on AI"}, "abstract": {"type": "string", "example": "This study explores..."}, "keywords": {"type": "array", "items": {"type": "string", "example": "AI"}}, "translated_abstract": {"type": "string", "example": "Ceci est une étude..."}, "language": {"type": "string", "enum": ["en", "fr"]}, "file": {"type": "file", "format": "binary"}, "issue_id": {"type": "integer", "example": 2}}, "type": "object"}}}}, "responses": {"201": {"description": "Manuscript created successfully"}, "422": {"description": "Validation error or submission deadline passed"}}, "security": [{"sanctum": []}]}}, "/api/v1/manuscripts/{id}": {"get": {"tags": ["Manuscripts"], "summary": "Get a specific manuscript", "operationId": "ca8982a956bab052b09993db92c51481", "parameters": [{"name": "id", "in": "path", "description": "Manuscript ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Manuscript details"}, "404": {"description": "Manuscript not found"}}, "security": [{"sanctum": []}]}, "put": {"tags": ["Manuscripts"], "summary": "Update a submitted manuscript", "operationId": "a840982060689d2534a0b81bb7a50b1e", "parameters": [{"name": "id", "in": "path", "description": "Manuscript ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"properties": {"title": {"type": "string", "example": "Updated research title"}, "abstract": {"type": "string", "example": "Updated abstract..."}, "keywords": {"type": "array", "items": {"type": "string", "example": "Innovation"}}, "translated_abstract": {"type": "string", "example": "Abstract traduit..."}, "file": {"type": "file", "format": "binary"}}, "type": "object"}}}}, "responses": {"200": {"description": "Manuscript updated successfully"}, "422": {"description": "Cannot modify after review started"}}, "security": [{"sanctum": []}]}}, "/api/v1/manuscripts/{manuscript_id}/reviews": {"get": {"tags": ["Reviews"], "summary": "List reviews for a manuscript", "operationId": "542319ee552072141547dbe186383a4f", "parameters": [{"name": "manuscript_id", "in": "path", "description": "Manuscript ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "List of reviews for the manuscript"}}, "security": [{"sanctum": []}]}, "post": {"tags": ["Reviews"], "summary": "Submit a review for a manuscript", "operationId": "b02e60f03c92baf21e425805ed1dc092", "parameters": [{"name": "manuscript_id", "in": "path", "description": "Manuscript ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["recommendation", "feedback"], "properties": {"recommendation": {"type": "string", "enum": ["accept", "minor", "major", "reject"]}, "feedback": {"type": "string"}, "confidential_comments": {"type": "string", "nullable": true}}, "type": "object"}}}}, "responses": {"200": {"description": "Review submitted successfully"}, "422": {"description": "Review already submitted or validation errors"}}, "security": [{"sanctum": []}]}}, "/api/v1/manuscripts/{manuscript_id}/reviews/{id}": {"get": {"tags": ["Reviews"], "summary": "Get a specific review for a manuscript", "operationId": "b91e546a911bf233c85ad35bb1aa6a54", "parameters": [{"name": "manuscript_id", "in": "path", "description": "Manuscript ID", "required": true, "schema": {"type": "integer"}}, {"name": "id", "in": "path", "description": "Review ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Review details"}, "404": {"description": "Review not found"}}, "security": [{"sanctum": []}]}}, "/api/v1/users": {"get": {"tags": ["Users"], "summary": "List users", "operationId": "3ab81e3f9b24b4f36ab08d72349ac632", "parameters": [{"name": "role", "in": "query", "description": "Filter users by role", "required": false, "schema": {"type": "string", "enum": ["author", "reviewer", "editor", "admin"]}}], "responses": {"200": {"description": "List of users"}}, "security": [{"sanctum": []}]}}, "/api/v1/users/{id}": {"get": {"tags": ["Users"], "summary": "Get user details", "operationId": "daaf7a6169ee9348c3fddb07778598ff", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "User details"}, "404": {"description": "User not found"}}, "security": [{"sanctum": []}]}, "put": {"tags": ["Users"], "summary": "Update user details", "operationId": "59e98fba99e974815e27979dd58e2175", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["name", "email"], "properties": {"name": {"type": "string", "maxLength": 255}, "email": {"type": "string", "format": "email", "maxLength": 255}, "role": {"type": "string", "enum": ["author", "reviewer", "editor", "admin"]}, "affiliation": {"type": "string", "maxLength": 255, "nullable": true}, "country": {"type": "string", "maxLength": 255, "nullable": true}, "expertise": {"type": "array", "items": {"type": "string", "maxLength": 255}, "nullable": true}}, "type": "object"}}}}, "responses": {"200": {"description": "User details updated"}, "422": {"description": "Validation errors"}}, "security": [{"sanctum": []}]}, "delete": {"tags": ["Users"], "summary": "Delete a user", "operationId": "c30c64b8b7730b65c7714c9af4a6f5fa", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "User deleted successfully"}, "404": {"description": "User not found"}}, "security": [{"sanctum": []}]}}, "/api/v1/volumes": {"get": {"tags": ["Volumes"], "summary": "List volumes", "operationId": "187348cd98cb8acccf5d4253a99cce00", "responses": {"200": {"description": "List of volumes"}}, "security": [{"sanctum": []}]}, "post": {"tags": ["Volumes"], "summary": "Create a new volume", "operationId": "9ab1cab9df9b317dc2683dfacd28c626", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["number", "year"], "properties": {"number": {"type": "integer"}, "year": {"description": "4-digit year", "type": "integer", "format": "int32"}}, "type": "object"}}}}, "responses": {"201": {"description": "Volume created successfully"}, "422": {"description": "Validation errors"}}, "security": [{"sanctum": []}]}}, "/api/v1/volumes/{id}": {"get": {"tags": ["Volumes"], "summary": "Get volume details", "operationId": "c70628559c2c0f7887c4bf5569275dd1", "parameters": [{"name": "id", "in": "path", "description": "Volume ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Volume details"}, "404": {"description": "Volume not found"}}, "security": [{"sanctum": []}]}, "put": {"tags": ["Volumes"], "summary": "Update volume details", "operationId": "60f88b889b85b4940f3b72a5c372349d", "parameters": [{"name": "id", "in": "path", "description": "Volume ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"published": {"type": "boolean"}, "publication_date": {"type": "string", "format": "date"}}, "type": "object"}}}}, "responses": {"200": {"description": "Volume details updated"}, "422": {"description": "Validation errors"}}, "security": [{"sanctum": []}]}, "delete": {"tags": ["Volumes"], "summary": "Delete a volume", "operationId": "6ef9890a53a4bbef8a8c5fbbc97b9f13", "parameters": [{"name": "id", "in": "path", "description": "Volume ID", "required": true, "schema": {"type": "integer"}}], "responses": {"204": {"description": "Volume deleted successfully"}, "404": {"description": "Volume not found"}}, "security": [{"sanctum": []}]}}}, "tags": [{"name": "Authentication", "description": "Authentication"}, {"name": "Issues", "description": "Issues"}, {"name": "Manuscripts", "description": "Manuscripts"}, {"name": "Reviews", "description": "Reviews"}, {"name": "Users", "description": "Users"}, {"name": "Volumes", "description": "Volumes"}], "components": {"securitySchemes": {"sanctum": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Enter token in format (Bearer <token>)", "name": "Authorization", "in": "header"}}}}