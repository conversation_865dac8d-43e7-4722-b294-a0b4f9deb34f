<?php


use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\Auth\PasswordResetLinkController;
use App\Http\Controllers\Auth\NewPasswordController;
use App\Http\Controllers\Auth\VerifyEmailController;
use App\Http\Controllers\IssueController;
use App\Http\Controllers\ManuscriptController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ReviewController;
use App\Http\Controllers\VolumeController;
use App\Http\Controllers\PlagiarismController;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\RateLimiter;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Auth\SocialAuthController;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Session\Middleware\StartSession;
use App\Http\Controllers\UserController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\Admin\ActivityLogController;
use App\Http\Controllers\Admin\SystemMetricsController;
use App\Http\Controllers\Admin\ContentController;
use App\Http\Controllers\Api\NotificationPreferenceController;
use App\Http\Controllers\Api\UserDeviceController;

RateLimiter::for('api', function (Request $request) {
    return Limit::perMinute(60)->by(optional($request->user())->id ?: $request->ip());
});

// Public routes with caching
Route::middleware('cache.control:static')->group(function () {
    Route::get('/volumes', [VolumeController::class, 'index']);
    Route::get('/volumes/{volume}', [VolumeController::class, 'show']);
    Route::get('/issues', [IssueController::class, 'index']);
});

Route::middleware('cache.control:dynamic')->group(function () {
    Route::get('/issues/current', [IssueController::class, 'getCurrentIssue']);
    Route::get('/issues/{issue}', [IssueController::class, 'show']);
});

Route::middleware('guest')->group(function () {
    Route::post('/register', [RegisteredUserController::class, 'store']);
    Route::post('/login', [LoginController::class, 'store']);
    Route::post('/forgot-password', [PasswordResetLinkController::class, 'store']);
    Route::post('/reset-password', [NewPasswordController::class, 'store']);
    Route::get('/auth/verify-email', [VerifyEmailController::class, '__invoke'])
        ->middleware(['throttle:100,1'])
        ->name('verification.verify');
    Route::post('/auth/resend-verification', [VerifyEmailController::class, 'resend']);
});

// Social Authentication Routes

Route::middleware([
    EncryptCookies::class,
    AddQueuedCookiesToResponse::class,
    StartSession::class,
    \Illuminate\View\Middleware\ShareErrorsFromSession::class,
    \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
])->group(function () {
    Route::get('/auth/google', [SocialAuthController::class, 'redirectToGoogle']);
    Route::get('/auth/google/callback', [SocialAuthController::class, 'handleGoogleCallback']);

    Route::get('/auth/orcid', [SocialAuthController::class, 'redirectToOrcid'])->middleware('guest');
    Route::get('/auth/orcid/callback', [SocialAuthController::class, 'handleOrcidCallback'])->middleware('guest');

});


Route::middleware('auth:sanctum')->group(function () {
    Route::post('/logout', [LoginController::class, 'destroy']);
    Route::get('/user', function(Request $request) {
        return new \App\Http\Resources\UserResource($request->user());
    });
    Route::put('/user', [UserController::class, 'updateProfile']);
    Route::post('/auth/refresh', [LoginController::class, 'refresh']);

    // Manuscript routes - specific routes first
    Route::get('/manuscripts/assigned-reviews', [ManuscriptController::class, 'getAssignedReviews']);
    Route::get('/author/manuscripts', [ManuscriptController::class, 'getAuthorManuscripts']);
    Route::post('/manuscripts/{manuscript}/resubmit', [ManuscriptController::class, 'resubmit']);
    Route::get('/manuscripts/{manuscript}/reviews', [ReviewController::class, 'index']);
    Route::post('/manuscripts/{manuscript}/reviews', [ReviewController::class, 'store']);
    Route::get('/manuscripts/{manuscript}/reviews/{review}', [ReviewController::class, 'show']);
    Route::put('/manuscripts/{manuscript}/reviews/{review}', [ReviewController::class, 'update']);
    Route::post('/manuscripts/{manuscript}/reviews/{review}/complete', [ReviewController::class, 'markAsCompleted']);
    Route::delete('/manuscripts/{manuscript}/reviews/{review}/complete', [ReviewController::class, 'markAsInProgress']);
    Route::post('/manuscripts/{manuscript}/reviewers', [ReviewController::class, 'assignReviewers']);
    Route::put('/manuscripts/{manuscript}/status', [ManuscriptController::class, 'updateStatus']);
    Route::post('/manuscripts/{manuscript}/publish', [ManuscriptController::class, 'publish']);
    Route::post('/manuscripts/{manuscript}/payment', [PaymentController::class, 'store']);
    Route::get('/manuscripts/{manuscript}/payment', [PaymentController::class, 'show']);
    Route::put('/manuscripts/{manuscript}/payment', [PaymentController::class, 'update']);
    Route::delete('/manuscripts/{manuscript}/payment', [PaymentController::class, 'destroy']);
    Route::get('/manuscripts/{manuscript}/download', [ManuscriptController::class, 'download'])->name('api.manuscripts.download');
    Route::get('/manuscripts/{manuscript}/versions/{version}/download', [ManuscriptController::class, 'downloadVersion'])->name('api.manuscripts.versions.download');

    // Plagiarism routes
    Route::post('/plagiarism/check', [PlagiarismController::class, 'check']);
    Route::get('/plagiarism/result/{checkId}', [PlagiarismController::class, 'getResult']);
    Route::get('/plagiarism/history/{manuscriptId}', [PlagiarismController::class, 'getHistory']);

    // Then the resource route
    Route::apiResource('manuscripts', ManuscriptController::class)
        ->except(['destroy']);

    // Review routes
    Route::get('/reviewers', [UserController::class, 'reviewers']);

    // Volume and Issue routes - accessible by both admin and editor
    Route::apiResource('volumes', VolumeController::class)->except(['index', 'show']);
    Route::apiResource('issues', IssueController::class)->except(['index', 'show']);

    // Additional issue routes
    Route::get('/volumes/{volume_id}/issues', [IssueController::class, 'getVolumeIssues']);
    Route::put('/issues/{issue}/publish', [IssueController::class, 'publish']);
    Route::put('/issues/{issue}/set-current', [IssueController::class, 'setCurrent']);

    // Admin routes
    Route::prefix('admin')->middleware('admin')->group(function () {
        Route::get('/dashboard-stats', [AdminController::class, 'getDashboardStats']);
        Route::get('/payments', [AdminController::class, 'getPayments']);
        Route::get('/payment-stats', [AdminController::class, 'getPaymentStats']);
        Route::get('/manuscripts', [AdminController::class, 'getManuscripts']);
        Route::get('/manuscript-stats', [AdminController::class, 'getManuscriptStats']);
        Route::put('/manuscripts/{id}/status', [AdminController::class, 'updateManuscriptStatus']);
        Route::delete('/manuscripts/{id}', [AdminController::class, 'deleteManuscript']);
        Route::get('/reviews', [AdminController::class, 'getReviews']);
        Route::get('/review-stats', [AdminController::class, 'getReviewStats']);
        Route::delete('/reviews/{id}', [AdminController::class, 'deleteReview']);

        // Activity Log routes (replacing system logs)
        Route::get('/activity-logs', [ActivityLogController::class, 'index']);
        Route::get('/activity-logs/stats', [ActivityLogController::class, 'stats']);
        Route::delete('/activity-logs/clear', [ActivityLogController::class, 'clear']);
        Route::get('/activity-logs/export', [ActivityLogController::class, 'export']);

        // System Metrics routes for graphs and monitoring
        Route::get('/metrics', [SystemMetricsController::class, 'getMetrics']);
        Route::get('/metrics/resources', [SystemMetricsController::class, 'getSystemResources']);
        Route::get('/metrics/activity', [SystemMetricsController::class, 'getActivityAnalytics']);

        // Content Management routes (Admin only - full access)
        Route::get('/content', [ContentController::class, 'index']);
        Route::post('/content', [ContentController::class, 'store']);
        Route::get('/content/stats', [ContentController::class, 'getStats']);
        Route::post('/content/bulk-action', [ContentController::class, 'bulkAction']);
        Route::get('/content/{id}', [ContentController::class, 'show']);
        Route::put('/content/{id}', [ContentController::class, 'update']);
        Route::delete('/content/{id}', [ContentController::class, 'destroy']);

        // Keep existing logs endpoints for backward compatibility
        Route::get('/logs', [AdminController::class, 'getSystemLogs']);
        Route::delete('/logs/clear', [AdminController::class, 'clearLogs']);
        Route::get('/logs/export', [AdminController::class, 'exportLogs']);
        Route::get('/analytics', [AdminController::class, 'getSystemAnalytics']);
    });

    // Editor Content Management routes (for announcements, editorials, etc.)
    Route::prefix('editor')->middleware('auth:sanctum')->group(function () {
        // Content Management routes for editors (check role in controller)
        Route::get('/content', [ContentController::class, 'index']);
        Route::post('/content', [ContentController::class, 'store']);
        Route::get('/content/stats', [ContentController::class, 'getStats']);
        Route::post('/content/bulk-action', [ContentController::class, 'bulkAction']);
        Route::get('/content/{id}', [ContentController::class, 'show']);
        Route::put('/content/{id}', [ContentController::class, 'update']);
        Route::delete('/content/{id}', [ContentController::class, 'destroy']);
    });

    // Settings routes
    Route::get('/settings', [SettingsController::class, 'index']);
    Route::get('/settings/group/{group}', [SettingsController::class, 'getByGroup']);
    Route::get('/settings/public', [SettingsController::class, 'getPublic']);
    Route::post('/settings', [SettingsController::class, 'store']);
    Route::put('/settings', [SettingsController::class, 'update']);
    Route::delete('/settings/{key}', [SettingsController::class, 'destroy']);

    // Manuscript routes
    Route::prefix('manuscripts')->group(function () {
        Route::post('direct-publish', [ManuscriptController::class, 'directPublish']);
        // ... other manuscript routes ...
    });

    // User routes
    Route::get('/users', [UserController::class, 'index']);
    Route::get('/users/authors', [UserController::class, 'authors']);
    Route::get('/users/{user}', [UserController::class, 'show']);
    Route::put('/users/{user}', [UserController::class, 'update']);
    Route::delete('/users/{user}', [UserController::class, 'destroy']);

    // Notification Preferences
    Route::get('/notification-preferences', [NotificationPreferenceController::class, 'show']);
    Route::put('/notification-preferences', [NotificationPreferenceController::class, 'update']);

    // Device Management
    Route::get('/user/devices', [UserDeviceController::class, 'index']);
    Route::post('/user/devices/{device}/trust', [UserDeviceController::class, 'trust']);
    Route::post('/user/devices/{device}/untrust', [UserDeviceController::class, 'untrust']);
    Route::delete('/user/devices/{device}', [UserDeviceController::class, 'destroy']);
});

Route::middleware(['auth:sanctum', 'verified'])->group(function () {
    // Payment routes only (manuscript routes are already defined above)
    Route::post('/manuscripts/{manuscript}/payments', [PaymentController::class, 'store']);
    Route::get('/manuscripts/{manuscript}/payments', [PaymentController::class, 'show']);
});

Route::middleware('cache.control:static')->group(function () {
    Route::get('/published-manuscripts', [ManuscriptController::class, 'getPublishedManuscripts']);
    Route::get('manuscripts/{manuscript}', [ManuscriptController::class, 'show']);
    Route::get('/manuscripts/{manuscript}/download', [ManuscriptController::class, 'download'])->name('api.manuscripts.download');
    Route::get('/manuscripts/by-issue/{issue}', [ManuscriptController::class, 'getManuscriptsByIssue']);
});

Route::get('/', function () {
    return response()->json([
        'message' => 'Welcome to the API',
        'version' => '1.0.0',
        'date' => now()->toDateTimeString(),
    ]);
});
Route::fallback(function () {
    return response()->json([
        'message' => 'Not Found',
    ], 404);
});
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now()->toDateTimeString(),
    ]);
});
Route::get('/oauth2-callback', function () {
    return response()->json(['error' => 'OAuth2 not configured'], 400);
})->name('l5-swagger.default.oauth2_callback');

// Public content routes (accessible without authentication)
Route::middleware('cache.control:static')->group(function () {
    Route::get('/content/{type}', [ContentController::class, 'getByType'])->name('content.public');
});
