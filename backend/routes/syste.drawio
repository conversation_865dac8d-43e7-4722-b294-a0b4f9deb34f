<mxfile host="65bd71144e">
    <diagram id="xRxTdDt-fKL-FNBRkPpd" name="Page-1">
        <mxGraphModel dx="1375" dy="687" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1100" pageHeight="850" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="4" value="" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="232" y="50" width="400" height="640" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="login" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="332" y="90" width="170" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="Add Sale" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="342" y="180" width="170" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="print receipt" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="282" y="260" width="170" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="logout" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="352" y="370" width="170" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="10" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="9" target="5">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="12" style="edgeStyle=none;html=1;" edge="1" parent="1" source="9" target="7">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="Admin" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;" vertex="1" parent="1">
                    <mxGeometry x="2" y="240" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="11" style="edgeStyle=none;html=1;entryX=0.041;entryY=0.4;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="9" target="6">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="13" style="edgeStyle=none;html=1;entryX=0.088;entryY=0.733;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="9" target="8">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="print receipt" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="472" y="225" width="130" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="System&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;" vertex="1" parent="1">
                    <mxGeometry x="792" y="210" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="18" style="edgeStyle=none;html=1;entryX=0.962;entryY=0.733;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="17" target="16">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="26" style="edgeStyle=none;html=1;" edge="1" parent="1" source="19">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="810" y="480" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" value="System&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;" vertex="1" parent="1">
                    <mxGeometry x="670" y="450" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="30" style="edgeStyle=none;html=1;curved=0;rounded=0;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="849.5476190476193" y="490" as="sourcePoint"/>
                        <mxPoint x="999.5" y="490" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="27" value="Frontend" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;dropTarget=0;collapsible=0;recursiveResize=0;outlineConnect=0;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" vertex="1" parent="1">
                    <mxGeometry x="800" y="400" width="100" height="300" as="geometry"/>
                </mxCell>
                <mxCell id="31" style="edgeStyle=none;html=1;curved=0;rounded=0;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="999.5476190476188" y="510" as="sourcePoint"/>
                        <mxPoint x="1149.5" y="510" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="35" style="edgeStyle=none;html=1;curved=0;rounded=0;" edge="1" parent="1" source="28" target="27">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="36" value="return user session" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="35">
                    <mxGeometry x="0.3211" y="-3" relative="1" as="geometry">
                        <mxPoint x="27" y="-7" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="28" value="Backend" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;dropTarget=0;collapsible=0;recursiveResize=0;outlineConnect=0;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" vertex="1" parent="1">
                    <mxGeometry x="960" y="400" width="90" height="300" as="geometry"/>
                </mxCell>
                <mxCell id="33" style="edgeStyle=none;html=1;curved=0;rounded=0;" edge="1" parent="1" source="29" target="28">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="29" value="data" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;dropTarget=0;collapsible=0;recursiveResize=0;outlineConnect=0;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" vertex="1" parent="1">
                    <mxGeometry x="1150" y="400" width="100" height="300" as="geometry"/>
                </mxCell>
                <mxCell id="32" value="look for user with username" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="1010" y="480" width="170" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="34" value="return to backend" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="1030" y="528" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="37" value="&lt;p style=&quot;margin:0px;margin-top:4px;margin-left:5px;text-align:left;&quot;&gt;&lt;b&gt;username and password valid&lt;/b&gt;&lt;/p&gt;" style="html=1;shape=mxgraph.sysml.package;overflow=fill;labelX=95;align=left;spacingLeft=5;verticalAlign=top;spacingTop=-3;" vertex="1" parent="1">
                    <mxGeometry x="822" y="503" width="218" height="97" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>
