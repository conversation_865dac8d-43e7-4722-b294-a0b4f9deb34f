<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('manuscript_versions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('manuscript_id')->constrained()->onDelete('cascade');
            $table->integer('version_number');
            $table->string('title');
            $table->text('abstract');
            $table->json('keywords')->nullable();
            $table->text('translated_abstract')->nullable();
            $table->string('file_path');
            $table->json('authors')->nullable();
            $table->text('revision_notes')->nullable();
            $table->enum('status', [
                'submitted',
                'under_review',
                'revisions_required',
                'accepted',
                'rejected',
                'payment_pending',
                'published'
            ])->default('submitted');
            $table->timestamp('submitted_at');
            $table->timestamps();

            $table->index(['manuscript_id', 'version_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('manuscript_versions');
    }
};
