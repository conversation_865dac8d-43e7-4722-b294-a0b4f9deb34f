<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('manuscripts', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('abstract');
            $table->json('keywords');
            $table->text('translated_abstract')->nullable();
            $table->string('file_path');
            $table->enum('language', ['en', 'fr']);
            $table->enum('status', [
                'submitted',
                'under_review',
                'payment_pending',
                'accepted',
                'rejected'
            ])->default('submitted');
            $table->enum('payment_status', ['pending', 'paid'])->default('pending');
            $table->foreignId('author_id')->constrained('users')->cascadeOnDelete();
            $table->foreignId('issue_id')->nullable()->constrained();
            $table->string('submission_id')->unique(); // JEMS-2025-001
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('manuscripts');
    }
};
