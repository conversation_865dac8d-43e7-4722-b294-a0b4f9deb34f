<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add 'revisions_required' to the status enum
        DB::statement("ALTER TABLE manuscripts MODIFY COLUMN status ENUM('submitted', 'under_review', 'payment_pending', 'accepted', 'rejected', 'revisions_required', 'published') NOT NULL DEFAULT 'submitted'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove 'revisions_required' from the status enum
        DB::statement("ALTER TABLE manuscripts MODIFY COLUMN status ENUM('submitted', 'under_review', 'payment_pending', 'accepted', 'rejected', 'published') NOT NULL DEFAULT 'submitted'");
    }
};