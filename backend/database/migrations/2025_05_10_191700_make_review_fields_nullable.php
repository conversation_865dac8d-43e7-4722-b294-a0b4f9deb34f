<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('reviews', function (Blueprint $table) {
            $table->enum('recommendation', [
                'accept',     // Accept as is
                'minor',      // Accept with minor revisions
                'major',      // Needs major revisions
                'reject'      // Reject
            ])->nullable()->change();
            $table->text('feedback')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('reviews', function (Blueprint $table) {
            $table->enum('recommendation', [
                'accept',     // Accept as is
                'minor',      // Accept with minor revisions
                'major',      // Needs major revisions
                'reject'      // Reject
            ])->nullable(false)->change();
            $table->text('feedback')->nullable(false)->change();
        });
    }
};
