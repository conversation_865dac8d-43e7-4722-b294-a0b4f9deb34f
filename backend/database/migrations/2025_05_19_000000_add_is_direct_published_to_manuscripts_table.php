<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('manuscripts', function (Blueprint $table) {
            $table->boolean('is_direct_published')->default(false);
            $table->boolean('featured')->default(false);
            $table->string('doi')->nullable()->unique();
            $table->timestamp('published_date')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('manuscripts', function (Blueprint $table) {
            $table->dropColumn(['is_direct_published', 'doi', 'published_date', 'featured']);
        });
    }
};
