<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('public_contents', function (Blueprint $table) {
            $table->enum('status', ['draft', 'published'])->default('published')->after('language');
            $table->boolean('featured')->default(false)->after('status');
            $table->integer('priority')->default(0)->after('featured');
            $table->unsignedBigInteger('created_by')->nullable()->after('priority');

            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->index(['type', 'status']);
            $table->index(['featured', 'priority']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('public_contents', function (Blueprint $table) {
            $table->dropForeign(['created_by']);
            $table->dropIndex(['type', 'status']);
            $table->dropIndex(['featured', 'priority']);
            $table->dropColumn(['status', 'featured', 'priority', 'created_by']);
        });
    }
};
