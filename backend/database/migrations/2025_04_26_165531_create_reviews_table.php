<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('manuscript_id')->constrained()->cascadeOnDelete();
            $table->foreignId('reviewer_id')->constrained('users')->cascadeOnDelete();
            $table->enum('recommendation', [
                'accept',     // Accept as is
                'minor',      // Accept with minor revisions
                'major',      // Needs major revisions
                'reject'      // Reject
            ]);
            $table->text('feedback'); // Comments for authors
            $table->text('confidential_comments')->nullable(); // For editors only
            $table->timestamp('submitted_at')->nullable();
            $table->timestamps();

            $table->unique(['manuscript_id', 'reviewer_id']); // Prevent duplicate assignments
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reviews');
    }
};
