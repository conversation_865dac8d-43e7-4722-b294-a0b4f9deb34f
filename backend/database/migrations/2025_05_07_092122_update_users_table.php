<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('last_login_at')->nullable();
            $table->string('last_login_ip')->nullable();
            $table->string('last_login_user_agent')->nullable();
            $table->string('last_login_location')->nullable();
            $table->string('last_login_country')->nullable();
            $table->string('last_login_city')->nullable();
            $table->string('last_login_region')->nullable();
            $table->string('last_login_latitude')->nullable();
            $table->string('last_login_longitude')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('last_login_at');
            $table->dropColumn('last_login_ip');
            $table->dropColumn('last_login_user_agent');
            $table->dropColumn('last_login_location');
            $table->dropColumn('last_login_country');
            $table->dropColumn('last_login_city');
            $table->dropColumn('last_login_region');
            $table->dropColumn('last_login_latitude');
            $table->dropColumn('last_login_longitude');
        });
    }
};
