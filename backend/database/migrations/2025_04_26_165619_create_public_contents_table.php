<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('public_contents', function (Blueprint $table) {
            $table->id();
            $table->enum('type', ['announcement', 'editorial', 'guidelines']);
            $table->json('title'); // {'en': 'Title', 'fr': 'Titre'}
            $table->json('content'); // {'en': 'Content', 'fr': 'Contenu'}
            $table->enum('language', ['en', 'fr']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('public_contents');
    }
};
