<?php

namespace Database\Seeders;

use App\Models\Payment;
use App\Models\Manuscript;
use App\Models\User;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class PaymentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $manuscripts = Manuscript::all();

        if ($manuscripts->isEmpty()) {
            $this->command->warn('Please run ManuscriptSeeder first!');
            return;
        }

        foreach ($manuscripts as $manuscript) {
            $paymentData = [
                'manuscript_id' => $manuscript->id,
                'user_id' => $manuscript->author_id,
                'amount' => 150.00, // Standard publication fee in USD
                'currency' => 'USD',
                'payment_method' => $this->getRandomPaymentMethod(),
                'status' => $manuscript->payment_status === 'paid' ? 'completed' : 'pending',
                'created_at' => $manuscript->created_at,
                'updated_at' => $manuscript->updated_at,
            ];

            if ($manuscript->payment_status === 'paid') {
                $paymentData = array_merge($paymentData, [
                    'transaction_id' => 'TXN_' . strtoupper(uniqid()),
                    'paid_at' => $manuscript->created_at->addDays(rand(1, 7)),
                    'payment_details' => $this->generatePaymentDetails($paymentData['payment_method']),
                ]);
            } else {
                $paymentData['payment_details'] = [
                    'invoice_number' => 'INV_' . strtoupper(uniqid()),
                    'due_date' => now()->addDays(30)->toDateString(),
                    'notes' => 'Payment pending for manuscript publication fee.',
                ];
            }

            Payment::create($paymentData);
        }

        $this->command->info('Payments seeded successfully!');
    }

    private function getRandomPaymentMethod()
    {
        $methods = ['bank_transfer', 'credit_card', 'paypal', 'mobile_money'];
        return $methods[array_rand($methods)];
    }

    private function generatePaymentDetails($method)
    {
        switch ($method) {
            case 'bank_transfer':
                return [
                    'bank_name' => 'Commercial Bank of Cameroon',
                    'account_number' => '****' . rand(1000, 9999),
                    'reference_number' => 'REF_' . strtoupper(uniqid()),
                    'transfer_date' => now()->subDays(rand(1, 10))->toDateString(),
                ];

            case 'credit_card':
                return [
                    'card_type' => 'Visa',
                    'last_four_digits' => rand(1000, 9999),
                    'authorization_code' => 'AUTH_' . strtoupper(uniqid()),
                    'processor' => 'Stripe',
                ];

            case 'paypal':
                return [
                    'paypal_transaction_id' => 'PP_' . strtoupper(uniqid()),
                    'payer_email' => '<EMAIL>',
                    'payment_date' => now()->subDays(rand(1, 10))->toDateString(),
                ];

            case 'mobile_money':
                return [
                    'provider' => 'MTN Mobile Money',
                    'phone_number' => '+237' . rand(*********, *********),
                    'transaction_id' => 'MM_' . strtoupper(uniqid()),
                    'confirmation_code' => 'CONF_' . rand(100000, 999999),
                ];

            default:
                return [];
        }
    }
}
