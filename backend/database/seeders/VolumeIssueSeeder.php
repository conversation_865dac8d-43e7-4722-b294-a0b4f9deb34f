<?php

namespace Database\Seeders;

use App\Models\Volume;
use App\Models\Issue;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class VolumeIssueSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Volumes for the past few years
        $volumes = [
            [
                'year' => 2023,
                'number' => 1,
                'description' => 'This inaugural volume explores various aspects of economic development and management strategies across African nations, with particular focus on sustainable growth and innovation.',
                'cover_image' => 'volumes/2023-vol1-cover.jpg',
                'published' => true,
                'publication_date' => Carbon::create(2023, 12, 15),
            ],
            [
                'year' => 2024,
                'number' => 2,
                'description' => 'Volume 2 focuses on financial market development, economic integration, and the role of technology in modern economic systems.',
                'cover_image' => 'volumes/2024-vol2-cover.jpg',
                'published' => true,
                'publication_date' => Carbon::create(2024, 12, 20),
            ],
            [
                'year' => 2025,
                'number' => 3,
                'description' => 'The current volume examines sustainable business practices, innovation in management, and the future of economic development in the digital age.',
                'cover_image' => 'volumes/2025-vol3-cover.jpg',
                'published' => false,
                'publication_date' => null,
            ],
        ];

        foreach ($volumes as $volumeData) {
            $volume = Volume::create($volumeData);

            // Create issues for each volume
            if ($volumeData['year'] == 2023) {
                // Volume 1 - 2 issues
                $issues = [
                    [
                        'number' => 1,
                        'type' => 'January',
                        'published' => true,
                        'publication_date' => Carbon::create(2023, 6, 15),
                        'submission_deadline' => Carbon::create(2023, 4, 15),
                    ],
                    [
                        'number' => 2,
                        'type' => 'June',
                        'published' => true,
                        'publication_date' => Carbon::create(2023, 12, 15),
                        'submission_deadline' => Carbon::create(2023, 10, 15),
                    ],
                ];
            } elseif ($volumeData['year'] == 2024) {
                // Volume 2 - 2 issues (January and June only)
                $issues = [
                    [
                        'number' => 1,
                        'type' => 'January',
                        'published' => true,
                        'publication_date' => Carbon::create(2024, 4, 20),
                        'submission_deadline' => Carbon::create(2024, 2, 20),
                    ],
                    [
                        'number' => 2,
                        'type' => 'June',
                        'published' => true,
                        'publication_date' => Carbon::create(2024, 8, 25),
                        'submission_deadline' => Carbon::create(2024, 6, 25),
                    ],
                ];
            } else {
                // Volume 3 (2025) - Current volume with 2 issues
                $issues = [
                    [
                        'number' => 1,
                        'type' => 'January',
                        'published' => true,
                        'publication_date' => Carbon::create(2025, 6, 15),
                        'submission_deadline' => Carbon::create(2025, 4, 15),
                        'is_current' => true,
                    ],
                    [
                        'number' => 2,
                        'type' => 'June',
                        'published' => false,
                        'publication_date' => null,
                        'submission_deadline' => Carbon::create(2025, 10, 15),
                        'is_current' => false,
                    ],
                ];
            }

            foreach ($issues as $issueData) {
                $issueData['volume_id'] = $volume->id;
                Issue::create($issueData);
            }
        }

        $this->command->info('Volumes and Issues seeded successfully!');
    }
}
