<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'journalName',
                'value' => 'Journal of Example Publications',
                'group' => 'general',
                'type' => 'string',
                'label' => 'Journal Name',
                'description' => 'The full name of the journal',
                'is_public' => true
            ],
            [
                'key' => 'journalAbbreviation',
                'value' => 'JEP',
                'group' => 'general',
                'type' => 'string',
                'label' => 'Journal Abbreviation',
                'description' => 'The abbreviated name of the journal',
                'is_public' => true
            ],
            [
                'key' => 'issn',
                'value' => '1234-5678',
                'group' => 'general',
                'type' => 'string',
                'label' => 'ISSN',
                'description' => 'The ISSN number of the journal',
                'is_public' => true
            ],
            [
                'key' => 'publisher',
                'value' => 'Example Publisher',
                'group' => 'general',
                'type' => 'string',
                'label' => 'Publisher',
                'description' => 'The publisher of the journal',
                'is_public' => true
            ],
            [
                'key' => 'contactEmail',
                'value' => '<EMAIL>',
                'group' => 'general',
                'type' => 'email',
                'label' => 'Contact Email',
                'description' => 'Primary contact email address',
                'is_public' => true
            ],

            // Appearance
            [
                'key' => 'primaryColor',
                'value' => '#3b82f6',
                'group' => 'appearance',
                'type' => 'string',
                'label' => 'Primary Color',
                'description' => 'The primary color theme of the journal',
                'is_public' => true
            ],
            [
                'key' => 'secondaryColor',
                'value' => '#10b981',
                'group' => 'appearance',
                'type' => 'string',
                'label' => 'Secondary Color',
                'description' => 'The secondary color theme of the journal',
                'is_public' => true
            ],
            [
                'key' => 'logo',
                'value' => '',
                'group' => 'appearance',
                'type' => 'string',
                'label' => 'Journal Logo',
                'description' => 'The logo of the journal',
                'is_public' => true
            ],
            [
                'key' => 'favicon',
                'value' => '',
                'group' => 'appearance',
                'type' => 'string',
                'label' => 'Favicon',
                'description' => 'The favicon of the journal',
                'is_public' => true
            ],

            // Submission Settings
            [
                'key' => 'submissionsOpen',
                'value' => true,
                'group' => 'submission',
                'type' => 'boolean',
                'label' => 'Accepting Submissions',
                'description' => 'Whether the journal is currently accepting submissions',
                'is_public' => true
            ],
            [
                'key' => 'submissionFee',
                'value' => 50.00,
                'group' => 'submission',
                'type' => 'number',
                'label' => 'Submission Fee',
                'description' => 'Fee for manuscript submission (in USD)',
                'is_public' => true
            ],
            [
                'key' => 'maxFileSize',
                'value' => 10,
                'group' => 'submission',
                'type' => 'number',
                'label' => 'Maximum File Size',
                'description' => 'Maximum file size for manuscript uploads (in MB)',
                'is_public' => true
            ],
            [
                'key' => 'allowedFileTypes',
                'value' => ['.doc', '.docx', '.pdf'],
                'group' => 'submission',
                'type' => 'array',
                'label' => 'Allowed File Types',
                'description' => 'File types allowed for manuscript submission',
                'is_public' => true
            ],

            // Review Process
            [
                'key' => 'reviewProcess',
                'value' => 'double-blind',
                'group' => 'review',
                'type' => 'string',
                'label' => 'Review Process',
                'description' => 'The type of review process used',
                'is_public' => false
            ],
            [
                'key' => 'minReviewers',
                'value' => 2,
                'group' => 'review',
                'type' => 'number',
                'label' => 'Minimum Reviewers',
                'description' => 'Minimum number of reviewers required per manuscript',
                'is_public' => false
            ],
            [
                'key' => 'reviewDeadline',
                'value' => 14,
                'group' => 'review',
                'type' => 'number',
                'label' => 'Review Deadline',
                'description' => 'Number of days allowed for manuscript review',
                'is_public' => false
            ],

            // Email Notifications
            [
                'key' => 'notifyAuthorsOnSubmission',
                'value' => true,
                'group' => 'notifications',
                'type' => 'boolean',
                'label' => 'Notify Authors on Submission',
                'description' => 'Whether to notify authors when their submission is received',
                'is_public' => false
            ],
            [
                'key' => 'notifyReviewersOnAssignment',
                'value' => true,
                'group' => 'notifications',
                'type' => 'boolean',
                'label' => 'Notify Reviewers on Assignment',
                'description' => 'Whether to notify reviewers when they are assigned a manuscript',
                'is_public' => false
            ],
            [
                'key' => 'notifyEditorsOnDecision',
                'value' => true,
                'group' => 'notifications',
                'type' => 'boolean',
                'label' => 'Notify Editors on Decision',
                'description' => 'Whether to notify editors when a decision is made',
                'is_public' => false
            ],

            // Privacy & Security
            [
                'key' => 'requireORCID',
                'value' => false,
                'group' => 'security',
                'type' => 'boolean',
                'label' => 'Require ORCID',
                'description' => 'Whether to require ORCID for submissions',
                'is_public' => false
            ],
            [
                'key' => 'enableTwoFactorAuth',
                'value' => true,
                'group' => 'security',
                'type' => 'boolean',
                'label' => 'Enable Two-Factor Authentication',
                'description' => 'Whether to enable two-factor authentication for users',
                'is_public' => false
            ],
            [
                'key' => 'dataRetentionPeriod',
                'value' => 5,
                'group' => 'security',
                'type' => 'number',
                'label' => 'Data Retention Period',
                'description' => 'Number of years to retain user data',
                'is_public' => false
            ]
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
