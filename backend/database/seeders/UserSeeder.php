<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Admin User
        $admin = User::create([
            'name' => 'JEMS Administrator',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'role' => 'admin',
            'affiliation' => 'The University of Bamenda',
            'country' => 'Cameroon',
            'expertise' => ['Journal Management', 'Academic Publishing'],
            'bio' => 'System administrator for the Journal of Economics and Management Sciences.',
            'orcid_id' => '0000-0000-0000-0001',
        ]);

        // Create Editor-in-Chief
        $editor = User::create([
            'name' => 'Professor <PERSON><PERSON>',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'role' => 'editor',
            'affiliation' => 'Faculty of Economics and Management Sciences, The University of Bamenda',
            'country' => 'Cameroon',
            'expertise' => ['Economics', 'Management Sciences', 'Development Economics'],
            'bio' => 'Ph.D. University of Dschang, Cameroon. Dean Faculty of Economics and Management Sciences, The University of Bamenda.',
            'orcid_id' => '0000-0000-0000-0002',
        ]);

        // Create Associate Editors
        $associateEditors = [
            [
                'name' => 'Dr. Agborbechem P. Tambi',
                'email' => '<EMAIL>',
                'affiliation' => 'Department of Economics, The University of Bamenda',
                'expertise' => ['Development Economics', 'Agricultural Economics', 'Rural Development'],
                'bio' => 'Associate Professor specializing in development economics and rural development strategies.',
            ],
            [
                'name' => 'Dr. Emmanuel Mbebeb',
                'email' => '<EMAIL>',
                'affiliation' => 'Department of Management Sciences, The University of Bamenda',
                'expertise' => ['Strategic Management', 'Organizational Behavior', 'Human Resource Management'],
                'bio' => 'Senior lecturer with expertise in strategic management and organizational development.',
            ],
            [
                'name' => 'Dr. Sarah Nkengfack',
                'email' => '<EMAIL>',
                'affiliation' => 'Department of Finance, The University of Bamenda',
                'expertise' => ['Corporate Finance', 'Banking', 'Financial Markets'],
                'bio' => 'Associate Professor specializing in corporate finance and banking systems.',
            ],
        ];

        foreach ($associateEditors as $index => $editorData) {
            $user = User::create([
                'name' => $editorData['name'],
                'email' => $editorData['email'],
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'role' => 'editor',
                'affiliation' => $editorData['affiliation'],
                'country' => 'Cameroon',
                'expertise' => $editorData['expertise'],
                'bio' => $editorData['bio'],
                'orcid_id' => '0000-0000-0000-000' . (3 + $index),
            ]);
        }

        // Create Reviewers
        $reviewers = [
            [
                'name' => 'Prof. Jean-Claude Fotso',
                'email' => '<EMAIL>',
                'affiliation' => 'University of Yaoundé I',
                'expertise' => ['Macroeconomics', 'Monetary Policy', 'Economic Development'],
            ],
            [
                'name' => 'Dr. Marie Ngozi Okafor',
                'email' => '<EMAIL>',
                'affiliation' => 'University of Nigeria, Nsukka',
                'expertise' => ['International Business', 'Marketing', 'Consumer Behavior'],
            ],
            [
                'name' => 'Prof. David Kwame Asante',
                'email' => '<EMAIL>',
                'affiliation' => 'University of Ghana',
                'expertise' => ['Financial Economics', 'Investment Analysis', 'Capital Markets'],
            ],
            [
                'name' => 'Dr. Fatima Al-Rashid',
                'email' => '<EMAIL>',
                'affiliation' => 'American University of Beirut',
                'expertise' => ['Operations Management', 'Supply Chain', 'Quality Management'],
            ],
            [
                'name' => 'Prof. Robert Thompson',
                'email' => '<EMAIL>',
                'affiliation' => 'London School of Economics',
                'expertise' => ['Econometrics', 'Statistical Analysis', 'Research Methods'],
            ],
        ];

        foreach ($reviewers as $index => $reviewerData) {
            $user = User::create([
                'name' => $reviewerData['name'],
                'email' => $reviewerData['email'],
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'role' => 'reviewer',
                'affiliation' => $reviewerData['affiliation'],
                'country' => ['Cameroon', 'Nigeria', 'Ghana', 'Lebanon', 'United Kingdom'][$index],
                'expertise' => $reviewerData['expertise'],
                'bio' => 'Experienced reviewer in ' . implode(', ', $reviewerData['expertise']) . '.',
                'orcid_id' => '0000-0000-0001-000' . ($index + 1),
            ]);
        }

        // Create Authors
        $authors = [
            [
                'name' => 'Dr. Amina Hassan',
                'email' => '<EMAIL>',
                'affiliation' => 'University of Douala',
                'expertise' => ['Entrepreneurship', 'Small Business Management'],
            ],
            [
                'name' => 'Prof. Michael Osei',
                'email' => '<EMAIL>',
                'affiliation' => 'Kwame Nkrumah University of Science and Technology',
                'expertise' => ['Industrial Economics', 'Technology Management'],
            ],
            [
                'name' => 'Dr. Grace Mwangi',
                'email' => '<EMAIL>',
                'affiliation' => 'University of Nairobi',
                'expertise' => ['Development Finance', 'Microfinance'],
            ],
            [
                'name' => 'Dr. Ahmed Benali',
                'email' => '<EMAIL>',
                'affiliation' => 'Mohammed V University',
                'expertise' => ['International Trade', 'Economic Integration'],
            ],
            [
                'name' => 'Dr. Lisa Johannesburg',
                'email' => '<EMAIL>',
                'affiliation' => 'University of Cape Town',
                'expertise' => ['Labor Economics', 'Social Policy'],
            ],
        ];

        foreach ($authors as $index => $authorData) {
            $user = User::create([
                'name' => $authorData['name'],
                'email' => $authorData['email'],
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'role' => 'author',
                'affiliation' => $authorData['affiliation'],
                'country' => ['Cameroon', 'Ghana', 'Kenya', 'Morocco', 'South Africa'][$index],
                'expertise' => $authorData['expertise'],
                'bio' => 'Researcher specializing in ' . implode(' and ', $authorData['expertise']) . '.',
                'orcid_id' => '0000-0000-0002-000' . ($index + 1),
            ]);
        }

        $this->command->info('Users seeded successfully!');
    }
}
