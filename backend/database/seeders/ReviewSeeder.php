<?php

namespace Database\Seeders;

use App\Models\Review;
use App\Models\Manuscript;
use App\Models\User;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class ReviewSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $reviewers = User::where('role', 'reviewer')->get();
        $manuscripts = Manuscript::all();

        if ($reviewers->isEmpty() || $manuscripts->isEmpty()) {
            $this->command->warn('Please run UserSeeder and ManuscriptSeeder first!');
            return;
        }

        // Create reviews for published manuscripts
        $publishedManuscripts = $manuscripts->where('status', 'published');

        foreach ($publishedManuscripts as $manuscript) {
            // Each published manuscript gets 2-3 reviews
            $numReviews = rand(2, 3);
            $selectedReviewers = $reviewers->random($numReviews);

            foreach ($selectedReviewers as $index => $reviewer) {
                $reviewData = $this->generateReviewData($manuscript, $reviewer, 'completed');
                Review::create($reviewData);
            }
        }

        // Create reviews for manuscripts under review
        $underReviewManuscripts = $manuscripts->where('status', 'under_review');

        foreach ($underReviewManuscripts as $manuscript) {
            // Each manuscript under review gets 2-3 reviews (some completed, some pending)
            $numReviews = rand(2, 3);
            $selectedReviewers = $reviewers->random($numReviews);

            foreach ($selectedReviewers as $index => $reviewer) {
                $status = $index < 1 ? 'completed' : 'pending'; // First review completed, others pending
                $reviewData = $this->generateReviewData($manuscript, $reviewer, $status);
                Review::create($reviewData);
            }
        }

        // Create some reviews for submitted manuscripts
        $submittedManuscripts = $manuscripts->where('status', 'submitted');

        foreach ($submittedManuscripts->take(2) as $manuscript) {
            // Assign reviewers but reviews are pending
            $numReviews = 2;
            $selectedReviewers = $reviewers->random($numReviews);

            foreach ($selectedReviewers as $reviewer) {
                $reviewData = $this->generateReviewData($manuscript, $reviewer, 'pending');
                Review::create($reviewData);
            }
        }

        $this->command->info('Reviews seeded successfully!');
    }

    private function generateReviewData($manuscript, $reviewer, $status)
    {
        $baseData = [
            'manuscript_id' => $manuscript->id,
            'reviewer_id' => $reviewer->id,
        ];

        if ($status === 'completed') {
            $recommendations = ['accept', 'minor', 'major', 'reject'];
            $recommendation = $recommendations[array_rand($recommendations)];

            // Generate realistic review content based on recommendation
            $reviewContent = $this->generateReviewContent($recommendation, $manuscript->title);

            $baseData = array_merge($baseData, [
                'recommendation' => $recommendation,
                'feedback' => $reviewContent['feedback'],
                'confidential_comments' => $reviewContent['confidential_comments'],
                'completed_at' => now()->subDays(rand(1, 30)),
                'submitted_at' => now()->subDays(rand(1, 30)),
            ]);
        } else {
            // For pending reviews, no recommendation or feedback yet
            $baseData = array_merge($baseData, [
                'recommendation' => 'minor', // Default value since it's required
                'feedback' => 'Review in progress...',
                'confidential_comments' => null,
                'completed_at' => null,
                'submitted_at' => null,
            ]);
        }

        return $baseData;
    }

    private function generateReviewContent($recommendation, $title)
    {
        $feedback = '';
        $confidentialComments = '';

        switch ($recommendation) {
            case 'accept':
                $feedback = "This manuscript presents a well-structured and comprehensive analysis of {$title}. The methodology is sound, the literature review is thorough, and the findings are clearly presented. The research makes a valuable contribution to the field and I recommend acceptance with minor editorial corrections.

Strengths:
- Clear research objectives and methodology
- Comprehensive literature review
- Well-presented results and analysis
- Practical implications clearly stated

Minor suggestions:
- Consider expanding the discussion on limitations
- Some minor grammatical corrections needed
- Figure 2 could benefit from better labeling";

                $confidentialComments = "This is a high-quality manuscript that makes a solid contribution to the field. The research is well-conducted and the paper is well-written. I recommend acceptance.";
                break;

            case 'minor':
                $feedback = "This manuscript addresses an important topic and presents interesting findings. However, there are several areas that require minor revisions before publication.

Strengths:
- Relevant and timely research topic
- Appropriate methodology
- Clear presentation of results

Areas for improvement:
- The literature review could be strengthened with more recent studies
- Some statistical analyses need clarification
- The discussion section could better connect findings to existing theory
- References need formatting corrections

Please address these issues and resubmit. The revisions should be straightforward and I look forward to seeing the improved version.";

                $confidentialComments = "This manuscript has merit but requires minor revisions. The authors should be able to address the issues I've identified without major restructuring.";
                break;

            case 'major':
                $feedback = "While this manuscript addresses an important research question, significant revisions are needed before it can be considered for publication.

Major concerns:
- The methodology section lacks sufficient detail for replication
- The sample size may be inadequate for the conclusions drawn
- The theoretical framework needs strengthening
- Several key studies are missing from the literature review
- The discussion does not adequately address study limitations

The authors should carefully address these concerns and consider restructuring parts of the manuscript. I would be willing to review a substantially revised version.";

                $confidentialComments = "This manuscript requires major revisions. While the topic is relevant, there are significant methodological and theoretical issues that need to be addressed.";
                break;

            case 'reject':
                $feedback = "Unfortunately, I cannot recommend this manuscript for publication in its current form. While the research topic is of interest, there are fundamental issues that cannot be adequately addressed through revision.

Major concerns:
- The research design is not appropriate for the research questions
- Significant methodological flaws that compromise the validity of findings
- The contribution to existing knowledge is limited
- The writing quality needs substantial improvement

I encourage the authors to consider these comments for future research endeavors.";

                $confidentialComments = "I recommend rejection of this manuscript due to fundamental methodological and conceptual issues that cannot be resolved through revision.";
                break;
        }

        return [
            'feedback' => $feedback,
            'confidential_comments' => $confidentialComments,
        ];
    }
}
