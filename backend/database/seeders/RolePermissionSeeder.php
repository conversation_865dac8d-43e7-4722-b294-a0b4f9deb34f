<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;


class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // User management
            'view users',
            'create users',
            'edit users',
            'delete users',
            'manage user roles',

            // Volume management
            'view volumes',
            'create volumes',
            'edit volumes',
            'delete volumes',
            'publish volumes',

            // Issue management
            'view issues',
            'create issues',
            'edit issues',
            'delete issues',
            'publish issues',

            // Manuscript management
            'view manuscripts',
            'create manuscripts',
            'edit manuscripts',
            'delete manuscripts',
            'submit manuscripts',
            'review manuscripts',
            'make editorial decisions',

            // Review management
            'view reviews',
            'create reviews',
            'edit reviews',
            'delete reviews',
            'assign reviewers',

            // Payment management
            'view payments',
            'process payments',
            'refund payments',

            // System management
            'view system settings',
            'edit system settings',
            'view logs',
            'manage backups'
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions
        $admin = Role::create(['name' => 'admin']);
        $admin->givePermissionTo(Permission::all());

        $editor = Role::create(['name' => 'editor']);
        $editor->givePermissionTo([
            'view volumes',
            'create volumes',
            'edit volumes',
            'delete volumes',
            'publish volumes',
            'view issues',
            'create issues',
            'edit issues',
            'delete issues',
            'publish issues',
            'view manuscripts',
            'edit manuscripts',
            'make editorial decisions',
            'view reviews',
            'assign reviewers',
            'view payments',
            'process payments'
        ]);

        $reviewer = Role::create(['name' => 'reviewer']);
        $reviewer->givePermissionTo([
            'view manuscripts',
            'review manuscripts',
            'view reviews',
            'create reviews',
            'edit reviews'
        ]);

        $author = Role::create(['name' => 'author']);
        $author->givePermissionTo([
            'view volumes',
            'view issues',
            'view manuscripts',
            'create manuscripts',
            'edit manuscripts',
            'submit manuscripts'
        ]);

        // Create a system role for automated processes
        $system = Role::create(['name' => 'system']);
        $system->givePermissionTo([
            'view manuscripts',
            'view reviews',
            'view payments',
            'process payments'
        ]);
    }
}
