<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Manuscript;
use App\Models\ManuscriptVersion;
use App\Models\User;

try {
    // Find or create a test author
    $author = User::where('email', '<EMAIL>')->first();
    if (!$author) {
        $author = User::create([
            'name' => 'Test Author',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'author',
            'affiliation' => 'Test University',
            'country' => 'Test Country',
            'email_verified_at' => now(),
        ]);
        echo "Created test author\n";
    }

    // Create or update test manuscript
    $manuscript = Manuscript::where('author_id', $author->id)->first();
    if (!$manuscript) {
        $manuscript = Manuscript::create([
            'title' => 'Research on AI Applications in Healthcare',
            'abstract' => 'This study explores the applications of artificial intelligence in modern healthcare systems.',
            'keywords' => ['AI', 'healthcare', 'machine learning'],
            'language' => 'en',
            'file_path' => 'manuscripts/ai-healthcare-v3.pdf',
            'author_id' => $author->id,
            'issue_id' => 1,
            'authors' => ['Dr. Test Author', 'Dr. Jane Smith'],
            'status' => 'revisions_required',
        ]);
    } else {
        $manuscript->update([
            'title' => 'Research on AI Applications in Healthcare (Updated)',
            'status' => 'revisions_required',
        ]);
    }

    // Clear existing versions
    ManuscriptVersion::where('manuscript_id', $manuscript->id)->delete();

    // Create version 1 (original submission)
    ManuscriptVersion::create([
        'manuscript_id' => $manuscript->id,
        'version_number' => 1,
        'title' => 'Research on AI Applications in Healthcare',
        'abstract' => 'This study explores the applications of artificial intelligence in modern healthcare systems.',
        'keywords' => ['AI', 'healthcare', 'machine learning'],
        'translated_abstract' => null,
        'file_path' => 'manuscripts/ai-healthcare-v1.pdf',
        'authors' => ['Dr. Test Author', 'Dr. Jane Smith'],
        'revision_notes' => null,
        'status' => 'submitted',
        'submitted_at' => now()->subDays(10),
        'created_at' => now()->subDays(10),
    ]);

    // Create version 2 (first revision)
    ManuscriptVersion::create([
        'manuscript_id' => $manuscript->id,
        'version_number' => 2,
        'title' => 'Research on AI Applications in Healthcare: An Updated Approach',
        'abstract' => 'This revised study explores the applications of artificial intelligence in modern healthcare systems with improved methodology.',
        'keywords' => ['AI', 'healthcare', 'machine learning', 'methodology'],
        'translated_abstract' => null,
        'file_path' => 'manuscripts/ai-healthcare-v2.pdf',
        'authors' => ['Dr. Test Author', 'Dr. Jane Smith'],
        'revision_notes' => 'Updated methodology section based on reviewer feedback. Added more recent literature review.',
        'status' => 'under_review',
        'submitted_at' => now()->subDays(5),
        'created_at' => now()->subDays(5),
    ]);

    // Create version 3 (second revision - current)
    ManuscriptVersion::create([
        'manuscript_id' => $manuscript->id,
        'version_number' => 3,
        'title' => 'Research on AI Applications in Healthcare: A Comprehensive Study',
        'abstract' => 'This comprehensive study explores the applications of artificial intelligence in modern healthcare systems with enhanced data analysis.',
        'keywords' => ['AI', 'healthcare', 'machine learning', 'data analysis', 'medical technology'],
        'translated_abstract' => null,
        'file_path' => 'manuscripts/ai-healthcare-v3.pdf',
        'authors' => ['Dr. Test Author', 'Dr. Jane Smith'],
        'revision_notes' => 'Enhanced data analysis section. Added statistical significance tests. Improved conclusion with practical implications.',
        'status' => 'revisions_required',
        'submitted_at' => now()->subDays(1),
        'created_at' => now()->subDays(1),
    ]);

    echo "\n=== VERSION HISTORY TEST SETUP COMPLETE ===\n";
    echo "Manuscript ID: {$manuscript->id}\n";
    echo "Manuscript Title: {$manuscript->title}\n";
    echo "Author: {$author->name} ({$author->email})\n";
    echo "Status: {$manuscript->status}\n";
    echo "Number of versions: " . $manuscript->versions()->count() . "\n";
    echo "\nVersions created:\n";
    foreach ($manuscript->versions()->orderBy('version_number')->get() as $version) {
        echo "- Version {$version->version_number}: {$version->title} ({$version->status})\n";
    }
    echo "\nTo test:\n";
    echo "1. Login with email: <EMAIL>, password: password\n";
    echo "2. Navigate to: /author/submissions/{$manuscript->id}\n";
    echo "3. You should see the Version History section with 3 versions\n";
    echo "4. You should also see the 'Resubmit' button for revisions\n";
    echo "\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
