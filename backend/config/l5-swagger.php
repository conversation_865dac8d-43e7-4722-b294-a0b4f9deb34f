<?php

return [
    'default' => 'default',
    'documentations' => [
        'default' => [
            'proxy' => false,
            'operations_sort' => 'alpha',
            'additional_config_url' => null,
            'validator_url' => null,

            'tagsSorter' => 'alpha',
            'pathsSorter' => 'alpha',
            'generateOperationId' => false,
            'api' => [
                'title' => 'JEMS Journal API',
                'description' => 'API documentation for JEMS Journal system',
                'version' => '1.0.0',
            ],
            'routes' => [
                'api' => 'api/documentation', // UI route
                'docs' => 'api/docs.json',   // JSON docs route
            ],
            'paths' => [
                'annotations' => [
                    base_path('app'),
                    base_path('routes'),
                ],
                'docs' => storage_path('api-docs'), // Storage path for docs
                'docs_json' => 'api-docs.json',
                'docs_yaml' => 'api-docs.yaml',
                'excludes' => [],
                'base' =>  null,
                'basePath' => '/api',
            ],
        ],
    ],
    'defaults' => [
        'routes' => [
            'api' => 'api/documentation',
            'docs' => 'api/docs.json',
        ],
        'securityDefinitions' => [
            'securitySchemes' => [
                'sanctum' => [
                    'type' => 'apiKey',
                    'description' => 'Enter token in format (Bearer <token>)',
                    'name' => 'Authorization',
                    'in' => 'header',
                ],
            ],
        ],
        'generate_always' => env('L5_SWAGGER_GENERATE_ALWAYS', false),
        'ui' => [
            'display' => [
                'doc_expansion' => 'none',
                'filter' => true,
            ],
        ],
    ],
];
