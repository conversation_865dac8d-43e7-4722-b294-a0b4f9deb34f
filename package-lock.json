{"name": "JEMS-journal", "lockfileVersion": 3, "requires": true, "packages": {"": {"dependencies": {"i18next": "^25.1.2", "i18next-browser-languagedetector": "^8.1.0", "react-i18next": "^15.5.1"}}, "node_modules/@babel/runtime": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.1.tgz", "integrity": "sha512-1x3D2xEk2fRo3PAhwQwu5UubzgiVWSXTBfWpVd2Mx2AzRqJuDJCsgaDVZ7HB5iGzDW1Hl1sWN2mFyKjmR9uAog==", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/html-parse-stringify": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/html-parse-stringify/-/html-parse-stringify-3.0.1.tgz", "integrity": "sha512-KknJ50kTInJ7qIScF3jeaFRpMpE8/lfiTdzf/twXyPBLAGrLRTmkz3AdTnKeh40X8k9L2fdYwEp/42WGXIRGcg==", "license": "MIT", "dependencies": {"void-elements": "3.1.0"}}, "node_modules/i18next": {"version": "25.1.2", "resolved": "https://registry.npmjs.org/i18next/-/i18next-25.1.2.tgz", "integrity": "sha512-SP63m8LzdjkrAjruH7SCI3ndPSgjt4/wX7ouUUOzCW/eY+HzlIo19IQSfYA9X3qRiRP1SYtaTsg/Oz/PGsfD8w==", "funding": [{"type": "individual", "url": "https://locize.com"}, {"type": "individual", "url": "https://locize.com/i18next.html"}, {"type": "individual", "url": "https://www.i18next.com/how-to/faq#i18next-is-awesome.-how-can-i-support-the-project"}], "license": "MIT", "dependencies": {"@babel/runtime": "^7.26.10"}, "peerDependencies": {"typescript": "^5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/i18next-browser-languagedetector": {"version": "8.1.0", "resolved": "https://registry.npmjs.org/i18next-browser-languagedetector/-/i18next-browser-languagedetector-8.1.0.tgz", "integrity": "sha512-mHZxNx1Lq09xt5kCauZ/4bsXOEA2pfpwSoU11/QTJB+pD94iONFwp+ohqi///PwiFvjFOxe1akYCdHyFo1ng5Q==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.23.2"}}, "node_modules/react": {"version": "19.1.0", "resolved": "https://registry.npmjs.org/react/-/react-19.1.0.tgz", "integrity": "sha512-FS+XFBNvn3GTAWq26joslQgWNoFu08F4kl0J4CgdNKADkdSGXQyTCnKteIAJy96Br6YbpEU1LSzV5dYtjMkMDg==", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/react-i18next": {"version": "15.5.1", "resolved": "https://registry.npmjs.org/react-i18next/-/react-i18next-15.5.1.tgz", "integrity": "sha512-C8RZ7N7H0L+flitiX6ASjq9p5puVJU1Z8VyL3OgM/QOMRf40BMZX+5TkpxzZVcTmOLPX5zlti4InEX5pFyiVeA==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.25.0", "html-parse-stringify": "^3.0.1"}, "peerDependencies": {"i18next": ">= 23.2.3", "react": ">= 16.8.0", "typescript": "^5"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}, "typescript": {"optional": true}}}, "node_modules/void-elements": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/void-elements/-/void-elements-3.1.0.tgz", "integrity": "sha512-Dhxzh5HZuiHQhbvTW9AMetFfBHDMYpo23Uo9btPXgdYP+3T5S+p+jgNy7spra+veYhBP2dCSgxR/i2Y02h5/6w==", "license": "MIT", "engines": {"node": ">=0.10.0"}}}}