/** @type {import('tailwindcss').Config} */
export default {
    content: [
      "./index.html",
      "./src/**/*.{js,ts,jsx,tsx}",
    ],
    theme: {
      extend: {
        colors: {
          // JEMS Color Scheme based on journal cover
          'jems': {
            'navy': '#1e3a8a',      // Deep navy blue from cover
            'blue': '#3b82f6',       // Primary blue
            'gold': '#fbbf24',       // Golden yellow
            'orange': '#f97316',     // Orange/coral accent
            'teal': '#06b6d4',       // Light blue/teal
            'green': '#10b981',      // Green from logo
            'cream': '#fef7ed',      // Cream background
            'dark': '#1f2937',       // Dark text
          },
          // Override default blue with JEMS blue
          'blue': {
            50: '#eff6ff',
            100: '#dbeafe',
            200: '#bfdbfe',
            300: '#93c5fd',
            400: '#60a5fa',
            500: '#3b82f6',  // JEMS blue
            600: '#1e3a8a',  // JEMS navy
            700: '#1d4ed8',
            800: '#1e40af',
            900: '#1e3a8a',  // JEMS navy
          }
        },
        fontFamily: {
          'serif': ['Georgia', 'Times New Roman', 'serif'],
          'sans': ['Inter', 'system-ui', 'sans-serif'],
        },
        backgroundImage: {
          'jems-gradient': 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #fbbf24 100%)',
          'jems-wave': 'linear-gradient(90deg, #06b6d4 0%, #3b82f6 50%, #fbbf24 100%)',
        }
      },
    },
    plugins: [],
  }
  