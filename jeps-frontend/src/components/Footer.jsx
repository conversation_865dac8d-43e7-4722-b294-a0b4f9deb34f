import { Link } from "react-router-dom";
import { FaUniversity, FaEnvelope, FaTwitter, FaFacebook, FaLinkedin } from "react-icons/fa";

const Footer = () => {
  return (
    <footer className="bg-gray-200 border-t shadow-lg">
      <div className="container mx-auto px-4 py-12 w-4/5">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* About */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-4">About JEPS</h3>
            <p className="text-gray-600 mb-4">
              The Journal of Education and Psychological Sciences (JEPS) is a peer-reviewed 
              multidisciplinary journal published by the Faculty of Education at the 
              University of Bamenda, Cameroon.
            </p>
            <div className="flex items-center text-gray-600">
              <FaUniversity className="mr-2" />
              <span>The University of Bamenda</span>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/author/submit" className="text-gray-600 hover:text-blue-600 transition-colors">
                  Submit Paper
                </Link>
              </li>
              <li>
                <Link to="/current-issue" className="text-gray-600 hover:text-blue-600 transition-colors">
                  Current Issue
                </Link>
              </li>
              <li>
                <Link to="/browse" className="text-gray-600 hover:text-blue-600 transition-colors">
                  Browse Articles
                </Link>
              </li>
              <li>
                <Link to="/editorial-board" className="text-gray-600 hover:text-blue-600 transition-colors">
                  Editorial Board
                </Link>
              </li>
            </ul>
          </div>

          {/* Guidelines */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-4">For Authors</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/guidelines" className="text-gray-600 hover:text-blue-600 transition-colors">
                  Submission Guidelines
                </Link>
              </li>
              <li>
                <Link to="/faq" className="text-gray-600 hover:text-blue-600 transition-colors">
                  FAQ
                </Link>
              </li>
              <li>
                <Link to="/announcements" className="text-gray-600 hover:text-blue-600 transition-colors">
                  Announcements
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-gray-600 hover:text-blue-600 transition-colors">
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Contact</h3>
            <address className="not-italic text-gray-600 space-y-2">
              <p>Faculty of Education</p>
              <p>The University of Bamenda</p>
              <p>Cameroon</p>
              <div className="flex items-center">
                <FaEnvelope className="mr-2" />
                <a href="mailto:<EMAIL>" className="hover:text-blue-600 transition-colors">
                  <EMAIL>
                </a>
              </div>
            </address>
            <div className="flex space-x-4 mt-4">
              <a href="#" className="text-gray-600 hover:text-blue-600 transition-colors">
                <FaTwitter size={20} />
              </a>
              <a href="#" className="text-gray-600 hover:text-blue-600 transition-colors">
                <FaFacebook size={20} />
              </a>
              <a href="#" className="text-gray-600 hover:text-blue-600 transition-colors">
                <FaLinkedin size={20} />
              </a>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-gray-200 mt-8 pt-8 text-center text-gray-500 text-sm">
          <p>© {new Date().getFullYear()} Journal of Education and Psychological Sciences. All rights reserved.</p>
          <p className="mt-1">ISSN: XXXX-XXXX | Published biannually (January & June)</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;