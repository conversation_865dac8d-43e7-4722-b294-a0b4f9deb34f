import { Link } from "react-router-dom";
import { FaUniversity, FaEnvelope, FaTwitter, FaFacebook, FaLinkedin } from "react-icons/fa";

const Footer = () => {
  return (
    <footer className="bg-gradient-to-r from-jems-navy to-jems-blue border-t-4 border-jems-gold shadow-2xl">
      <div className="container mx-auto px-4 py-12 w-4/5">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* About */}
          <div>
            <h3 className="text-lg font-bold text-jems-gold mb-4">About JEMS</h3>
            <p className="text-jems-cream mb-4 leading-relaxed">
              The Journal of Economics and Management Sciences (JEMS) is a peer-reviewed
              multidisciplinary journal published by the Faculty of Economics and Management Sciences at the
              University of Bamenda, Cameroon.
            </p>
            <div className="flex items-center text-jems-cream">
              <FaUniversity className="mr-2 text-jems-gold" />
              <span>The University of Bamenda</span>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-bold text-jems-gold mb-4">Quick Links</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/author/submit" className="text-jems-cream hover:text-jems-gold transition-colors duration-200 flex items-center">
                  <span className="w-2 h-2 bg-jems-orange rounded-full mr-2"></span>
                  Submit Paper
                </Link>
              </li>
              <li>
                <Link to="/current-issue" className="text-jems-cream hover:text-jems-gold transition-colors duration-200 flex items-center">
                  <span className="w-2 h-2 bg-jems-orange rounded-full mr-2"></span>
                  Current Issue
                </Link>
              </li>
              <li>
                <Link to="/browse" className="text-jems-cream hover:text-jems-gold transition-colors duration-200 flex items-center">
                  <span className="w-2 h-2 bg-jems-orange rounded-full mr-2"></span>
                  Browse Articles
                </Link>
              </li>
              <li>
                <Link to="/editorial-board" className="text-jems-cream hover:text-jems-gold transition-colors duration-200 flex items-center">
                  <span className="w-2 h-2 bg-jems-orange rounded-full mr-2"></span>
                  Editorial Board
                </Link>
              </li>
            </ul>
          </div>

          {/* Guidelines */}
          <div>
            <h3 className="text-lg font-bold text-jems-gold mb-4">For Authors</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/guidelines" className="text-jems-cream hover:text-jems-gold transition-colors duration-200 flex items-center">
                  <span className="w-2 h-2 bg-jems-teal rounded-full mr-2"></span>
                  Submission Guidelines
                </Link>
              </li>
              <li>
                <Link to="/faq" className="text-jems-cream hover:text-jems-gold transition-colors duration-200 flex items-center">
                  <span className="w-2 h-2 bg-jems-teal rounded-full mr-2"></span>
                  FAQ
                </Link>
              </li>
              <li>
                <Link to="/announcements" className="text-jems-cream hover:text-jems-gold transition-colors duration-200 flex items-center">
                  <span className="w-2 h-2 bg-jems-teal rounded-full mr-2"></span>
                  Announcements
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-jems-cream hover:text-jems-gold transition-colors duration-200 flex items-center">
                  <span className="w-2 h-2 bg-jems-teal rounded-full mr-2"></span>
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-lg font-bold text-jems-gold mb-4">Contact</h3>
            <address className="not-italic text-jems-cream space-y-2">
              <p>Faculty of Economics and Management Sciences</p>
              <p>The University of Bamenda</p>
              <p>Cameroon</p>
              <div className="flex items-center mt-3">
                <FaEnvelope className="mr-2 text-jems-gold" />
                <a href="mailto:<EMAIL>" className="hover:text-jems-gold transition-colors">
                  <EMAIL>
                </a>
              </div>
            </address>
            <div className="flex space-x-4 mt-6">
              <a href="#" className="text-jems-cream hover:text-jems-gold transition-colors transform hover:scale-110">
                <FaTwitter size={22} />
              </a>
              <a href="#" className="text-jems-cream hover:text-jems-gold transition-colors transform hover:scale-110">
                <FaFacebook size={22} />
              </a>
              <a href="#" className="text-jems-cream hover:text-jems-gold transition-colors transform hover:scale-110">
                <FaLinkedin size={22} />
              </a>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-jems-gold border-opacity-30 mt-8 pt-8 text-center text-jems-cream text-sm">
          <p>© {new Date().getFullYear()} Journal of Economics and Management Sciences. All rights reserved.</p>
          <p className="mt-1">ISSN: XXXX-XXXX | Published biannually (January & June)</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;