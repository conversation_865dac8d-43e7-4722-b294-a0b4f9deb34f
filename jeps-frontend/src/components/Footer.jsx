import { Link } from "react-router-dom";
import { FaUniversity, FaEnvelope, FaTwitter, FaFacebook, FaLinkedin } from "react-icons/fa";

const Footer = () => {
  return (
    <footer className="bg-gradient-to-r from-blue-900 to-blue-800 border-t-4 border-orange-400 shadow-2xl">
      <div className="container mx-auto px-4 py-12 w-4/5">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* About */}
          <div>
            <h3 className="text-lg font-bold text-orange-400 mb-4">About JEMS</h3>
            <p className="text-gray-200 mb-4 leading-relaxed">
              The Journal of Economics and Management Sciences (JEMS) is a peer-reviewed
              multidisciplinary journal published by the Faculty of Economics and Management Sciences at the
              University of Bamenda, Cameroon.
            </p>
            <div className="flex items-center text-gray-200">
              <FaUniversity className="mr-2 text-green-400" />
              <span>The University of Bamenda</span>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-bold text-orange-400 mb-4">Quick Links</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/author/submit" className="text-gray-200 hover:text-yellow-400 transition-colors duration-200 flex items-center">
                  <span className="w-2 h-2 bg-orange-400 rounded-full mr-2"></span>
                  Submit Paper
                </Link>
              </li>
              <li>
                <Link to="/current-issue" className="text-gray-200 hover:text-yellow-400 transition-colors duration-200 flex items-center">
                  <span className="w-2 h-2 bg-orange-400 rounded-full mr-2"></span>
                  Current Issue
                </Link>
              </li>
              <li>
                <Link to="/browse" className="text-gray-200 hover:text-yellow-400 transition-colors duration-200 flex items-center">
                  <span className="w-2 h-2 bg-orange-400 rounded-full mr-2"></span>
                  Browse Articles
                </Link>
              </li>
              <li>
                <Link to="/editorial-board" className="text-gray-200 hover:text-yellow-400 transition-colors duration-200 flex items-center">
                  <span className="w-2 h-2 bg-orange-400 rounded-full mr-2"></span>
                  Editorial Board
                </Link>
              </li>
            </ul>
          </div>

          {/* Guidelines */}
          <div>
            <h3 className="text-lg font-bold text-orange-400 mb-4">For Authors</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/guidelines" className="text-gray-200 hover:text-yellow-400 transition-colors duration-200 flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                  Submission Guidelines
                </Link>
              </li>
              <li>
                <Link to="/faq" className="text-gray-200 hover:text-yellow-400 transition-colors duration-200 flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                  FAQ
                </Link>
              </li>
              <li>
                <Link to="/announcements" className="text-gray-200 hover:text-yellow-400 transition-colors duration-200 flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                  Announcements
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-gray-200 hover:text-yellow-400 transition-colors duration-200 flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-lg font-bold text-orange-400 mb-4">Contact</h3>
            <address className="not-italic text-gray-200 space-y-2">
              <p>Faculty of Economics and Management Sciences</p>
              <p>The University of Bamenda</p>
              <p>Cameroon</p>
              <div className="flex items-center mt-3">
                <FaEnvelope className="mr-2 text-yellow-400" />
                <a href="mailto:<EMAIL>" className="hover:text-yellow-400 transition-colors">
                  <EMAIL>
                </a>
              </div>
            </address>
            <div className="flex space-x-4 mt-6">
              <a href="#" className="text-gray-200 hover:text-yellow-400 transition-colors transform hover:scale-110">
                <FaTwitter size={22} />
              </a>
              <a href="#" className="text-gray-200 hover:text-yellow-400 transition-colors transform hover:scale-110">
                <FaFacebook size={22} />
              </a>
              <a href="#" className="text-gray-200 hover:text-yellow-400 transition-colors transform hover:scale-110">
                <FaLinkedin size={22} />
              </a>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-orange-400 border-opacity-30 mt-8 pt-8 text-center text-gray-300 text-sm">
          <p>© {new Date().getFullYear()} Journal of Economics and Management Sciences. All rights reserved.</p>
          <p className="mt-1">ISSN: XXXX-XXXX | Published biannually (January & June)</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;