import { Link, useLocation } from "react-router-dom";
import { FaUniversity, FaBookOpen, FaSearch, FaLanguage, FaUserGraduate } from "react-icons/fa";    
import { useState } from "react";
import { useTranslation } from 'react-i18next';
import LoadingSpinner from './common/LoadingSpinner';

const Header = () => {
    const location = useLocation();
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isChangingLanguage, setIsChangingLanguage] = useState(false);
    const { t, i18n } = useTranslation();
    const isActive = (path) => location.pathname === path;

    const toggleMenu = () => {
        setIsMenuOpen(!isMenuOpen);
    };

    const toggleLanguage = () => {
        setIsChangingLanguage(true);
        const newLang = i18n.language === 'en' ? 'fr' : 'en';
        
        // Change language after loading completes
        setTimeout(() => {
            i18n.changeLanguage(newLang);
            setIsChangingLanguage(false);
        }, 3000);
    };

    return (
        <>
            {/* Language Change Loading Overlay */}
            {isChangingLanguage && (
                <div className="fixed inset-0 bg-black bg-opacity-20 z-50 flex items-center justify-center">
                    <div className="flex flex-col items-center">
                        <LoadingSpinner size="small" className="text-blue-600 w-6 h-6" />
                        <p className="mt-2 text-sm text-gray-600">{t('common.loading')}</p>
                    </div>
                </div>
            )}

            <header className="bg-blue-900 shadow-lg sticky top-0 z-40 border-b-2 border-green-500">
                <div className="container mx-auto px-4">
                    <div className="flex justify-between items-center py-4">
                        {/* Logo */}
                        <Link to="/" className="flex items-center space-x-3">
                            <div className="bg-white rounded-lg p-2 shadow-md">
                                <img src="assets/JemsLogo.png" alt="JEMS Logo" className="w-16 h-8" />
                            </div>
                            <div className="hidden lg:block">
                                <h1 className="text-white font-bold text-lg">JEMS</h1>
                                <p className="text-yellow-200 text-xs">Economics & Management Sciences</p>
                            </div>
                        </Link>

                        {/* Desktop Navigation */}
                        <nav className="hidden md:flex items-center space-x-6">
                            <Link
                                to="/"
                                className={`flex items-center space-x-1 py-2 px-3 rounded-md border-b-2 transition-all duration-200 ${isActive('/') ? 'border-green-400 text-green-400 bg-white bg-opacity-10' : 'border-transparent text-white hover:text-green-400 hover:bg-white hover:bg-opacity-10'}`}
                            >
                                <span className="font-medium">{t('nav.home')}</span>
                            </Link>
                            <Link
                                to="/about"
                                className={`flex items-center space-x-1 py-2 px-3 rounded-md border-b-2 transition-all duration-200 ${isActive('/about') ? 'border-yellow-400 text-yellow-400 bg-white bg-opacity-10' : 'border-transparent text-white hover:text-yellow-400 hover:bg-white hover:bg-opacity-10'}`}
                            >
                                <span className="font-medium">{t('nav.about')}</span>
                            </Link>
                            <Link
                                to="/current-issue"
                                className={`flex items-center space-x-1 py-2 px-3 rounded-md border-b-2 transition-all duration-200 ${isActive('/current-issue') ? 'border-yellow-400 text-yellow-400 bg-white bg-opacity-10' : 'border-transparent text-white hover:text-yellow-400 hover:bg-white hover:bg-opacity-10'}`}
                            >
                                <span className="font-medium">{t('nav.currentIssue')}</span>
                            </Link>
                            <Link
                                to="/browse"
                                className={`flex items-center space-x-1 py-2 px-3 rounded-md border-b-2 transition-all duration-200 ${isActive('/browse') ? 'border-jems-gold text-jems-gold bg-white bg-opacity-10' : 'border-transparent text-white hover:text-jems-gold hover:bg-white hover:bg-opacity-10'}`}
                            >
                                <span className="font-medium">{t('nav.browse')}</span>
                            </Link>
                            <Link
                                to="/guidelines"
                                className={`flex items-center space-x-1 py-2 px-3 rounded-md border-b-2 transition-all duration-200 ${isActive('/guidelines') ? 'border-jems-gold text-jems-gold bg-white bg-opacity-10' : 'border-transparent text-white hover:text-jems-gold hover:bg-white hover:bg-opacity-10'}`}
                            >
                                <span className="font-medium">{t('nav.guidelines')}</span>
                            </Link>
                            <Link
                                to="/editorial-board"
                                className={`flex items-center space-x-1 py-2 px-3 rounded-md border-b-2 transition-all duration-200 ${isActive('/editorial-board') ? 'border-jems-gold text-jems-gold bg-white bg-opacity-10' : 'border-transparent text-white hover:text-jems-gold hover:bg-white hover:bg-opacity-10'}`}
                            >
                                <span className="font-medium">{t('nav.editorialBoard')}</span>
                            </Link>
                            {/* Language Toggle */}
                            <button
                                onClick={toggleLanguage}
                                className="flex items-center space-x-2 text-white hover:text-jems-gold transition-colors py-2 px-3 rounded-md hover:bg-white hover:bg-opacity-10"
                            >
                                <FaLanguage className="text-lg" />
                                <span className="font-medium">{i18n.language === 'en' ? 'FR' : 'EN'}</span>
                            </button>
                            <Link
                                to="/login"
                                className="flex items-center space-x-2 bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-yellow-500 hover:text-blue-900 transition-all duration-200 font-medium shadow-md"
                            >
                                <FaUserGraduate />
                                <span>{t('nav.authorLogin')}</span>
                            </Link>
                        </nav>

                        {/* Mobile menu button */}
                        <button
                            className="md:hidden text-white hover:text-jems-gold transition-colors"
                            onClick={toggleMenu}
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>
                    </div>

                    {/* Mobile Navigation */}
                    {isMenuOpen && (
                        <nav className="md:hidden bg-blue-800 shadow-lg border-t border-orange-400">
                            <Link
                                to="/"
                                className={`block py-3 px-4 border-l-4 transition-all ${isActive('/') ? 'border-orange-400 text-orange-300 font-bold bg-orange-400 bg-opacity-10' : 'border-transparent text-white hover:text-orange-300 hover:border-orange-400 hover:bg-orange-400 hover:bg-opacity-10'}`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                {t('nav.home')}
                            </Link>
                            <Link
                                to="/about"
                                className={`block py-3 px-4 border-l-4 transition-all ${isActive('/about') ? 'border-orange-400 text-orange-300 font-bold bg-orange-400 bg-opacity-10' : 'border-transparent text-white hover:text-orange-300 hover:border-orange-400 hover:bg-orange-400 hover:bg-opacity-10'}`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                {t('nav.about')}
                            </Link>
                            <Link
                                to="/current-issue"
                                className={`block py-3 px-4 border-l-4 transition-all ${isActive('/current-issue') ? 'border-orange-400 text-orange-300 font-bold bg-orange-400 bg-opacity-10' : 'border-transparent text-white hover:text-orange-300 hover:border-orange-400 hover:bg-orange-400 hover:bg-opacity-10'}`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                {t('nav.currentIssue')}
                            </Link>
                            <Link
                                to="/browse"
                                className={`block py-3 px-4 border-l-4 transition-all ${isActive('/browse') ? 'border-orange-400 text-orange-300 font-bold bg-orange-400 bg-opacity-10' : 'border-transparent text-white hover:text-orange-300 hover:border-orange-400 hover:bg-orange-400 hover:bg-opacity-10'}`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                {t('nav.browse')}
                            </Link>
                            <Link
                                to="/guidelines"
                                className={`block py-3 px-4 border-l-4 transition-all ${isActive('/guidelines') ? 'border-orange-400 text-orange-300 font-bold bg-orange-400 bg-opacity-10' : 'border-transparent text-white hover:text-orange-300 hover:border-orange-400 hover:bg-orange-400 hover:bg-opacity-10'}`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                {t('nav.guidelines')}
                            </Link>
                            <Link
                                to="/editorial-board"
                                className={`block py-3 px-4 border-l-4 transition-all ${isActive('/editorial-board') ? 'border-orange-400 text-orange-300 font-bold bg-orange-400 bg-opacity-10' : 'border-transparent text-white hover:text-orange-300 hover:border-orange-400 hover:bg-orange-400 hover:bg-opacity-10'}`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                {t('nav.editorialBoard')}
                            </Link>
                            {/* Mobile Language Toggle */}
                            <button
                                onClick={() => {
                                    toggleLanguage();
                                    setIsMenuOpen(false);
                                }}
                                className="flex items-center space-x-2 py-3 px-4 text-white hover:text-orange-300 hover:bg-orange-400 hover:bg-opacity-10 transition-colors w-full text-left"
                            >
                                <FaLanguage className="text-lg" />
                                <span>{i18n.language === 'en' ? 'FR' : 'EN'}</span>
                            </button>
                            <div className="px-4 py-2">
                                <Link
                                    to="/login"
                                    className="block py-3 px-4 bg-orange-500 text-white rounded-lg hover:bg-green-600 hover:text-white transition-all text-center font-medium shadow-md"
                                    onClick={() => setIsMenuOpen(false)}
                                >
                                    {t('nav.authorLogin')}
                                </Link>
                            </div>
                        </nav>
                    )}
                </div>
            </header>
        </>
    );
};

export default Header;