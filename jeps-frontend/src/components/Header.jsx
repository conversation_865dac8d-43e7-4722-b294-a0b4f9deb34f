import { Link, useLocation } from "react-router-dom";
import { FaUniversity, FaBook<PERSON>pen, FaUserGraduate, FaSearch, FaLanguage } from "react-icons/fa";
import { useState } from "react";
import { useTranslation } from 'react-i18next';
import LoadingSpinner from './common/LoadingSpinner';

const Header = () => {
    const location = useLocation();
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isChangingLanguage, setIsChangingLanguage] = useState(false);
    const { t, i18n } = useTranslation();
    const isActive = (path) => location.pathname === path;

    const toggleMenu = () => {
        setIsMenuOpen(!isMenuOpen);
    };

    const toggleLanguage = () => {
        setIsChangingLanguage(true);
        const newLang = i18n.language === 'en' ? 'fr' : 'en';
        
        // Change language after loading completes
        setTimeout(() => {
            i18n.changeLanguage(newLang);
            setIsChangingLanguage(false);
        }, 3000);
    };

    return (
        <>
            {/* Language Change Loading Overlay */}
            {isChangingLanguage && (
                <div className="fixed inset-0 bg-black bg-opacity-20 z-50 flex items-center justify-center">
                    <div className="flex flex-col items-center">
                        <LoadingSpinner size="small" className="text-blue-600 w-6 h-6" />
                        <p className="mt-2 text-sm text-gray-600">{t('common.loading')}</p>
                    </div>
                </div>
            )}

            <header className="bg-white shadow-sm sticky top-0 z-40">
                <div className="container mx-auto px-4">
                    <div className="flex justify-between items-center py-4">
                        {/* Logo */}
                        <Link to="/" className="flex items-center space-x-2">
                            <img src="assets/jepsLogo.png" alt="Logo" className="w-20 h-10" />
                        </Link>

                        {/* Desktop Navigation */}
                        <nav className="hidden md:flex items-center space-x-8">
                            <Link 
                                to="/" 
                                className={`flex items-center space-x-1 py-2 px-1 border-b-2 ${isActive('/') ? 'border-blue-600 text-blue-600' : 'border-transparent text-gray-600 hover:text-blue-600'}`}
                            >
                                <span>{t('nav.home')}</span>
                            </Link>
                            <Link 
                                to="/about" 
                                className={`flex items-center space-x-1 py-2 px-1 border-b-2 ${isActive('/about') ? 'border-blue-600 text-blue-600' : 'border-transparent text-gray-600 hover:text-blue-600'}`}
                            >
                                <span>{t('nav.about')}</span>
                            </Link>
                            <Link 
                                to="/current-issue" 
                                className={`flex items-center space-x-1 py-2 px-1 border-b-2 ${isActive('/current-issue') ? 'border-blue-600 text-blue-600' : 'border-transparent text-gray-600 hover:text-blue-600'}`}
                            >
                                <span>{t('nav.currentIssue')}</span>
                            </Link>
                            <Link 
                                to="/browse" 
                                className={`flex items-center space-x-1 py-2 px-1 border-b-2 ${isActive('/browse') ? 'border-blue-600 text-blue-600' : 'border-transparent text-gray-600 hover:text-blue-600'}`}
                            >
                                <span>{t('nav.browse')}</span>
                            </Link>
                            <Link 
                                to="/guidelines" 
                                className={`flex items-center space-x-1 py-2 px-1 border-b-2 ${isActive('/guidelines') ? 'border-blue-600 text-blue-600' : 'border-transparent text-gray-600 hover:text-blue-600'}`}
                            >
                                <span>{t('nav.guidelines')}</span>
                            </Link>
                            <Link 
                                to="/editorial-board" 
                                className={`flex items-center space-x-1 py-2 px-1 border-b-2 ${isActive('/editorial-board') ? 'border-blue-600 text-blue-600' : 'border-transparent text-gray-600 hover:text-blue-600'}`}
                            >
                                <span>{t('nav.editorialBoard')}</span>
                            </Link>
                            {/* Language Toggle */}
                            <button
                                onClick={toggleLanguage}
                                className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors"
                            >
                                <FaLanguage className="text-xl" />
                                <span>{i18n.language === 'en' ? 'FR' : 'EN'}</span>
                            </button>
                            <Link 
                                to="/login" 
                                className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                            >
                                <FaUserGraduate />
                                <span>{t('nav.authorLogin')}</span>
                            </Link>
                        </nav>

                        {/* Mobile menu button */}
                        <button 
                            className="md:hidden text-gray-600 hover:text-blue-600" 
                            onClick={toggleMenu}
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>
                    </div>

                    {/* Mobile Navigation */}
                    {isMenuOpen && (
                        <nav className="md:hidden bg-white shadow-md">
                            <Link 
                                to="/" 
                                className={`block py-2 px-4 ${isActive('/') ? 'text-blue-600 font-bold' : 'text-gray-600 hover:text-blue-600'}`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                {t('nav.home')}
                            </Link>
                            <Link 
                                to="/about" 
                                className={`block py-2 px-4 ${isActive('/about') ? 'text-blue-600 font-bold' : 'text-gray-600 hover:text-blue-600'}`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                {t('nav.about')}
                            </Link>
                            <Link 
                                to="/current-issue" 
                                className={`block py-2 px-4 ${isActive('/current-issue') ? 'text-blue-600 font-bold' : 'text-gray-600 hover:text-blue-600'}`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                {t('nav.currentIssue')}
                            </Link>
                            <Link 
                                to="/browse" 
                                className={`block py-2 px-4 ${isActive('/browse') ? 'text-blue-600 font-bold' : 'text-gray-600 hover:text-blue-600'}`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                {t('nav.browse')}
                            </Link>
                            <Link 
                                to="/guidelines" 
                                className={`block py-2 px-4 ${isActive('/guidelines') ? 'text-blue-600 font-bold' : 'text-gray-600 hover:text-blue-600'}`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                {t('nav.guidelines')}
                            </Link>
                            <Link 
                                to="/editorial-board" 
                                className={`block py-2 px-4 ${isActive('/editorial-board') ? 'text-blue-600 font-bold' : 'text-gray-600 hover:text-blue-600'}`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                {t('nav.editorialBoard')}
                            </Link>
                            {/* Mobile Language Toggle */}
                            <button
                                onClick={() => {
                                    toggleLanguage();
                                    setIsMenuOpen(false);
                                }}
                                className="flex items-center space-x-2 py-2 px-4 text-gray-600 hover:text-blue-600 transition-colors"
                            >
                                <FaLanguage className="text-xl" />
                                <span>{i18n.language === 'en' ? 'FR' : 'EN'}</span>
                            </button>
                            <Link 
                                to="/login" 
                                className="block py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                onClick={() => setIsMenuOpen(false)}
                            >
                                {t('nav.authorLogin')}
                            </Link>
                        </nav>
                    )}
                </div>
            </header>
        </>
    );
};

export default Header;