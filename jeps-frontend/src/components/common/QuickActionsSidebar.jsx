import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useSidebar } from '../../layouts/PublicLayout';
import {
  FaPaperPlane,
  FaSearch,
  FaNewspaper,
  FaBook,
  FaUsers,
  FaEnvelope,
  FaChevronRight,
  FaChevronLeft,
  FaDownload,
  FaBell,
  FaQuestionCircle,
  FaGlobe,
  FaTwitter,
  FaFacebook,
  FaLinkedin,
  FaInstagram
} from 'react-icons/fa';

const QuickActionsSidebar = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const { isCollapsed, setIsCollapsed } = useSidebar();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const quickActions = [
    {
      title: t('sidebar.submitManuscript'),
      description: t('sidebar.submitDescription'),
      icon: FaPaperPlane,
      link: '/register',
      color: 'bg-blue-900 hover:bg-blue-800',
      textColor: 'text-blue-900'
    },
    {
      title: t('sidebar.browseArticles'),
      description: t('sidebar.browseDescription'),
      icon: FaSearch,
      link: '/browse',
      color: 'bg-green-600 hover:bg-green-700',
      textColor: 'text-green-600'
    },
    {
      title: t('sidebar.currentIssue'),
      description: t('sidebar.currentIssueDescription'),
      icon: FaNewspaper,
      link: '/current-issue',
      color: 'bg-orange-500 hover:bg-orange-600',
      textColor: 'text-orange-600'
    },
    {
      title: t('sidebar.guidelines'),
      description: t('sidebar.guidelinesDescription'),
      icon: FaBook,
      link: '/guidelines',
      color: 'bg-yellow-500 hover:bg-yellow-600',
      textColor: 'text-yellow-600'
    }
  ];

  const quickLinks = [
    { title: t('sidebar.editorialBoard'), icon: FaUsers, link: '/editorial-board' },
    { title: t('sidebar.contact'), icon: FaEnvelope, link: '/contact' },
    { title: t('sidebar.announcements'), icon: FaBell, link: '/announcements' },
    { title: t('sidebar.faq'), icon: FaQuestionCircle, link: '/faq' },
    { title: t('sidebar.archive'), icon: FaDownload, link: '/archive' }
  ];

  const socialLinks = [
    { icon: FaTwitter, link: '#', color: 'text-blue-400 hover:text-blue-500' },
    { icon: FaFacebook, link: '#', color: 'text-blue-600 hover:text-blue-700' },
    { icon: FaLinkedin, link: '#', color: 'text-blue-700 hover:text-blue-800' },
    { icon: FaInstagram, link: '#', color: 'text-pink-500 hover:text-pink-600' }
  ];

  return (
    <>
      {/* Mobile Floating Action Button */}
      <div className="lg:hidden fixed bottom-6 right-6 z-50">
        <div className="relative">
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="bg-gradient-to-r from-blue-900 to-blue-800 hover:from-blue-800 hover:to-blue-700 text-white p-4 rounded-full shadow-lg transition-all duration-300 border-2 border-orange-400"
          >
            <FaPaperPlane className="w-5 h-5" />
          </button>
          
          {mobileMenuOpen && (
            <div className="absolute bottom-16 right-0 bg-white rounded-lg shadow-xl border border-orange-200 p-4 w-64">
              <h3 className="font-semibold text-blue-900 mb-3">Quick Actions</h3>
              <div className="space-y-2">
                {quickActions.slice(0, 3).map((action, index) => {
                  const Icon = action.icon;
                  return (
                    <Link
                      key={index}
                      to={action.link}
                      className="flex items-center space-x-3 p-2 rounded-lg hover:bg-orange-50 transition-colors"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <div className={`p-2 rounded-lg ${action.color} text-white`}>
                        <Icon className="w-3 h-3" />
                      </div>
                      <span className="text-sm font-medium text-blue-900">{action.title}</span>
                    </Link>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Desktop Sidebar */}
      <div className={`hidden lg:block fixed right-0 top-20 h-[calc(100vh-5rem)] z-40 transition-all duration-300 ${
        isCollapsed ? 'w-16' : 'w-80'
      }`}>
        {/* Toggle Button */}
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="absolute -left-4 top-6 bg-white shadow-lg rounded-full p-2 border border-orange-300 hover:bg-orange-50 transition-colors z-50"
        >
          {isCollapsed ? (
            <FaChevronLeft className="w-4 h-4 text-gray-600" />
          ) : (
            <FaChevronRight className="w-4 h-4 text-gray-600" />
          )}
        </button>

        {/* Sidebar Content */}
        <div className={`bg-white shadow-xl border-l border-orange-300 h-full overflow-y-auto ${
          isCollapsed ? 'px-2' : 'px-6'
        } py-6`}>
          
          {!isCollapsed && (
            <>
              {/* Header */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">
                  {t('sidebar.quickActions')}
                </h3>
                <p className="text-sm text-gray-700">
                  {t('sidebar.quickActionsDescription')}
                </p>
              </div>

              {/* Quick Actions */}
              <div className="space-y-4 mb-8">
                {quickActions.map((action, index) => {
                  const Icon = action.icon;
                  const isActive = location.pathname === action.link;
                  
                  return (
                    <Link
                      key={index}
                      to={action.link}
                      className={`block p-4 rounded-xl border transition-all duration-200 ${
                        isActive
                          ? 'border-orange-300 bg-orange-50 shadow-md'
                          : 'border-orange-200 hover:border-orange-300 hover:shadow-md'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`p-2 rounded-lg ${action.color} text-white flex-shrink-0`}>
                          <Icon className="w-4 h-4" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className={`font-medium text-sm ${
                            isActive ? action.textColor : 'text-blue-900'
                          }`}>
                            {action.title}
                          </h4>
                          <p className="text-xs text-gray-700 mt-1 overflow-hidden" style={{
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical'
                          }}>
                            {action.description}
                          </p>
                        </div>
                      </div>
                    </Link>
                  );
                })}
              </div>

              {/* Quick Links */}
              <div className="mb-8">
                <h4 className="text-sm font-semibold text-gray-700 mb-3 uppercase tracking-wide">
                  {t('sidebar.quickLinks')}
                </h4>
                <div className="space-y-2">
                  {quickLinks.map((link, index) => {
                    const Icon = link.icon;
                    const isActive = location.pathname === link.link;
                    
                    return (
                      <Link
                        key={index}
                        to={link.link}
                        className={`flex items-center space-x-3 p-2 rounded-lg transition-colors ${
                          isActive 
                            ? 'bg-blue-50 text-blue-600' 
                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800'
                        }`}
                      >
                        <Icon className="w-4 h-4 flex-shrink-0" />
                        <span className="text-sm font-medium">{link.title}</span>
                      </Link>
                    );
                  })}
                </div>
              </div>

              {/* Journal Info */}
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-4 mb-6">
                <div className="flex items-center space-x-2 mb-2">
                  <FaGlobe className="w-4 h-4 text-blue-600" />
                  <h4 className="text-sm font-semibold text-blue-800">
                    {t('sidebar.journalInfo')}
                  </h4>
                </div>
                <p className="text-xs text-blue-700 mb-3">
                  {t('sidebar.journalDescription')}
                </p>
                <div className="text-xs text-blue-600 space-y-1">
                  <div>ISSN: 2789-3456</div>
                  <div>Impact Factor: 2.45</div>
                  <div>Published: Bi-annually</div>
                </div>
              </div>

              {/* Social Links */}
              <div>
                <h4 className="text-sm font-semibold text-gray-700 mb-3 uppercase tracking-wide">
                  {t('sidebar.followUs')}
                </h4>
                <div className="flex space-x-3">
                  {socialLinks.map((social, index) => {
                    const Icon = social.icon;
                    return (
                      <a
                        key={index}
                        href={social.link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`p-2 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors ${social.color}`}
                      >
                        <Icon className="w-4 h-4" />
                      </a>
                    );
                  })}
                </div>
              </div>
            </>
          )}

          {/* Collapsed State */}
          {isCollapsed && (
            <div className="space-y-4">
              {quickActions.map((action, index) => {
                const Icon = action.icon;
                const isActive = location.pathname === action.link;
                
                return (
                  <Link
                    key={index}
                    to={action.link}
                    className={`block p-3 rounded-lg transition-colors ${
                      isActive 
                        ? 'bg-blue-50 text-blue-600' 
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                    title={action.title}
                  >
                    <Icon className="w-5 h-5 mx-auto" />
                  </Link>
                );
              })}
              
              <div className="border-t border-gray-200 pt-4 mt-4">
                {quickLinks.slice(0, 3).map((link, index) => {
                  const Icon = link.icon;
                  const isActive = location.pathname === link.link;
                  
                  return (
                    <Link
                      key={index}
                      to={link.link}
                      className={`block p-2 rounded-lg transition-colors mb-2 ${
                        isActive 
                          ? 'bg-blue-50 text-blue-600' 
                          : 'text-gray-600 hover:bg-gray-50'
                      }`}
                      title={link.title}
                    >
                      <Icon className="w-4 h-4 mx-auto" />
                    </Link>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default QuickActionsSidebar; 