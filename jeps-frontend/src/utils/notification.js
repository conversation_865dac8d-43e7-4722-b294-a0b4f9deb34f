// Simple notification utility
export const showNotification = (type, message) => {
  // For now, just use console.log - this can be enhanced later with a proper toast library
  console.log(`[${type.toUpperCase()}]: ${message}`);
  
  // You can replace this with a proper toast notification library like react-toastify
  // For now, we'll use a simple alert for errors
  if (type === 'error') {
    console.error(message);
  }
};

export default { showNotification }; 