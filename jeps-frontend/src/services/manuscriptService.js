import axios from 'axios';
import { issueService } from './issueService';
import { volumeService } from './volumeService';

const API_URL = import.meta.env.VITE_API_URL;

export const manuscriptService = {
  async submitManuscript(formData) {
    console.log(...formData)
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(`${API_URL}/api/v1/manuscripts`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${token}`
        },
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  async resubmitManuscript(id, formData) {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(`${API_URL}/api/v1/manuscripts/${id}/resubmit`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${token}`
        },
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  async getManuscriptById(id) {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/v1/manuscripts/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  async getAuthorManuscripts() {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/v1/author/manuscripts`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        params: {
          include: 'reviews.reviewer,status'
        }
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  getIssues: async () => {
    try {
      const response = await axios.get(`${API_URL}/api/v1/issues`);
      return response.data;
    } catch (error) {
      console.error('Error fetching issues:', error);
      throw error;
    }
  },

  getVolumes: volumeService.getAllVolumes,

  async directPublish(formData) {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(`${API_URL}/api/v1/manuscripts/direct-publish`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${token}`
        },
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  getPublishedManuscripts: async (params = new URLSearchParams()) => {
    try {
      const response = await axios.get(`${API_URL}/api/v1/published-manuscripts?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching published manuscripts:', error);
      throw error;
    }
  },

  getManuscriptsByIssue: async (issueId) => {
    try {
      const response = await axios.get(`${API_URL}/api/v1/manuscripts/by-issue/${issueId}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },
}; 