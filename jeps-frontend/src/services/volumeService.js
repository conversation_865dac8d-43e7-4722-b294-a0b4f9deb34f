import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL;

// Create axios instance with base config
const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  }
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  console.log(token);
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor to handle token refresh
apiClient.interceptors.response.use(
  response => response,
  async error => {
    const originalRequest = error.config;
    
    // If unauthorized and not already retried
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        // Attempt to refresh token
        const refreshResponse = await axios.post(`${API_URL}/api/v1/auth/refresh`, {}, {
          withCredentials: true
        });
        
        const newToken = refreshResponse.data.token;
        localStorage.setItem('token', newToken);

        
        // Retry original request with new token
        originalRequest.headers.Authorization = `Bearer ${newToken}`;
        return apiClient(originalRequest);
      } catch (refreshError) {
        return Promise.reject(refreshError);
      }
    }
    
    // For other errors, just reject
    return Promise.reject(error);
  }
);

export const volumeService = {
  async getAllVolumes() {
    try {
      const response = await apiClient.get('/api/v1/volumes', {
        params: { include: 'issues' }
      });
      return response.data.data;
    } catch (error) {
      throw this.handleError(error);
    }
  },

  async getVolume(id) {
    try {
      const response = await apiClient.get(`/api/v1/volumes/${id}`);
      return response.data.data;
    } catch (error) {
      throw this.handleError(error);
    }
  },

  async createVolume(volumeData) {
    try {

        console.log(volumeData);

      const response = await apiClient.post('/api/v1/volumes', volumeData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data.data;
    } catch (error) {
      throw this.handleError(error);
    }
  },

  async updateVolume(id, volumeData) {
    try {
      const formData = new FormData();
      Object.keys(volumeData).forEach(key => {
        formData.append(key, volumeData[key]);
      });
      formData.append('_method', 'PUT');

      const response = await apiClient.post(`/api/v1/volumes/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data.data;
    } catch (error) {
      throw this.handleError(error);
    }
  },

  async deleteVolume(id) {
    try {
      const response = await apiClient.delete(`/api/v1/volumes/${id}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  },

  async publishVolume(id) {
    try {
      const response = await apiClient.put(`/api/v1/volumes/${id}`, { 
        published: true 
      });
      return response.data.data;
    } catch (error) {
      throw this.handleError(error);
    }
  },

  handleError(error) {
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      
      if (status === 401) {
        // Special handling for unauthorized
        return {
          message: 'Session expired. Please login again.',
          status,
          data
        };
      }
      
      return {
        message: data.message || 'An error occurred',
        status,
        data
      };
    } else if (error.request) {
      // Request was made but no response
      return {
        message: 'No response from server. Please check your connection.',
        status: 0
      };
    } else {
      // Something else happened
      return {
        message: error.message || 'An unexpected error occurred',
        status: -1
      };
    }
  }
};