import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

class AnnouncementService {
  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Add auth token to requests
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
  }

  // Get all announcements (public)
  async getAnnouncements(params = {}) {
    const response = await this.api.get('/api/v1/content/announcement', { params });
    return response.data;
  }

  // Get all content for admin/editor management
  async getAllContent(params = {}) {
    const response = await this.api.get('/api/v1/editor/content', { params });
    return response.data;
  }

  // Create new announcement
  async createAnnouncement(data) {
    const response = await this.api.post('/api/v1/editor/content', {
      ...data,
      type: 'announcement'
    });
    return response.data;
  }

  // Update announcement
  async updateAnnouncement(id, data) {
    const response = await this.api.put(`/api/v1/editor/content/${id}`, data);
    return response.data;
  }

  // Delete announcement
  async deleteAnnouncement(id) {
    const response = await this.api.delete(`/api/v1/editor/content/${id}`);
    return response.data;
  }

  // Get single announcement
  async getAnnouncement(id) {
    const response = await this.api.get(`/api/v1/editor/content/${id}`);
    return response.data;
  }

  // Bulk actions
  async bulkAction(action, ids) {
    const response = await this.api.post('/api/v1/editor/content/bulk-action', {
      action,
      ids
    });
    return response.data;
  }

  // Get stats
  async getStats() {
    const response = await this.api.get('/api/v1/editor/content/stats');
    return response.data;
  }
}

export default new AnnouncementService(); 