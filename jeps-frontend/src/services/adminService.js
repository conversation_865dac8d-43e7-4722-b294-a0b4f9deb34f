const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

class AdminService {
  constructor() {
    this.baseURL = `${API_BASE_URL}/api/v1`;
  }

  // Helper method to get auth headers
  getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  // Helper method to handle API responses
  async handleResponse(response) {
    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Network error' }));
      throw new Error(error.message || `HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  // Payment Management
  async getPayments() {
    try {
      const response = await fetch(`${this.baseURL}/admin/payments`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching payments:', error);
      throw error;
    }
  }

  async getPaymentStats() {
    try {
      const response = await fetch(`${this.baseURL}/admin/payment-stats`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching payment stats:', error);
      throw error;
    }
  }

  // Manuscript Management
  async getManuscripts() {
    try {
      const response = await fetch(`${this.baseURL}/admin/manuscripts`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching manuscripts:', error);
      throw error;
    }
  }

  async getManuscriptStats() {
    try {
      const response = await fetch(`${this.baseURL}/admin/manuscript-stats`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching manuscript stats:', error);
      throw error;
    }
  }

  async deleteManuscript(id) {
    try {
      const response = await fetch(`${this.baseURL}/admin/manuscripts/${id}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error deleting manuscript:', error);
      throw error;
    }
  }

  async updateManuscriptStatus(id, status) {
    try {
      const response = await fetch(`${this.baseURL}/admin/manuscripts/${id}/status`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({ status })
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error updating manuscript status:', error);
      throw error;
    }
  }

  async downloadManuscript(id) {
    try {
      const response = await fetch(`${this.baseURL}/admin/manuscripts/${id}/download`, {
        headers: this.getAuthHeaders()
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return response.blob();
    } catch (error) {
      console.error('Error downloading manuscript:', error);
      throw error;
    }
  }

  // Review Management
  async getReviews() {
    try {
      const response = await fetch(`${this.baseURL}/admin/reviews`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching reviews:', error);
      throw error;
    }
  }

  async getReviewStats() {
    try {
      const response = await fetch(`${this.baseURL}/admin/review-stats`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching review stats:', error);
      throw error;
    }
  }

  async deleteReview(id) {
    try {
      const response = await fetch(`${this.baseURL}/admin/reviews/${id}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error deleting review:', error);
      throw error;
    }
  }

  // User Management
  async getUsers() {
    try {
      const response = await fetch(`${this.baseURL}/admin/users`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  }

  async getUserStats() {
    try {
      const response = await fetch(`${this.baseURL}/admin/user-stats`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching user stats:', error);
      throw error;
    }
  }

  async updateUserRole(userId, role) {
    try {
      const response = await fetch(`${this.baseURL}/admin/users/${userId}/role`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({ role })
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error updating user role:', error);
      throw error;
    }
  }

  async deleteUser(id) {
    try {
      const response = await fetch(`${this.baseURL}/admin/users/${id}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  // System Analytics
  async getSystemAnalytics(params = {}) {
    try {
      const queryParams = new URLSearchParams(params);
      const response = await fetch(`${this.baseURL}/admin/analytics?${queryParams}`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching system analytics:', error);
      throw error;
    }
  }

  // System Metrics for Graphs
  async getSystemMetrics(params = {}) {
    try {
      const queryParams = new URLSearchParams(params);
      const response = await fetch(`${this.baseURL}/admin/metrics?${queryParams}`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching system metrics:', error);
      throw error;
    }
  }

  async getSystemResources() {
    try {
      const response = await fetch(`${this.baseURL}/admin/metrics/resources`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching system resources:', error);
      throw error;
    }
  }

  async getActivityAnalytics(params = {}) {
    try {
      const queryParams = new URLSearchParams(params);
      const response = await fetch(`${this.baseURL}/admin/metrics/activity?${queryParams}`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching activity analytics:', error);
      throw error;
    }
  }

  async getDashboardStats() {
    try {
      const response = await fetch(`${this.baseURL}/admin/dashboard-stats`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      throw error;
    }
  }

  // Activity Logs (new implementation)
  async getActivityLogs(params = {}) {
    try {
      const queryParams = new URLSearchParams(params);
      const response = await fetch(`${this.baseURL}/admin/activity-logs?${queryParams}`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching activity logs:', error);
      throw error;
    }
  }

  async getActivityLogStats(params = {}) {
    try {
      const queryParams = new URLSearchParams(params);
      const response = await fetch(`${this.baseURL}/admin/activity-logs/stats?${queryParams}`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching activity log stats:', error);
      throw error;
    }
  }

  async clearActivityLogs(data = {}) {
    try {
      const response = await fetch(`${this.baseURL}/admin/activity-logs/clear`, {
        method: 'DELETE',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({ confirm: true, ...data })
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error clearing activity logs:', error);
      throw error;
    }
  }

  async exportActivityLogs(format = 'csv', params = {}) {
    try {
      const queryParams = new URLSearchParams({ format, ...params });
      const response = await fetch(`${this.baseURL}/admin/activity-logs/export?${queryParams}`, {
        headers: this.getAuthHeaders()
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      // Return the response text for CSV or JSON for JSON format
      if (format === 'json') {
        return response.json();
      } else {
        return response.text();
      }
    } catch (error) {
      console.error('Error exporting activity logs:', error);
      throw error;
    }
  }

  // System Logs (legacy - kept for backward compatibility)
  async getSystemLogs(params = {}) {
    try {
      const queryParams = new URLSearchParams(params);
      const response = await fetch(`${this.baseURL}/admin/logs?${queryParams}`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching system logs:', error);
      throw error;
    }
  }

  async clearSystemLogs() {
    try {
      const response = await fetch(`${this.baseURL}/admin/logs/clear`, {
        method: 'DELETE',
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error clearing system logs:', error);
      throw error;
    }
  }

  async exportSystemLogs(format = 'csv') {
    try {
      const response = await fetch(`${this.baseURL}/admin/logs/export?format=${format}`, {
        headers: this.getAuthHeaders()
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      // Return the response text for CSV/TXT or JSON for JSON format
      if (format === 'json') {
        return response.json();
      } else {
        return response.text();
      }
    } catch (error) {
      console.error('Error exporting system logs:', error);
      throw error;
    }
  }

  // Settings Management
  async getSettings() {
    try {
      const response = await fetch(`${this.baseURL}/admin/settings`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching settings:', error);
      throw error;
    }
  }

  async updateSetting(key, value) {
    try {
      const response = await fetch(`${this.baseURL}/admin/settings/${key}`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({ value })
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error updating setting:', error);
      throw error;
    }
  }

  async updateSettings(settings) {
    try {
      const response = await fetch(`${this.baseURL}/admin/settings`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(settings)
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error updating settings:', error);
      throw error;
    }
  }

  // Content Management
  async getContent(params = {}) {
    try {
      const queryParams = new URLSearchParams(params);
      const response = await fetch(`${this.baseURL}/admin/content?${queryParams}`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching content:', error);
      throw error;
    }
  }

  async getContentStats() {
    try {
      const response = await fetch(`${this.baseURL}/admin/content/stats`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching content stats:', error);
      throw error;
    }
  }

  async createContent(data) {
    try {
      const response = await fetch(`${this.baseURL}/admin/content`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(data)
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error creating content:', error);
      throw error;
    }
  }

  async getContentById(id) {
    try {
      const response = await fetch(`${this.baseURL}/admin/content/${id}`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching content by ID:', error);
      throw error;
    }
  }

  async updateContent(contentId, data) {
    try {
      const response = await fetch(`${this.baseURL}/admin/content/${contentId}`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(data)
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error updating content:', error);
      throw error;
    }
  }

  async deleteContent(contentId) {
    try {
      const response = await fetch(`${this.baseURL}/admin/content/${contentId}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error deleting content:', error);
      throw error;
    }
  }

  async bulkContentAction(action, ids) {
    try {
      const response = await fetch(`${this.baseURL}/admin/content/bulk-action`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({ action, ids })
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error performing bulk action:', error);
      throw error;
    }
  }

  // Public content access
  async getPublicContent(type, language = 'en') {
    try {
      const response = await fetch(`${this.baseURL}/content/${type}?language=${language}`);
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching public content:', error);
      throw error;
    }
  }

  // Backup and Export
  async createBackup() {
    try {
      const response = await fetch(`${this.baseURL}/admin/backup`, {
        method: 'POST',
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error creating backup:', error);
      throw error;
    }
  }

  async exportData(type) {
    try {
      const response = await fetch(`${this.baseURL}/admin/export/${type}`, {
        headers: this.getAuthHeaders()
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return response.blob();
    } catch (error) {
      console.error('Error exporting data:', error);
      throw error;
    }
  }

  // Notifications
  async sendNotification(data) {
    try {
      const response = await fetch(`${this.baseURL}/admin/notifications`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(data)
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error sending notification:', error);
      throw error;
    }
  }

  async getNotifications() {
    try {
      const response = await fetch(`${this.baseURL}/admin/notifications`, {
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const adminService = new AdminService();
export default adminService; 