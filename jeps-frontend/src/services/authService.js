import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL;

const authService = {
    async register(userData) {
        const response = await axios.post(`${API_URL}/api/v1/register`, userData);
        return response.data;
    },

    async login(credentials) {
        const response = await axios.post(`${API_URL}/api/v1/login`, credentials);
        if (response.data.data) {
            localStorage.setItem('token', response.data.data.token);
            localStorage.setItem('user', JSON.stringify(response.data.data.user));
        }
        return response.data;
    },

    async logout() {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                throw new Error('No token found');
            }
            
            const response = await axios.post(`${API_URL}/api/v1/logout`, {}, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            return response.data;
        } catch (error) {
            console.error('Logout error:', error);
            // Even if the server request fails, clear local storage
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            throw error;
        }
    },

    async forgotPassword(email) {
        const response = await axios.post(`${API_URL}/api/v1/forgot-password`, { email });
        return response.data;
    },

    async resetPassword(data) {
        const response = await axios.post(`${API_URL}/api/v1/reset-password`, data);
        return response.data;
    },

    async verifyEmail({ id, hash, expires, signature }) {
        console.log('Verifying email with params:', { id, hash, expires, signature });  
        const response = await axios.get(`${API_URL}/api/v1/auth/verify-email`, {
            params: {
                id,
                hash,
                expires,
                signature,
            }
        });
        return response.data;
    },

    async resendVerificationEmail(email) {
        const response = await axios.post(`${API_URL}/api/v1/auth/resend-verification`, { email });
        return response.data;
    },

    // Google Authentication
    async loginWithGoogle() {
        window.location.href = `${API_URL}/api/v1/auth/google`;
    },

    // ORCID Authentication
    async loginWithOrcid() {
        window.location.href = `${API_URL}/api/v1/auth/orcid`;
    },

    async handleGoogleCallback(code) {
        const response = await axios.get(`${API_URL}/api/v1/auth/google/callback?code=${code}`);
        if (response.data.data) {
            localStorage.setItem('token', response.data.data.token);
            localStorage.setItem('user', JSON.stringify(response.data.data.user));
        }
        return response.data;
    }
};

export default authService; 