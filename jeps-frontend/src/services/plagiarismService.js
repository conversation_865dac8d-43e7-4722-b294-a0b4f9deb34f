import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

class PlagiarismService {
  constructor() {
    this.apiClient = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Add auth token to requests
    this.apiClient.interceptors.request.use((config) => {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });
  }

  /**
   * Check document for plagiarism using external API
   * @param {string} manuscriptId - The manuscript ID
   * @param {File} file - The manuscript file
   * @returns {Promise} Plagiarism check result
   */
  async checkPlagiarism(manuscriptId, file) {
    try {
      const formData = new FormData();
      formData.append('manuscript_id', manuscriptId);
      formData.append('file', file);

      const response = await this.apiClient.post('/api/v1/plagiarism/check', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get plagiarism check result
   * @param {string} checkId - The plagiarism check ID
   * @returns {Promise} Plagiarism check result
   */
  async getCheckResult(checkId) {
    try {
      const response = await this.apiClient.get(`/api/v1/plagiarism/result/${checkId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get plagiarism history for a manuscript
   * @param {string} manuscriptId - The manuscript ID
   * @returns {Promise} Plagiarism check history
   */
  async getCheckHistory(manuscriptId) {
    try {
      const response = await this.apiClient.get(`/api/v1/plagiarism/history/${manuscriptId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  handleError(error) {
    if (error.response) {
      const message = error.response.data?.message || 'An error occurred';
      throw new Error(message);
    } else if (error.request) {
      throw new Error('Network error. Please check your connection.');
    } else {
      throw new Error('An unexpected error occurred');
    }
  }
}

export const plagiarismService = new PlagiarismService(); 