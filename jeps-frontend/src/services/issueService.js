import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL;

// Create axios instance with timeout and retry configuration
const apiClient = axios.create({
    baseURL: API_URL,
    timeout: 15000, // 15 seconds timeout
    headers: {
        'Content-Type': 'application/json',
    }
});

// Add request interceptor for retries
apiClient.interceptors.response.use(
    (response) => response,
    async (error) => {
        const config = error.config;
        
        // Only retry for network errors and server errors (not client errors)
        if (!config._retryCount && (
            error.code === 'NETWORK_ERROR' || 
            error.code === 'ECONNABORTED' ||
            (error.response?.status >= 500)
        )) {
            config._retryCount = (config._retryCount || 0) + 1;
            
            if (config._retryCount <= 3) {
                // Wait before retrying (exponential backoff)
                const delay = Math.pow(2, config._retryCount) * 1000;
                await new Promise(resolve => setTimeout(resolve, delay));
                return apiClient(config);
            }
        }
        
        return Promise.reject(error);
    }
);

export const issueService = {
    // Get current issue with improved error handling
    async getCurrentIssue(signal) {
        try {
            const response = await apiClient.get('/api/v1/issues/current', {
                signal, // Allow request cancellation
                timeout: 10000, // Shorter timeout for this critical endpoint
            });
            
            // The backend returns { issue: IssueResource, manuscripts: [...] }
            // We need to combine them into a single object for the frontend
            const { issue, manuscripts } = response.data;
            return {
                ...issue,
                manuscripts: manuscripts || []
            };
        } catch (error) {
            if (signal?.aborted) {
                throw new Error('Request was cancelled');
            }
            
            // Provide more specific error messages
            if (error.code === 'ECONNABORTED' || error.code === 'NETWORK_ERROR') {
                throw new Error('Network connection failed. Please check your internet connection and try again.');
            }
            
            if (error.response?.status === 404) {
                throw new Error('No current issue found');
            }
            
            if (error.response?.status >= 500) {
                throw new Error('Server error. Please try again later.');
            }
            
            throw error.response?.data || error;
        }
    },

    // Get all issues
    async getAllIssues() {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get(`${API_URL}/api/v1/issues`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            return response.data.data;
        } catch (error) {
            throw error.response?.data || error;
        }
    },

    // Get issues for a specific volume
    async getVolumeIssues(volumeId) {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get(`${API_URL}/api/v1/volumes/${volumeId}/issues`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            return response.data.data;
        } catch (error) {
            throw error.response?.data || error;
        }
    },

    // Get a single issue by ID
    async getIssue(id) {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get(`${API_URL}/api/v1/issues/${id}`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            return response.data.data;
        } catch (error) {
            throw error.response?.data || error;
        }
    },

    // Create a new issue
    async createIssue(issueData) {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.post(`${API_URL}/api/v1/issues`, issueData, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            return response.data.data;
        } catch (error) {
            throw error.response?.data || error;
        }
    },

    // Update an issue
    async updateIssue(id, issueData) {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.put(`${API_URL}/api/v1/issues/${id}`, issueData, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            return response.data.data;
        } catch (error) {
            throw error.response?.data || error;
        }
    },

    // Delete an issue
    async deleteIssue(id) {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.delete(`${API_URL}/api/v1/issues/${id}`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            return response.data;
        } catch (error) {
            throw error.response?.data || error;
        }
    },

    // Publish an issue
    async publishIssue(id) {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.put(`${API_URL}/api/v1/issues/${id}/publish`, {}, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            return response.data.data;
        } catch (error) {
            throw error.response?.data || error;
        }
    },

    // Set an issue as current
    async setCurrent(id) {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.put(`${API_URL}/api/v1/issues/${id}/set-current`, {}, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            return response.data;
        } catch (error) {
            throw error.response?.data || error;
        }
    }
}; 