import axios from 'axios';

const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL + '/api/v1' || 'http://localhost:8000/api/v1',
  headers: {
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },
  withCredentials: true, // Needed for session/cookie auth
});

// Function to get CSRF cookie (for session-based auth)
const getCsrfCookie = async () => {
  try {
    await axios.get(`${import.meta.env.VITE_API_URL || 'http://localhost:8000'}/sanctum/csrf-cookie`, {
      withCredentials: true
    });
  } catch (error) {
    // Only log, don't block
    console.error('Failed to get CSRF cookie:', error);
  }
};

// Request interceptor
api.interceptors.request.use(
  async (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      // Use Bearer token for API token endpoints
      config.headers.Authorization = `Bearer ${token}`;
      config.withCredentials = false; // Don't send cookies for token auth
    } else {
      // Use session/cookie + CSRF for social auth endpoints
      await getCsrfCookie();
      config.withCredentials = true;
    }
    // Only set Content-Type for non-FormData requests
    if (!(config.data instanceof FormData)) {
      config.headers['Content-Type'] = 'application/json';
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.log(error);
    return Promise.reject(error);
  }
);

export { api }; 