import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL;

// Create axios instance with base config
const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  }
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export const submissionService = {
  async getAllSubmissions(page = 1, perPage = 10) {
    try {
      const response = await apiClient.get('/api/v1/manuscripts', {
        params: { 
          include: 'author,reviewers,reviews.reviewer,issue.volume,versions',
          page: page,
          per_page: perPage
        }
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  },

  async getAuthorSubmissions() {
    try {
      const response = await apiClient.get('/api/v1/manuscripts', {
        params: { 
          include: 'issue,issue.volume,status,reviews.reviewer' 
        }
      });
      return response.data.data;
    } catch (error) {
      throw this.handleError(error);
    }
  },

  async getSubmission(id) {
    try {
      const response = await apiClient.get(`/api/v1/manuscripts/${id}`, {
        params: { 
          include: 'author,reviewers,reviews.reviewer,issue.volume,versions' 
        }
      });
      return response.data.data;
    } catch (error) {
      throw this.handleError(error);
    }
  },

  async updateSubmissionStatus(id, status) {
    try {
      const response = await apiClient.put(`/api/v1/manuscripts/${id}/status`, { status });
      return response.data.data;
    } catch (error) {
      throw this.handleError(error);
    }
  },

  async assignReviewers(id, reviewerIds) {
    try {
      const response = await apiClient.post(`/api/v1/manuscripts/${id}/reviewers`, { reviewer_ids: reviewerIds });
      return response.data.data;
    } catch (error) {
      throw this.handleError(error);
    }
  },

  async getAvailableReviewers() {
    try {
      const response = await apiClient.get('/api/v1/reviewers');
      return response.data.data;
    } catch (error) {
      throw this.handleError(error);
    }
  },

  handleError(error) {
    if (error.response) {
      const { status, data } = error.response;
      
      if (status === 401) {
        return {
          message: 'Session expired. Please login again.',
          status,
          data
        };
      }
      
      return {
        message: data.message || 'An error occurred',
        status,
        data
      };
    } else if (error.request) {
      return {
        message: 'No response from server. Please check your connection.',
        status: 0
      };
    } else {
      return {
        message: error.message || 'An unexpected error occurred',
        status: -1
      };
    }
  },

  async publishSubmission(id) {
    try {
      const response = await apiClient.post(`/api/v1/manuscripts/${id}/publish`);
      return response.data.data;
    } catch (error) {
      throw this.handleError(error);
    }
  },

  async getPendingPayments() {
    try {
      const response = await apiClient.get('/api/v1/manuscripts', {
        params: { 
          status: 'accepted',
          payment_status: 'pending',
          include: 'author,issue.volume'
        }
      });
      return response.data.data;
    } catch (error) {
      throw this.handleError(error);
    }
  },

  async recordPayment(manuscriptId, paymentData) {
    try {
      const response = await apiClient.post(`/api/v1/manuscripts/${manuscriptId}/payment`, paymentData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  },

  async updatePayment(manuscriptId, paymentData) {
    try {
      const response = await apiClient.put(`/api/v1/manuscripts/${manuscriptId}/payment`, paymentData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  },

  async deletePayment(manuscriptId) {
    try {
      const response = await apiClient.delete(`/api/v1/manuscripts/${manuscriptId}/payment`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  },

  async getPayment(manuscriptId) {
    try {
      const response = await apiClient.get(`/api/v1/manuscripts/${manuscriptId}/payment`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }
}; 