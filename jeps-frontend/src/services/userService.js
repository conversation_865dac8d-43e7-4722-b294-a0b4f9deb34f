import axios from 'axios';
import { api } from './api';

const API_URL = import.meta.env.VITE_API_URL;

const userService = {
  // Get all users with optional role filter
  getUsers: async (role = null, page = 1, search = '') => {
    try {
      const token = localStorage.getItem('token');
      const params = {};
      if (role) params.role = role;
      if (page) params.page = page;
      if (search) params.search = search;
      const response = await axios.get(`${API_URL}/api/v1/users`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        params
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get all authors
  getAuthors: <AUTHORS>
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/v1/users/authors`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get all reviewers
  getReviewers: async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/v1/reviewers`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get user details
  getUser: async (id) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/v1/users/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Update user details
  updateUser: async (id, userData) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.put(`${API_URL}/api/v1/users/${id}`, userData, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Delete user
  deleteUser: async (id) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.delete(`${API_URL}/api/v1/users/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get current user profile
  getCurrentUser: async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/v1/user`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Update current user profile
  updateProfile: async (profileData) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.put(`${API_URL}/api/v1/user`, profileData, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  async createUser(userData) {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(`${API_URL}/api/v1/users`, userData, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get user's trusted devices
  getUserDevices: async () => {
    try {
      const response = await api.get('/user/devices');
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Trust a device
  trustDevice: async (deviceId) => {
    try {
      const response = await api.post(`/user/devices/${deviceId}/trust`);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Untrust a device
  untrustDevice: async (deviceId) => {
    try {
      const response = await api.post(`/user/devices/${deviceId}/untrust`);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Delete a device
  deleteDevice: async (deviceId) => {
    try {
      const response = await api.delete(`/user/devices/${deviceId}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get notification preferences
  getNotificationPreferences: async () => {
    try {
      const response = await api.get('/notification-preferences');
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Update notification preferences
  updateNotificationPreferences: async (preferences) => {
    try {
      const response = await api.put('/notification-preferences', {
        login_alerts: Boolean(preferences.login_alerts),
        password_changes: Boolean(preferences.password_changes),
        email_changes: Boolean(preferences.email_changes),
        new_devices: Boolean(preferences.new_devices)
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  }
};

export default userService; 