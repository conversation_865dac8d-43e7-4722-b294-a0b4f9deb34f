import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL;

const reviewService = {
  // Get reviews for a manuscript
  getManuscriptReviews: async (manuscriptId) => {
    try {
      const token = localStorage.getItem('token');
      console.log('Fetching reviews for manuscript:', manuscriptId);
      const response = await axios.get(`${API_URL}/api/v1/manuscripts/${manuscriptId}/reviews`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('Reviews response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching reviews:', error);
      throw error.response?.data || error.message;
    }
  },

  // Get a specific review
  getReview: async (manuscriptId, reviewId) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/v1/manuscripts/${manuscriptId}/reviews/${reviewId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Submit a review
  submitReview: async (manuscriptId, reviewData) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(`${API_URL}/api/v1/manuscripts/${manuscriptId}/reviews`, reviewData, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Update a review
  updateReview: async (manuscriptId, reviewId, reviewData) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.put(`${API_URL}/api/v1/manuscripts/${manuscriptId}/reviews/${reviewId}`, reviewData, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Mark review as completed
  markAsCompleted: async (manuscriptId, reviewId) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(`${API_URL}/api/v1/manuscripts/${manuscriptId}/reviews/${reviewId}/complete`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Mark review as in progress
  markAsInProgress: async (manuscriptId, reviewId) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.delete(`${API_URL}/api/v1/manuscripts/${manuscriptId}/reviews/${reviewId}/complete`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get assigned reviews for the current reviewer
  getAssignedReviews: async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/v1/manuscripts/assigned-reviews`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        params: {
          status: 'under_review'
        }
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  }
};

export default reviewService; 