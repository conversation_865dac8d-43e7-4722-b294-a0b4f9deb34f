/**
 * <AUTHOR> (andrew21-mch)
 * @github https://github.com/andrew21-mch
 * @description Main Application Component - Handles routing and layout structure for the entire application
 */

import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { ToastContainer } from 'react-toastify';
import { Toaster } from 'react-hot-toast';
import 'react-toastify/dist/ReactToastify.css';
import PublicLayout from "./layouts/PublicLayout";
import AdminLayout from "./layouts/AdminLayout";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import { AuthProvider } from './contexts/AuthContext';
import { I18nextProvider } from 'react-i18next';
import i18n from './i18n';

// Public Pages
import HomePage from "./pages/public/HomePage";
import AboutPage from "./pages/public/AboutPage";
import ArticlePage from "./pages/public/ArticlePage";
import GuidelinesPage from "./pages/public/GuidelinesPage";
import EditorialBoardPage from "./pages/public/EditorialBoardPage";
import ContactPage from "./pages/public/ContactPage";
import CurrentIssuePage from "./pages/public/CurrentIssuePage";
import ArchivePage from "./pages/public/ArchivePage";
import FAQPage from "./pages/public/FAQPage";
import ArticleViewPage from './pages/public/ArticleViewPage';

// Auth Pages
import LoginPage from "./pages/auth/Auth/LoginPage";
import RegisterPage from "./pages/auth/Auth/RegisterPage";
import ResetPasswordPage from "./pages/auth/Auth/ResetPasswordPage";
import NewPasswordPage from "./pages/auth/Auth/NewPasswordPage";
import VerifyEmailPage from "./pages/auth/VerifyEmail/VerifyEmailPage";
import OAuthCallback from "./pages/auth/Auth/OAuthCallback";

// Author Pages
import AuthorDashboard from "./pages/author/AuthorDashboard";
import AuthorSubmissions from "./pages/author/AuthorSubmissions";
import PaymentPage from "./pages/author/PaymentPage";
import SecuritySettings from "./pages/SecuritySettings";

// Reviewer Pages
import ReviewerDashboard from "./pages/reviewer/Dashboard";
import AssignedReviews from "./pages/reviewer/AssignedReviews";
import CompletedReviews from "./pages/reviewer/CompletedReviews";
import SubmitReview from "./pages/reviewer/SubmitReview";
import ReviewerProfile from "./pages/reviewer/ReviewProfile";
import ReviewerGuidelines from "./pages/reviewer/ReviewerGuidelines";

// Editor Pages
import EditorDashboard from "./pages/editor/Dashboard";
import EditorSubmissions from "./pages/editor/EditorSubmissions";
import ReviewManagement from "./pages/editor/ReviewManagement";
import AnnouncementManagement from "./pages/editor/AnnouncementManagement";
import IssuePlanning from "./pages/editor/IssuePlanning";
import PaymentTracking from "./pages/editor/PaymentTracking";
import Volumes from "./pages/editor/Volumes";
import AssignReviewers from './pages/editor/AssignReviewers';
import ViewSubmissionPage from './pages/submission/ViewSubmission/ViewSubmissionPage';
import DirectPublish from './pages/editor/DirectPublish';
import ManageSubmission from './pages/editor/ManageSubmission';

// Admin Pages
import AdminDashboard from "./pages/admin/Dashboard";
import UserManagement from "./pages/admin/UserManagement";
import JournalSettings from "./pages/admin/JournalSettings";
import ContentManagement from "./pages/admin/ContentManagement";
import SystemMonitoring from "./pages/admin/SystemMonitoring";
import PaymentManagement from "./pages/admin/PaymentManagement";
import ManuscriptManagement from "./pages/admin/ManuscriptManagement";
import AdminReviewManagement from "./pages/admin/ReviewManagement";

// Error Page
import AnnouncementsPage from "./pages/public/AnnouncementPage";
import SubmissionForm from "./pages/author/NewSubmission/SubmissionForm";
import AuthorProfile from "./pages/author/Profile";
import NotFoundPage from "./pages/public/NotFoundPage";
import ReviewStatus from "./pages/author/ReviewStatus";

function App() {
  return (
    <I18nextProvider i18n={i18n}>
      <Router>
        <AuthProvider>
          <ToastContainer
            position="top-right"
            autoClose={5000}
            hideProgressBar={false}
            newestOnTop
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="light"
          />
          <Toaster position="top-right" />
          <Routes>
            {/* Public Routes */}
            <Route element={<PublicLayout />}>
              <Route path="/" element={<HomePage />} />
              <Route path="/about" element={<AboutPage />} />
              <Route path="/browse" element={<ArticlePage />} />
              <Route path="/article/:id" element={<ArticleViewPage />} />
              <Route path="/guidelines" element={<GuidelinesPage />} />
              <Route path="/editorial-board" element={<EditorialBoardPage />} />
              <Route path="/contact" element={<ContactPage />} />
              <Route path="/current-issue" element={<CurrentIssuePage />} />
              <Route path="/archive" element={<ArchivePage />} />
              <Route path="/announcements" element={<AnnouncementsPage />} />
              <Route path="/faq" element={<FAQPage />} />
              
              {/* Auth Routes */}
              <Route path="/login" element={<LoginPage />} />
              <Route path="/register" element={<RegisterPage />} />
              <Route path="/forgot-password" element={<ResetPasswordPage />} />
              <Route path="/password-reset/:token" element={<NewPasswordPage />} />
              <Route path="/auth/callback" element={<OAuthCallback />} />
            </Route>

            {/* Verify Email Route - Outside of PublicLayout */}
            <Route path="/verify-email" element={<VerifyEmailPage />} />

            {/* Author Routes */}
            <Route path="/author" element={
              <ProtectedRoute>
                <AdminLayout role="author" />
              </ProtectedRoute>
            }>
              <Route index element={<AuthorDashboard />} />
              <Route path="submissions" element={<AuthorSubmissions />} />
              <Route path="submissions/:id" element={<ViewSubmissionPage />} />
              <Route path="submissions/:id/payment" element={<PaymentPage />} />
              <Route path="new-submission" element={<SubmissionForm />} />
              <Route path="profile" element={<AuthorProfile />} />
              <Route path="security" element={<SecuritySettings />} />
              <Route path="submit" element={<SubmissionForm />} />
              <Route path="reviews" element={<ReviewStatus />} />
            </Route>

            {/* Reviewer Routes */}
            <Route path="/reviewer" element={
              <ProtectedRoute>
                <AdminLayout role="reviewer" />
              </ProtectedRoute>
            }>
              <Route index element={<ReviewerDashboard />} />
              <Route path="assigned" element={<AssignedReviews />} />
              <Route path="completed" element={<CompletedReviews />} />
              <Route path="submissions/:id" element={<SubmitReview />} />
              <Route path="security" element={<SecuritySettings />} />
              <Route path="profile" element={<ReviewerProfile />} />
              <Route path="guidelines" element={<ReviewerGuidelines />} />
            </Route>

            {/* Editor Routes */}
            <Route path="/editor" element={
              <ProtectedRoute>
                <AdminLayout role="editor" />
              </ProtectedRoute>
            }>
              <Route index element={<EditorDashboard />} />
              <Route path="submissions" element={<EditorSubmissions />} />
              <Route path="submissions/:id" element={<ViewSubmissionPage />} />
              <Route path="submissions/:id/manage" element={<ManageSubmission />} />
              <Route path="submissions/:id/assign-reviewers" element={<AssignReviewers />} />
              <Route path="reviews" element={<ReviewManagement />} />
              <Route path="announcements" element={<AnnouncementManagement />} />
              <Route path="volumes" element={<Volumes />} />
              <Route path="issues" element={<IssuePlanning />} />
              <Route path="payments" element={<PaymentTracking />} />
              <Route path="direct-publish" element={<DirectPublish />} />
              <Route path="security" element={<SecuritySettings />} />
            </Route>

            {/* Admin Routes */}
            <Route path="/admin" element={
              <ProtectedRoute>
                <AdminLayout role="admin" />
              </ProtectedRoute>
            }>
              <Route index element={<AdminDashboard />} />
              <Route path="users" element={<UserManagement />} />
              <Route path="manuscripts" element={<ManuscriptManagement />} />
              <Route path="payments" element={<PaymentManagement />} />
              <Route path="reviews" element={<AdminReviewManagement />} />
              <Route path="settings" element={<JournalSettings />} />
              <Route path="security" element={<SecuritySettings />} />
              <Route path="content" element={<ContentManagement />} />
              <Route path="monitoring" element={<SystemMonitoring />} />
            </Route>

            {/* 404 Page */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </AuthProvider>
      </Router>
    </I18nextProvider>
  );
}

export default App;