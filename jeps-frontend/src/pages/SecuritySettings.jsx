import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Box, Typography, Paper, Switch, FormControlLabel, 
  List, ListItem, ListItemText, ListItemSecondaryAction,
  IconButton, Dialog, DialogTitle, DialogContent, 
  DialogActions, Button, Divider, Alert, Card,
  CardContent, Grid, useTheme
} from '@mui/material';
import { 
  Delete as DeleteIcon, 
  Security as SecurityIcon,
  Computer as ComputerIcon,
  Phone as PhoneIcon,
  Tablet as TabletIcon,
  Notifications as NotificationsIcon,
  Devices as DevicesIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { formatDistanceToNow, isValid } from 'date-fns';
import userService from '../services/userService';

const SecuritySettings = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [devices, setDevices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [notificationPreferences, setNotificationPreferences] = useState({
    login_alerts: true,
    password_changes: true,
    email_changes: true,
    new_devices: true
  });

  useEffect(() => {
    fetchDevices();
    fetchNotificationPreferences();
  }, []);

  const fetchDevices = async () => {
    try {
      const response = await userService.getUserDevices();
      setDevices(response || []);
      setError(null);
    } catch (err) {
      setError(t('errors.fetchDevices'));
      console.error('Error fetching devices:', err);
      setDevices([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchNotificationPreferences = async () => {
    try {
      const response = await userService.getNotificationPreferences();
      console.log(response);
      setNotificationPreferences(response || {
        login_alerts: true,
        password_changes: true,
        email_changes: true,
        new_devices: true
      });
    } catch (err) {
      console.error('Error fetching notification preferences:', err);
    }
  };

  const handleTrustDevice = async (deviceId) => {
    try {
      await userService.trustDevice(deviceId);
      setDevices(devices.map(device => 
        device.id === deviceId ? { ...device, is_trusted: true } : device
      ));
      toast.success(t('success.deviceTrusted'));
    } catch (error) {
      console.error('Error trusting device:', error);
      toast.error(error.message || t('errors.trustDevice'));
    }
  };

  const handleUntrustDevice = async (deviceId) => {
    try {
      await userService.untrustDevice(deviceId);
      setDevices(devices.map(device => 
        device.id === deviceId ? { ...device, is_trusted: false } : device
      ));
      toast.success(t('success.deviceUntrusted'));
    } catch (error) {
      console.error('Error untrusting device:', error);
      toast.error(error.message || t('errors.untrustDevice'));
    }
  };

  const handleDeleteDevice = async () => {
    if (!selectedDevice) return;

    try {
      await userService.deleteDevice(selectedDevice.id);
      setDevices(devices.filter(device => device.id !== selectedDevice.id));
      setDeleteDialogOpen(false);
      setSelectedDevice(null);
      toast.success(t('success.deviceDeleted'));
    } catch (error) {
      console.error('Error deleting device:', error);
      toast.error(error.message || t('errors.deleteDevice'));
    }
  };

  const handleNotificationPreferenceChange = async (preference) => {
    try {
      const newPreferences = {
        ...notificationPreferences,
        [preference]: !notificationPreferences[preference]
      };
      await userService.updateNotificationPreferences(newPreferences);
      setNotificationPreferences(newPreferences);
      toast.success(t('success.preferencesUpdated'));
    } catch (error) {
      console.error('Error updating preferences:', error);
      toast.error(error.message || t('errors.updatePreferences'));
    }
  };

  const getDeviceIcon = (deviceType) => {
    switch (deviceType?.toLowerCase()) {
      case 'mobile':
        return <PhoneIcon />;
      case 'tablet':
        return <TabletIcon />;
      default:
        return <ComputerIcon />;
    }
  };

  const formatLastActive = (dateString) => {
    console.log(dateString);
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    return isValid(date) ? formatDistanceToNow(date, { addSuffix: true }) : 'Invalid date';
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Typography>Loading...</Typography>
      </div>
    );
  }

  return (
    <div className="w-full px-4 py-6">
      <Typography variant="h4" className="mb-8 font-semibold text-primary">
        {t('security.title')}
      </Typography>

      {error && (
        <Alert severity="error" className="mb-6">
          {error}
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Notification Preferences */}
        <div className="bg-white rounded-lg shadow-md">
          <div className="p-6">
            <div className="flex items-center w-full mb-4">
              <NotificationsIcon className="text-primary mr-2" />
              <Typography variant="h6" className="font-semibold">
                {t('security.notificationPreferences.title')}
              </Typography>
            </div>
            <Typography variant="body2" className="text-gray-600 mb-4">
              {t('security.notificationPreferences.description')}
            </Typography>
            <div className="space-y-4">
              <div className="flex items-center justify-between py-3 border-b border-gray-200 last:border-0">
                <div>
                  <Typography className="font-medium">
                    {t('security.notificationPreferences.loginAlerts')}
                  </Typography>
                  <Typography variant="body2" className="text-gray-600">
                    {t('security.notificationPreferences.loginAlertsDescription')}
                  </Typography>
                </div>
                <Switch
                  edge="end"
                  checked={notificationPreferences.login_alerts}
                  onChange={() => handleNotificationPreferenceChange('login_alerts')}
                  color="primary"
                />
              </div>
              <div className="flex items-center justify-between py-3 border-b border-gray-200 last:border-0">
                <div>
                  <Typography className="font-medium">
                    {t('security.notificationPreferences.passwordChanges')}
                  </Typography>
                  <Typography variant="body2" className="text-gray-600">
                    {t('security.notificationPreferences.passwordChangesDescription')}
                  </Typography>
                </div>
                <Switch
                  edge="end"
                  checked={notificationPreferences.password_changes}
                  onChange={() => handleNotificationPreferenceChange('password_changes')}
                  color="primary"
                />
              </div>
              <div className="flex items-center justify-between py-3 border-b border-gray-200 last:border-0">
                <div>
                  <Typography className="font-medium">
                    {t('security.notificationPreferences.emailChanges')}
                  </Typography>
                  <Typography variant="body2" className="text-gray-600">
                    {t('security.notificationPreferences.emailChangesDescription')}
                  </Typography>
                </div>
                <Switch
                  edge="end"
                  checked={notificationPreferences.email_changes}
                  onChange={() => handleNotificationPreferenceChange('email_changes')}
                  color="primary"
                />
              </div>
              <div className="flex items-center justify-between py-3">
                <div>
                  <Typography className="font-medium">
                    {t('security.notificationPreferences.newDevices')}
                  </Typography>
                  <Typography variant="body2" className="text-gray-600">
                    {t('security.notificationPreferences.newDevicesDescription')}
                  </Typography>
                </div>
                <Switch
                  edge="end"
                  checked={notificationPreferences.new_devices}
                  onChange={() => handleNotificationPreferenceChange('new_devices')}
                  color="primary"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Trusted Devices */}
        <div className="bg-white rounded-lg shadow-md">
          <div className="p-6">
            <div className="flex items-center w-full mb-4">
              <DevicesIcon className="text-primary mr-2" />
              <Typography variant="h6" className="font-semibold">
                {t('security.trustedDevices.title')}
              </Typography>
            </div>
            <Typography variant="body2" className="text-gray-600 mb-4">
              {t('security.trustedDevices.description')}
            </Typography>
            <div className="space-y-3">
              {(devices || []).map((device) => (
                <div 
                  key={device.id}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center mb-1">
                      {getDeviceIcon(device.device_type)}
                      <Typography className="ml-2 font-medium">
                        {device.device_name}
                      </Typography>
                    </div>
                    <div className="space-y-1">
                      <Typography variant="body2" className="text-gray-600">
                        {device.browser} on {device.os}
                      </Typography>
                      <Typography variant="body2" className="text-gray-600">
                        {t('security.trustedDevices.lastActive')}: {formatLastActive(device.last_used_at)}
                      </Typography>
                      {device.ip_address && (
                        <Typography variant="body2" className="text-gray-600">
                          IP: {device.ip_address}
                        </Typography>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {device.is_trusted ? (
                      <IconButton 
                        onClick={() => handleUntrustDevice(device.id)}
                        title={t('security.trustedDevices.actions.untrust')}
                        className="text-primary"
                      >
                        <SecurityIcon />
                      </IconButton>
                    ) : (
                      <IconButton 
                        onClick={() => handleTrustDevice(device.id)}
                        title={t('security.trustedDevices.actions.trust')}
                      >
                        <SecurityIcon />
                      </IconButton>
                    )}
                    <IconButton 
                      onClick={() => {
                        setSelectedDevice(device);
                        setDeleteDialogOpen(true);
                      }}
                      title={t('security.trustedDevices.actions.delete')}
                      className="text-red-500"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Delete Device Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        PaperProps={{
          className: "rounded-lg min-w-[400px]"
        }}
      >
        <DialogTitle className="pb-1">
          {t('security.trustedDevices.deleteDialog.title')}
        </DialogTitle>
        <DialogContent>
          <Typography>
            {t('security.trustedDevices.deleteDialog.message')}
          </Typography>
        </DialogContent>
        <DialogActions className="px-6 pb-4">
          <Button 
            onClick={() => setDeleteDialogOpen(false)}
            variant="outlined"
          >
            {t('security.trustedDevices.deleteDialog.cancel')}
          </Button>
          <Button 
            onClick={handleDeleteDevice} 
            color="error"
            variant="contained"
          >
            {t('security.trustedDevices.deleteDialog.confirm')}
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default SecuritySettings; 