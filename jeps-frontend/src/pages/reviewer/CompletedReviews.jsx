import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  FaFileAlt, FaUser, FaCalendarAlt, FaTag, FaSpinner, 
  FaDownload, FaCheckCircle, FaTimesCircle, FaSearch,
  FaUniversity, FaGlobe, FaBook, FaClock, FaFileUpload,
  FaExclamationTriangle, FaUserFriends, FaComments,
  FaArrowRight, FaEye
} from 'react-icons/fa';
import reviewService from '../../services/reviewService';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { toast } from 'react-toastify';

const CompletedReviews = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [submissions, setSubmissions] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    fetchReviews();
  }, []);

  const fetchReviews = async () => {
    try {
      const data = await reviewService.getAssignedReviews();
      // Filter only completed reviews
      const completedReviews = data.data.filter(submission => 
        submission.reviews?.some(review => review.submitted_at)
      );
      setSubmissions(completedReviews);
    } catch (err) {
      console.error('Error fetching reviews:', err);
      setError(err.message || 'Failed to load completed reviews');
      toast.error(err.message || 'Failed to load completed reviews');
    } finally {
      setLoading(false);
    }
  };

  const getReviewRecommendation = (submission) => {
    const review = submission.reviews?.find(review => review.submitted_at);
    if (!review) return null;
    
    const recommendations = {
      'accept': 'Accept',
      'minor': 'Minor Revisions',
      'major': 'Major Revisions',
      'reject': 'Reject'
    };
    return recommendations[review.recommendation] || review.recommendation;
  };

  const filteredSubmissions = submissions.filter(submission => {
    return submission.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
           submission.submission_id.toLowerCase().includes(searchQuery.toLowerCase());
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <FaExclamationTriangle className="mx-auto h-12 w-12 text-red-500" />
          <h2 className="mt-4 text-xl font-semibold text-gray-900">Error Loading Reviews</h2>
          <p className="mt-2 text-sm text-gray-600">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-9xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Completed Reviews</h1>
              <p className="mt-1 text-sm text-gray-500">
                View your submitted manuscript reviews
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-9xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Search */}
          <div className="mb-6">
            <div className="relative rounded-md shadow-sm">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaSearch className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors"
                placeholder="Search by title or submission ID..."
              />
            </div>
          </div>

          {/* Submissions List */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            {filteredSubmissions.length === 0 ? (
              <div className="text-center py-12">
                <FaFileAlt className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No completed reviews</h3>
                <p className="mt-1 text-sm text-gray-500">
                  You haven't completed any manuscript reviews yet.
                </p>
              </div>
            ) : (
              <ul className="divide-y divide-gray-200 mt-2">
                {filteredSubmissions.map((submission) => {
                  const review = submission.reviews?.[0];
                  const recommendation = getReviewRecommendation(submission);
                  
                  return (
                    <li key={submission.id} className="hover:bg-gray-50 transition-colors">
                      <div className="px-6 py-5 mt-2">
                        <div className="flex items-center justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <div className="flex-shrink-0">
                                  <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                                    <FaCheckCircle className="h-5 w-5 text-green-600" />
                                  </div>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-indigo-600 truncate">
                                    {submission.title}
                                  </p>
                                  <div className="mt-1 flex items-center text-sm text-gray-500">
                                    <FaFileAlt className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" />
                                    <span className="truncate">{submission.submission_id}</span>
                                    <span className="mx-2">•</span>
                                    <FaCalendarAlt className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" />
                                    <span>Submitted {new Date(review.submitted_at).toLocaleDateString()}</span>
                                  </div>
                                  {recommendation && (
                                    <div className="mt-1 text-sm text-gray-600">
                                      <span className="font-medium">Your Recommendation:</span> {recommendation}
                                    </div>
                                  )}
                                </div>
                              </div>
                              <div className="ml-4 flex items-center space-x-4">
                                <button
                                  onClick={() => navigate(`/reviewer/submissions/${submission.id}`)}
                                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
                                >
                                  <FaEye className="mr-2 h-4 w-4" />
                                  View Review
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  );
                })}
              </ul>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompletedReviews;