import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { 
  FaArrowLeft, 
  FaSpinner, 
  FaInfoCircle, 
  FaCheckCircle, 
  FaClock,
  FaDownload,
  FaEye,
  FaFileAlt, 
  FaUser, 
  FaCalendarAlt, 
  FaTag, 
  FaExclamationTriangle,
  FaSave, 
  FaEyeSlash, 
  FaBook, 
  FaLanguage,
  FaUniversity, 
  FaGlobe, 
  FaFileUpload
} from 'react-icons/fa';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import reviewService from '../../services/reviewService';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { manuscriptService } from '../../services/manuscriptService';

const RichTextEditor = ({ content, onChange, placeholder }) => {
  console.log('RichTextEditor received content:', content);
  
  function uploadAdapter(loader) {
    return {
      upload: () => {
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          loader.file.then(file => {
            reader.onload = () => {
              resolve({
                default: reader.result
              });
            };
            reader.onerror = () => reject('Upload failed');
            reader.readAsDataURL(file);
          });
        });
      }
    };
  }

  function uploadPlugin(editor) {
    editor.plugins.get('FileRepository').createUploadAdapter = (loader) => {
      return uploadAdapter(loader);
    };
  }

  return (
    <div className="border border-gray-300 rounded-lg overflow-hidden bg-white shadow-sm">
      <CKEditor
        editor={ClassicEditor}
        data={content || ''}
        onChange={(event, editor) => {
          const data = editor.getData();
          console.log('CKEditor onChange:', data);
          onChange(data);
        }}
        onReady={(editor) => {
          console.log('CKEditor ready, current content:', editor.getData());
        }}
        config={{
          placeholder: placeholder,
          extraPlugins: [uploadPlugin],
          toolbar: [
            'heading',
            '|',
            'bold',
            'italic',
            'underline',
            'strikethrough',
            '|',
            'fontColor',
            'fontBackgroundColor',
            '|',
            'bulletedList',
            'numberedList',
            '|',
            'outdent',
            'indent',
            '|',
            'blockQuote',
            'insertTable',
            'mediaEmbed',
            'link',
            '|',
            'imageUpload',
            '|',
            'undo',
            'redo'
          ],
          image: {
            toolbar: [
              'imageStyle:inline',
              'imageStyle:block',
              'imageStyle:side',
              '|',
              'toggleImageCaption',
              'imageTextAlternative',
              '|',
              'linkImage'
            ],
            styles: [
              'alignLeft',
              'alignCenter',
              'alignRight'
            ],
            resizeOptions: [
              {
                name: 'resizeImage:original',
                value: null,
                label: 'Original'
              },
              {
                name: 'resizeImage:50',
                value: '50',
                label: '50%'
              },
              {
                name: 'resizeImage:75',
                value: '75',
                label: '75%'
              }
            ],
            resizeUnit: '%'
          }
        }}
      />
      <style jsx global>{`
        .ck-editor__editable {
          min-height: 400px !important;
          max-height: 600px !important;
        }
        .ck-editor__editable_inline {
          padding: 1.5rem !important;
        }
        .ck.ck-editor__main > .ck-editor__editable {
          background-color: #ffffff !important;
        }
        .ck.ck-toolbar {
          background-color: #f9fafb !important;
          border-bottom: 1px solid #e5e7eb !important;
        }
        .ck.ck-toolbar .ck-toolbar__items {
          flex-wrap: wrap !important;
        }
        .ck.ck-button {
          color: #374151 !important;
        }
        .ck.ck-button:hover {
          background-color: #e5e7eb !important;
        }
        .ck.ck-button.ck-on {
          background-color: #e5e7eb !important;
          color: #4f46e5 !important;
        }
        .ck.ck-image {
          max-width: 100% !important;
        }
        .ck.ck-image__resizer {
          border: 2px solid #4f46e5 !important;
        }
        .ck.ck-image__resizer__handle {
          background-color: #4f46e5 !important;
          border: 2px solid #ffffff !important;
        }
      `}</style>
    </div>
  );
};

const SubmitReview = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [completingReview, setCompletingReview] = useState(false);
  const [error, setError] = useState('');
  const [manuscript, setManuscript] = useState(null);
  const [existingReview, setExistingReview] = useState(null);
  const [formData, setFormData] = useState({
    recommendation: '',
    feedback: '',
    confidential_comments: ''
  });
  const [formErrors, setFormErrors] = useState({});

  useEffect(() => {
    fetchManuscript();
    fetchExistingReview();
  }, [id]);

  const fetchExistingReview = async () => {
    try {
      const data = await reviewService.getManuscriptReviews(id);
      console.log('Manuscript data reviews:', data.data[0]);
      const review = data.data[0];
      if (review) {
        console.log('Found existing review:', review);
        setExistingReview(review);
        setFormData({
          recommendation: review.recommendation,
          feedback: review.feedback,
          confidential_comments: review.confidential_comments || ''
        });
      }
    } catch (err) {
      console.error('Error fetching existing review:', err);
    }
  };

  const fetchManuscript = async () => {
    try {
      const data = await manuscriptService.getManuscriptById(id);
      setManuscript(data.data);
    } catch (err) {
      console.error('Error fetching manuscript:', err);
      setError(err.message || 'Failed to load manuscript details');
      toast.error(err.message || 'Failed to load manuscript details');
    } finally {
      setLoading(false);
    }
  };

  // Add a useEffect to log formData changes
  useEffect(() => {
    console.log('Form data updated:', formData);
  }, [formData]);

  // Add a useEffect to log when loading is complete
  useEffect(() => {
    if (!loading) {
      console.log('Loading complete, current form data:', formData);
    }
  }, [loading, formData]);

  const validateForm = () => {
    const errors = {};
    if (!formData.recommendation) {
      errors.recommendation = 'Please select a recommendation';
    }
    if (!formData.feedback) {
      errors.feedback = 'Please provide feedback for the authors';
    }
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fill in all required fields');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      await reviewService.submitReview(id, formData);
      toast.success('Review submitted successfully');
      // Refresh the review data
      await fetchExistingReview();
    } catch (err) {
      console.error('Error submitting review:', err);
      setError(err.message || 'Failed to submit review');
      toast.error(err.message || 'Failed to submit review');
    } finally {
      setSubmitting(false);
    }
  };

  const handleMarkAsCompleted = async () => {
    if (!existingReview) {
      toast.error('Please submit your review first');
      return;
    }

    setCompletingReview(true);
    try {
      await reviewService.markAsCompleted(id, existingReview.id);
      toast.success('Review marked as completed');
      await fetchExistingReview();
      navigate('/reviewer/assigned');
    } catch (err) {
      console.error('Error marking review as completed:', err);
      toast.error(err.message || 'Failed to mark review as completed');
    } finally {
      setCompletingReview(false);
    }
  };

  const handleMarkAsInProgress = async () => {
    if (!existingReview) {
      return;
    }

    setCompletingReview(true);
    try {
      await reviewService.markAsInProgress(id, existingReview.id);
      toast.success('Review marked as in progress');
      await fetchExistingReview();
    } catch (err) {
      console.error('Error marking review as in progress:', err);
      toast.error(err.message || 'Failed to mark review as in progress');
    } finally {
      setCompletingReview(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleEditorChange = (name) => (content) => {
    setFormData(prev => ({
      ...prev,
      [name]: content
    }));
    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-9xl mx-auto">
          <div className="bg-white shadow rounded-lg p-6">
            <div className="text-center">
              <FaExclamationTriangle className="mx-auto h-12 w-12 text-red-500" />
              <h3 className="mt-2 text-lg font-medium text-gray-900">Error</h3>
              <p className="mt-1 text-sm text-gray-500">{error}</p>
              <div className="mt-6">
                <button
                  onClick={() => navigate('/reviewer/assigned')}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  <FaArrowLeft className="mr-2" />
                  Back to Assigned Reviews
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-9xl mx-auto">
        <div className="bg-white shadow rounded-lg p-6">
          {/* Header Section */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Submit Review</h2>
              <p className="mt-1 text-sm text-gray-500">
                Provide your evaluation and feedback for the manuscript
              </p>
            </div>
            <button
              onClick={() => navigate('/reviewer/assigned')}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <FaArrowLeft className="mr-2" />
              Back to Reviews
            </button>
          </div>

          {/* Manuscript Details Section */}
          {manuscript && (
            <div className="mb-8 bg-gray-50 rounded-lg p-6 border border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 flex items-center">
                  <FaFileAlt className="mr-2 text-indigo-500" />
                  Manuscript Details
                </h3>
                <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  manuscript.status === 'submitted' ? 'bg-blue-100 text-blue-800' :
                  manuscript.status === 'under_review' ? 'bg-yellow-100 text-yellow-800' :
                  manuscript.status === 'accepted' ? 'bg-green-100 text-green-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {manuscript.status.replace('_', ' ').toUpperCase()}
                </span>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <p className="text-sm font-medium text-gray-500">Title</p>
                  <p className="mt-1 text-sm text-gray-900">{manuscript.title}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Submission ID</p>
                  <p className="mt-1 text-sm text-gray-900">{manuscript.submission_id}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Language</p>
                  <p className="mt-1 text-sm text-gray-900 flex items-center">
                    <FaLanguage className="mr-2 text-gray-400" />
                    {manuscript.language.toUpperCase()}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Submitted Date</p>
                  <p className="mt-1 text-sm text-gray-900 flex items-center">
                    <FaCalendarAlt className="mr-2 text-gray-400" />
                    {new Date(manuscript.created_at).toLocaleDateString()}
                  </p>
                </div>
                {manuscript.keywords && typeof manuscript.keywords === 'string' && manuscript.keywords.trim() && (
                  <div className="md:col-span-2">
                    <p className="text-sm font-medium text-gray-500">Keywords</p>
                    <div className="mt-1 flex flex-wrap gap-2">
                      {manuscript.keywords.split(',').map((keyword, index) => (
                        <span key={index} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {keyword.trim()}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Abstract Section */}
              {manuscript.abstract && (
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-500 mb-2">Abstract</h4>
                  <div className="bg-white p-4 rounded-md border border-gray-200">
                    <div 
                      className="text-sm text-gray-900 leading-relaxed"
                      dangerouslySetInnerHTML={{ __html: manuscript.abstract }}
                    />
                  </div>
                </div>
              )}

              {/* Manuscript File Download */}
              <div className="bg-white p-4 rounded-md border border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <FaFileAlt className="mr-3 text-indigo-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Manuscript File</p>
                      <p className="text-xs text-gray-500">Download the full manuscript for review</p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <a
                      href={`${import.meta.env.VITE_API_URL}/api/v1/manuscripts/${manuscript.id}/download`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-3 py-2 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      <FaEye className="mr-1" />
                      View
                    </a>
                    <a
                      href={`${import.meta.env.VITE_API_URL}/api/v1/manuscripts/${manuscript.id}/download?download=1`}
                      className="inline-flex items-center px-3 py-2 border border-transparent text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      <FaDownload className="mr-1" />
                      Download
                    </a>
                  </div>
                </div>
              </div>

              {/* Anonymous Review Notice */}
              <div className="mt-4 p-3 bg-yellow-50 rounded-md border border-yellow-200">
                <div className="flex items-start">
                  <FaEyeSlash className="mr-2 text-yellow-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-yellow-800">Anonymous Review</p>
                    <p className="text-xs text-yellow-700 mt-1">
                      Author information has been hidden to ensure unbiased review. Please base your evaluation solely on the manuscript content.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Review Status Section */}
          {existingReview && (
            <div className="mb-8 bg-blue-50 rounded-lg p-6 border border-blue-200">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 flex items-center">
                  <FaCheckCircle className="mr-2 text-blue-500" />
                  Review Status
                </h3>
                <div className="flex items-center space-x-3">
                  <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    existingReview.status === 'completed' ? 'bg-green-100 text-green-800' :
                    existingReview.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    {existingReview.status === 'completed' ? 'Completed' :
                     existingReview.status === 'in_progress' ? 'In Progress' :
                     'Pending Submission'}
                  </span>
                  
                  {existingReview.is_submitted && !existingReview.is_completed && (
                    <button
                      onClick={handleMarkAsCompleted}
                      disabled={completingReview}
                      className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                    >
                      {completingReview ? (
                        <>
                          <FaSpinner className="animate-spin mr-1" />
                          Completing...
                        </>
                      ) : (
                        <>
                          <FaCheckCircle className="mr-1" />
                          Mark as Completed
                        </>
                      )}
                    </button>
                  )}
                  
                  {existingReview.is_completed && (
                    <button
                      onClick={handleMarkAsInProgress}
                      disabled={completingReview}
                      className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 disabled:opacity-50"
                    >
                      {completingReview ? (
                        <>
                          <FaSpinner className="animate-spin mr-1" />
                          Updating...
                        </>
                      ) : (
                        <>
                          <FaClock className="mr-1" />
                          Mark as In Progress
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <p className="font-medium text-gray-500">Submitted</p>
                  <p className="text-gray-900 flex items-center">
                    {existingReview.is_submitted ? (
                      <>
                        <FaCheckCircle className="text-green-500 mr-1" />
                        {new Date(existingReview.submitted_at).toLocaleDateString()}
                      </>
                    ) : (
                      <>
                        <FaClock className="text-yellow-500 mr-1" />
                        Not submitted
                      </>
                    )}
                  </p>
                </div>
                <div>
                  <p className="font-medium text-gray-500">Completed</p>
                  <p className="text-gray-900 flex items-center">
                    {existingReview.is_completed ? (
                      <>
                        <FaCheckCircle className="text-green-500 mr-1" />
                        {new Date(existingReview.completed_at).toLocaleDateString()}
                      </>
                    ) : (
                      <>
                        <FaClock className="text-yellow-500 mr-1" />
                        Not completed
                      </>
                    )}
                  </p>
                </div>
                <div>
                  <p className="font-medium text-gray-500">Recommendation</p>
                  <p className="text-gray-900">
                    {existingReview.recommendation ? (
                      <span className={`capitalize ${
                        existingReview.recommendation === 'accept' ? 'text-green-600' :
                        existingReview.recommendation === 'reject' ? 'text-red-600' :
                        'text-yellow-600'
                      }`}>
                        {existingReview.recommendation}
                      </span>
                    ) : 'Not selected'}
                  </p>
                </div>
              </div>
              
              {existingReview.is_completed && (
                <div className="mt-4 p-3 bg-green-50 rounded-md">
                  <p className="text-sm text-green-700 flex items-center">
                    <FaCheckCircle className="mr-2" />
                    This review has been marked as completed. You can still edit it if needed.
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Review Form */}
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Recommendation Section */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <label htmlFor="recommendation" className="block text-lg font-medium text-gray-900 mb-4">
                Your Recommendation
              </label>
              <select
                id="recommendation"
                name="recommendation"
                value={formData.recommendation}
                onChange={handleInputChange}
                className={`mt-1 block w-full pl-3 pr-10 py-3 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md ${
                  formErrors.recommendation ? 'border-red-300' : ''
                }`}
              >
                <option value="">Select a recommendation</option>
                <option value="accept">Accept as is</option>
                <option value="minor">Accept with minor revisions</option>
                <option value="major">Needs major revisions</option>
                <option value="reject">Reject</option>
              </select>
              {formErrors.recommendation && (
                <p className="mt-2 text-sm text-red-600">{formErrors.recommendation}</p>
              )}
            </div>

            {/* Feedback Section */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <label htmlFor="feedback" className="block text-lg font-medium text-gray-900 mb-4">
                Feedback for Authors
              </label>
              <div className="mt-1">
                <RichTextEditor
                  content={formData.feedback}
                  onChange={handleEditorChange('feedback')}
                  placeholder="Provide detailed feedback for the authors..."
                />
                {formErrors.feedback && (
                  <p className="mt-2 text-sm text-red-600">{formErrors.feedback}</p>
                )}
              </div>
            </div>

            {/* Confidential Comments Section */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <label htmlFor="confidential_comments" className="block text-lg font-medium text-gray-900 mb-4">
                Confidential Comments
                <span className="ml-2 text-sm font-normal text-gray-500">(for editors only)</span>
              </label>
              <div className="mt-1">
                <RichTextEditor
                  content={formData.confidential_comments}
                  onChange={handleEditorChange('confidential_comments')}
                  placeholder="Add any confidential comments for the editors..."
                />
              </div>
            </div>

            {/* Submit Button Section */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={() => navigate('/reviewer/assigned')}
                className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={submitting}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {submitting ? (
                  <>
                    <FaSpinner className="animate-spin -ml-1 mr-2 h-5 w-5" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <FaSave className="-ml-1 mr-2 h-5 w-5" />
                    Submit Review
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default SubmitReview; 