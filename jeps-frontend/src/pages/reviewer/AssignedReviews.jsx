import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  FaFileAlt, FaUser, FaCalendarAlt, FaTag, FaSpinner, 
  FaDownload, FaCheckCircle, FaTimesCircle, FaSearch,
  FaUniversity, FaGlobe, FaBook, FaClock, FaFileUpload,
  FaExclamationTriangle, FaUserFriends, FaComments,
  FaArrowRight
} from 'react-icons/fa';
import reviewService from '../../services/reviewService';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { toast } from 'react-toastify';

const AssignedReviews = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [submissions, setSubmissions] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    fetchReviews();
  }, []);

  const fetchReviews = async () => {
    try {
      const data = await reviewService.getAssignedReviews();
      setSubmissions(data.data);
    } catch (err) {
      console.error('Error fetching reviews:', err);
      setError(err.message || 'Failed to load assigned reviews');
      toast.error(err.message || 'Failed to load assigned reviews');
    } finally {
      setLoading(false);
    }
  };

  const getReviewStatus = (submission) => {
    const review = submission.reviews?.[0];
    if (!review) return 'pending';
    return review.submitted_at ? 'completed' : 'in_progress';
  };

  const getReviewStatusBadgeClass = (status) => {
    const statusClasses = {
      'pending': 'bg-gray-100 text-gray-800',
      'in_progress': 'bg-blue-100 text-blue-800',
      'completed': 'bg-green-100 text-green-800'
    };
    return statusClasses[status] || 'bg-gray-100 text-gray-800';
  };

  const getReviewRecommendation = (submission) => {
    const review = submission.reviews?.[0];
    if (!review || !review.submitted_at) return null;
    
    const recommendations = {
      'accept': 'Accept',
      'minor': 'Minor Revisions',
      'major': 'Major Revisions',
      'reject': 'Reject'
    };
    return recommendations[review.recommendation] || review.recommendation;
  };

  const filteredSubmissions = submissions.filter(submission => {
    const matchesSearch = submission.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         submission.submission_id.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || getReviewStatus(submission) === statusFilter;
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <FaExclamationTriangle className="mx-auto h-12 w-12 text-red-500" />
          <h2 className="mt-4 text-xl font-semibold text-gray-900">Error Loading Reviews</h2>
          <p className="mt-2 text-sm text-gray-600">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-9xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Assigned Reviews</h1>
              <p className="mt-1 text-sm text-gray-500">
                Manage and track your assigned manuscript reviews
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-9xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Filters */}
          <div className="mb-6 flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaSearch className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors"
                  placeholder="Search by title or submission ID..."
                />
              </div>
            </div>
            <div className="w-full sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md transition-colors"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
              </select>
            </div>
          </div>

          {/* Submissions List */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            {filteredSubmissions.length === 0 ? (
              <div className="text-center py-12">
                <FaFileAlt className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No reviews assigned</h3>
                <p className="mt-1 text-sm text-gray-500">
                  You don't have any manuscripts assigned for review at this time.
                </p>
              </div>
            ) : (
              <ul className="divide-y divide-gray-200 mt-2">
                {filteredSubmissions.map((submission) => {
                  const reviewStatus = getReviewStatus(submission);
                  const review = submission.reviews?.[0];
                  const recommendation = getReviewRecommendation(submission);
                  
                  return (
                    <li key={submission.id} className="hover:bg-gray-50 transition-colors">
                      <div className="px-6 py-5 mt-2">
                        <div className="flex items-center justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <div className="flex-shrink-0">
                                  <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                                    <FaFileAlt className="h-5 w-5 text-indigo-600" />
                                  </div>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-indigo-600 truncate">
                                    {submission.title}
                                  </p>
                                  <div className="mt-1 flex items-center text-sm text-gray-500">
                                    <FaFileAlt className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" />
                                    <span className="truncate">{submission.submission_id}</span>
                                    <span className="mx-2">•</span>
                                    <FaCalendarAlt className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" />
                                    <span>Assigned {submission.reviewers?.[0]?.pivot?.assigned_at ? new Date(submission.reviewers[0].pivot.assigned_at).toLocaleDateString() : 'N/A'}</span>
                                    {review?.submitted_at && (
                                      <>
                                        <span className="mx-2">•</span>
                                        <FaCheckCircle className="flex-shrink-0 mr-1.5 h-4 w-4 text-green-400" />
                                        <span>Submitted {new Date(review.submitted_at).toLocaleDateString()}</span>
                                      </>
                                    )}
                                  </div>
                                  {recommendation && (
                                    <div className="mt-1 text-sm text-gray-600">
                                      <span className="font-medium">Your Recommendation:</span> {recommendation}
                                    </div>
                                  )}
                                </div>
                              </div>
                              <div className="ml-4 flex items-center space-x-4">
                                <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getReviewStatusBadgeClass(reviewStatus)}`}>
                                  {reviewStatus.replace('_', ' ').toUpperCase()}
                                </span>
                                <button
                                  onClick={() => navigate(`/reviewer/submissions/${submission.id}`)}
                                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
                                >
                                  {reviewStatus === 'completed' ? 'View Review' : 'Start Review'}
                                  <FaArrowRight className="ml-2 h-4 w-4" />
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  );
                })}
              </ul>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssignedReviews;