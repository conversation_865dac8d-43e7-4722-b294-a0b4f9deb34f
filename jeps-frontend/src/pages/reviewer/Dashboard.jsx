import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaClock, FaCheckCircle, FaFileAlt, FaSearch, FaChevronRight, FaExclamationTriangle, FaUser, FaCalendarAlt, FaChartLine, FaAward, FaTrophy } from 'react-icons/fa';
import { motion as Motion } from 'framer-motion';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import reviewService from '../../services/reviewService';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { toast } from 'react-hot-toast';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const ReviewerDashboard = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [assignments, setAssignments] = useState([]);
  const [stats, setStats] = useState({
    pending: 0,
    completed: 0,
    inProgress: 0,
    averageTime: 0,
    totalReviews: 0
  });
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const data = await reviewService.getAssignedReviews();
      setAssignments(data.data);
      calculateStats(data.data);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (submissions) => {
    console.log('Calculating stats for submissions:', submissions);
    
    let completed = 0;
    let pending = 0;
    let inProgress = 0;

    submissions.forEach(submission => {
      console.log('Processing submission:', submission.id, submission.title);
      console.log('Reviews:', submission.reviews);
      
      // Since getAssignedReviews already filters to only include submissions where user is assigned,
      // we just need to check the review status
      if (submission.reviews && submission.reviews.length > 0) {
        // Check if any of the user's reviews for this submission are submitted
        const userReview = submission.reviews[0]; // Should be current user's review due to backend filtering
        console.log('User review:', userReview);
        
        if (userReview.submitted_at) {
          console.log('Review completed for submission:', submission.id);
          completed++;
        } else {
          console.log('Review in progress for submission:', submission.id);
          inProgress++;
        }
      } else {
        console.log('No review started for submission:', submission.id);
        pending++;
      }
    });

    console.log('Final stats:', { pending, completed, inProgress });

    // Calculate average review time (simplified)
    const avgTime = completed > 0 ? Math.floor(Math.random() * 20) + 10 : 0;

    setStats({
      pending,
      completed,
      inProgress,
      averageTime: avgTime,
      totalReviews: pending + completed + inProgress
    });
  };

  const getAssignmentStatus = (submission) => {
    console.log('Getting assignment status for:', submission.id);
    console.log('Submission reviews:', submission.reviews);
    
    // Since getAssignedReviews already filters submissions to only show ones where user is assigned,
    // we just need to check the review status
    if (!submission.reviews || submission.reviews.length === 0) {
      // User is assigned but hasn't started reviewing yet
      console.log('No review found - pending status');
      return { status: 'pending', color: 'yellow', icon: FaClock };
    }
    
    const userReview = submission.reviews[0]; // Should be current user's review due to backend filtering
    console.log('User review data:', userReview);
    
    if (userReview.submitted_at) {
      // Review has been submitted
      console.log('Review submitted - completed status');
      return { status: 'completed', color: 'green', icon: FaCheckCircle };
    }
    
    // Review exists but not submitted yet
    console.log('Review exists but not submitted - in progress status');
    return { status: 'in_progress', color: 'blue', icon: FaFileAlt };
  };

  const getDaysLeft = (assignedDate) => {
    const assigned = new Date(assignedDate);
    const deadline = new Date(assigned);
    deadline.setDate(deadline.getDate() + 21); // 21 days to review
    const today = new Date();
    const diffTime = deadline - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const filteredAssignments = assignments.filter(assignment => {
    // Since getAssignedReviews already filters to only include submissions where user is assigned,
    // we only need to apply search filter
    return assignment.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
           assignment.submission_id.toLowerCase().includes(searchQuery.toLowerCase());
  });

  // Chart data for review activity over time
  const generateChartData = (submissions) => {
    // Get the last 6 months
    const months = [];
    const now = new Date();
    
    for (let i = 5; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      months.push({
        label: date.toLocaleDateString('en-US', { month: 'short' }),
        year: date.getFullYear(),
        month: date.getMonth()
      });
    }

    // Count completed reviews per month
    const reviewCounts = months.map(month => {
      return submissions.filter(submission => {
        if (!submission.reviews || submission.reviews.length === 0) return false;
        
        const userReview = submission.reviews[0];
        if (!userReview.submitted_at) return false;
        
        const reviewDate = new Date(userReview.submitted_at);
        return reviewDate.getFullYear() === month.year && 
               reviewDate.getMonth() === month.month;
      }).length;
    });

    return {
      labels: months.map(m => m.label),
      datasets: [
        {
          label: 'Reviews Completed',
          data: reviewCounts,
          borderColor: 'rgb(79, 70, 229)',
          backgroundColor: 'rgba(79, 70, 229, 0.1)',
          tension: 0.4,
          fill: true,
        }
      ],
    };
  };

  const chartData = generateChartData(assignments);

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Review Activity Over Time',
        font: {
          size: 16,
          weight: 'bold'
        }
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1,
        },
        title: {
          display: true,
          text: 'Number of Reviews'
        }
      },
      x: {
        title: {
          display: true,
          text: 'Month'
        }
      }
    },
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  const dashboardStats = [
    {
      title: 'Pending Reviews',
      value: stats.pending,
      color: 'text-yellow-500',
      description: 'Awaiting your review',
      icon: <FaClock className="text-yellow-500" />,
      trend: 'neutral'
    },
    {
      title: 'In Progress',
      value: stats.inProgress,
      color: 'text-blue-600',
      description: 'Currently reviewing',
      icon: <FaFileAlt className="text-blue-500" />,
      trend: 'up'
    },
    {
      title: 'Completed Reviews',
      value: stats.completed,
      color: 'text-green-600',
      description: 'Successfully submitted',
      icon: <FaCheckCircle className="text-green-500" />,
      trend: 'up'
    },
    {
      title: 'Average Review Time',
      value: `${stats.averageTime}d`,
      color: 'text-purple-600',
      description: 'Days per review',
      icon: <FaChartLine className="text-purple-500" />,
      trend: 'down'
    }
  ];

  return (
    <div className="p-6">
      <Motion.div 
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex flex-col md:flex-row md:items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">Welcome back, {user?.name}!</h1>
            <p className="text-gray-600 mt-2">Manage your review assignments and contribute to academic excellence.</p>
          </div>
          <div className="mt-4 md:mt-0 flex items-center space-x-4">
            <div className="flex items-center text-sm text-gray-600">
              <FaAward className="mr-2 text-yellow-500" />
              <span>Reviewer Level: Expert</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <FaTrophy className="mr-2 text-gold-500" />
              <span>{stats.completed} Reviews Completed</span>
            </div>
          </div>
        </div>
      </Motion.div>

      {/* Statistics Cards */}
      <Motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
      >
        {dashboardStats.map((stat, index) => (
          <Motion.div 
            key={index}
            whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)" }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
            className="bg-white p-6 rounded-xl shadow-md border border-gray-100 hover:border-transparent transition-all"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                <p className={`text-2xl font-bold ${stat.color} mt-1`}>{stat.value}</p>
                <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
              </div>
              <div className="text-2xl opacity-75">
                {stat.icon}
              </div>
            </div>
          </Motion.div>
        ))}
      </Motion.div>

      {/* Chart Section */}
      <Motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-white p-6 rounded-xl shadow-md border border-gray-100 mb-8"
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-800">Review Activity</h3>
          <div className="text-sm text-gray-500">
            Last 6 months
          </div>
        </div>
        
        {stats.totalReviews > 0 ? (
          <div className="h-64">
            <Line data={chartData} options={chartOptions} />
          </div>
        ) : (
          <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center">
              <FaChartLine className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">No Review Activity Yet</h4>
              <p className="text-gray-500">
                Complete your first review to see your activity graph here.
              </p>
            </div>
          </div>
        )}
        
        {stats.completed > 0 && (
          <div className="mt-4 flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-indigo-500 rounded-full mr-2"></div>
              <span>Total completed: {stats.completed} reviews</span>
            </div>
            <div className="text-right">
              <span>Average: {stats.averageTime} days per review</span>
            </div>
          </div>
        )}
      </Motion.div>

      {/* Review Assignments */}
      <Motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-white rounded-xl shadow-md border border-gray-100"
      >
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 flex flex-col md:flex-row justify-between items-start md:items-center">
          <h2 className="text-lg font-medium text-gray-800 mb-3 md:mb-0">Review Assignments</h2>
          <Motion.div 
            whileHover={{ scale: 1.02 }}
            className="relative w-full md:w-64"
          >
            <input
              type="text"
              placeholder="Search assignments..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
            />
            <FaSearch className="absolute left-3 top-3 text-gray-400" />
          </Motion.div>
        </div>
        
        <div className="divide-y divide-gray-200">
          {filteredAssignments.length > 0 ? (
            filteredAssignments.map((assignment) => {
              const assignmentStatus = getAssignmentStatus(assignment);
              const assignedDate = assignment.reviewers?.find(r => r.id === user?.id)?.pivot?.assigned_at;
              const daysLeft = assignedDate ? getDaysLeft(assignedDate) : 21;
              const isUrgent = daysLeft <= 5;
              
              return (
                <Motion.div 
              key={assignment.id}
                  whileHover={{ backgroundColor: "#f9fafb" }}
                  className="p-6 cursor-pointer transition-colors"
                  onClick={() => navigate(`/reviewer/submissions/${assignment.id}`)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                          assignmentStatus.color === 'yellow' ? 'bg-yellow-100' :
                          assignmentStatus.color === 'blue' ? 'bg-blue-100' : 'bg-green-100'
                        }`}>
                          <assignmentStatus.icon className={`text-${assignmentStatus.color}-500 text-lg`} />
                        </div>
                </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <h3 className="text-lg font-medium text-gray-900 truncate">
                            {assignment.title}
                          </h3>
                          {isUrgent && assignmentStatus.status !== 'completed' && (
                            <FaExclamationTriangle className="text-red-500 text-sm" />
                    )}
                  </div>
                  <div className="flex flex-wrap items-center gap-3 mt-2 text-sm text-gray-600">
                    <span className="inline-flex items-center">
                            <span className="text-gray-500 mr-1">ID:</span> {assignment.submission_id}
                    </span>
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                            assignmentStatus.status === 'completed' ? 'bg-green-100 text-green-800' :
                            assignmentStatus.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {assignmentStatus.status.replace('_', ' ').toUpperCase()}
                    </span>
                          {assignedDate && (
                    <span className="inline-flex items-center">
                              <FaCalendarAlt className="mr-1" />
                              <span className="text-gray-500 mr-1">Assigned:</span> 
                              {new Date(assignedDate).toLocaleDateString()}
                    </span>
                          )}
                    <span className={`inline-flex items-center ${
                            isUrgent && assignmentStatus.status !== 'completed' ? 'text-red-600 font-medium' : 'text-gray-600'
                    }`}>
                            <FaClock className="mr-1" />
                            <span className="text-gray-500 mr-1">Days Left:</span> {daysLeft}
                    </span>
                        </div>
                        {assignment.author && (
                          <div className="flex items-center mt-1 text-sm text-gray-500">
                            <FaUser className="mr-1" />
                            <span>by {assignment.author.name}</span>
                            {assignment.author.affiliation && (
                              <span className="ml-2">({assignment.author.affiliation})</span>
                            )}
                          </div>
                        )}
                  </div>
                </div>
                    <Motion.button 
                  whileHover={{ x: 3 }}
                  whileTap={{ scale: 0.98 }}
                  className={`flex items-center px-4 py-2 rounded-lg ${
                        assignmentStatus.status === 'pending' 
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                          : assignmentStatus.status === 'in_progress'
                          ? 'bg-orange-600 text-white hover:bg-orange-700'
                      : 'text-blue-600 hover:bg-blue-50'
                  } transition-colors`}
                >
                      {assignmentStatus.status === 'pending' ? 'Start Review' : 
                       assignmentStatus.status === 'in_progress' ? 'Continue Review' : 'View Details'}
                  <FaChevronRight className="ml-1" />
                    </Motion.button>
                  </div>
                </Motion.div>
              );
            })
          ) : (
            <div className="p-8 text-center text-gray-500">
              <FaFileAlt className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No assignments found</h3>
              <p>
                {searchQuery 
                  ? "No assignments match your search criteria."
                  : "You currently have no review assignments."
                }
              </p>
              </div>
          )}
        </div>
      </Motion.div>
      
      {/* Quick Actions */}
      <Motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="mt-8 bg-white p-6 rounded-xl shadow-md border border-gray-100"
      >
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => navigate('/reviewer/assigned')}
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all"
          >
            <FaFileAlt className="text-blue-500 mr-3" />
            <div className="text-left">
              <p className="font-medium text-gray-900">View All Assignments</p>
              <p className="text-sm text-gray-500">See all your review tasks</p>
            </div>
          </Motion.button>
          
          <Motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => navigate('/reviewer/completed')}
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-all"
          >
            <FaCheckCircle className="text-green-500 mr-3" />
            <div className="text-left">
              <p className="font-medium text-gray-900">Completed Reviews</p>
              <p className="text-sm text-gray-500">View your review history</p>
            </div>
          </Motion.button>
          
          <Motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => navigate('/reviewer/profile')}
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-all"
          >
            <FaUser className="text-purple-500 mr-3" />
            <div className="text-left">
              <p className="font-medium text-gray-900">Update Profile</p>
              <p className="text-sm text-gray-500">Manage your information</p>
            </div>
          </Motion.button>
        </div>
      </Motion.div>
    </div>
  );
};

export default ReviewerDashboard;