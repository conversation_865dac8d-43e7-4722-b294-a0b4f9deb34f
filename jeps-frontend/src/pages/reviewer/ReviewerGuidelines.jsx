import { motion } from 'framer-motion';
import { FaBook, FaCheckCircle, FaClock, FaExclamationTriangle, FaEye, FaComments, FaShieldAlt, FaGavel } from 'react-icons/fa';

const ReviewerGuidelines = () => {
  const guidelines = [
    {
      icon: <FaBook className="text-blue-500" />,
      title: "Review Thoroughly",
      description: "Read the manuscript carefully and provide constructive, detailed feedback that helps authors improve their work."
    },
    {
      icon: <FaClock className="text-yellow-500" />,
      title: "Meet Deadlines",
      description: "Complete your review within the specified timeframe. If you need an extension, please contact the editor promptly."
    },
    {
      icon: <FaShieldAlt className="text-green-500" />,
      title: "Maintain Confidentiality",
      description: "Keep all manuscript content confidential. Do not share or discuss the work with others without permission."
    },
    {
      icon: <FaComments className="text-purple-500" />,
      title: "Provide Constructive Feedback",
      description: "Offer specific, actionable suggestions for improvement. Be respectful and professional in your comments."
    },
    {
      icon: <FaEye className="text-indigo-500" />,
      title: "Check for Plagiarism",
      description: "Be alert to potential plagiarism or self-plagiarism. Report any concerns to the editor immediately."
    },
    {
      icon: <FaGavel className="text-red-500" />,
      title: "Be Objective and Fair",
      description: "Base your review on academic merit. Avoid bias based on author identity, institution, or personal opinions."
    }
  ];

  const reviewCriteria = [
    {
      category: "Originality & Significance",
      points: [
        "Does the work present new insights or findings?",
        "Is the research question important and relevant?",
        "Does it contribute meaningfully to the field?"
      ]
    },
    {
      category: "Methodology",
      points: [
        "Are the methods appropriate for the research question?",
        "Is the methodology clearly described and replicable?",
        "Are there any methodological limitations?"
      ]
    },
    {
      category: "Analysis & Results",
      points: [
        "Are the results clearly presented and well-organized?",
        "Is the analysis appropriate and thorough?",
        "Are conclusions supported by the data?"
      ]
    },
    {
      category: "Writing & Presentation",
      points: [
        "Is the manuscript well-written and clearly structured?",
        "Are figures and tables informative and well-designed?",
        "Is the language appropriate for the target audience?"
      ]
    }
  ];

  return (
    <div className="p-6">
      <motion.div 
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <h1 className="text-2xl font-bold text-gray-800">Reviewer Guidelines</h1>
        <p className="text-gray-600 mt-2">Best practices for conducting high-quality peer reviews</p>
      </motion.div>

      {/* Core Guidelines */}
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.1 }}
        className="mb-8"
      >
        <h2 className="text-xl font-semibold text-gray-800 mb-6">Core Principles</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {guidelines.map((guideline, index) => (
            <motion.div 
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * index }}
              className="bg-white p-6 rounded-xl shadow-md border border-gray-100 hover:shadow-lg transition-all"
            >
              <div className="flex items-start space-x-4">
                <div className="text-2xl">{guideline.icon}</div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">{guideline.title}</h3>
                  <p className="text-gray-600">{guideline.description}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Review Criteria */}
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
        className="mb-8"
      >
        <h2 className="text-xl font-semibold text-gray-800 mb-6">Review Criteria</h2>
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {reviewCriteria.map((criteria, index) => (
              <div key={index} className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-800">{criteria.category}</h3>
                <ul className="space-y-2">
                  {criteria.points.map((point, pointIndex) => (
                    <li key={pointIndex} className="flex items-start space-x-2">
                      <FaCheckCircle className="text-green-500 mt-1 flex-shrink-0" />
                      <span className="text-gray-600">{point}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Review Process */}
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
        className="mb-8"
      >
        <h2 className="text-xl font-semibold text-gray-800 mb-6">Review Process</h2>
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6">
          <div className="space-y-4">
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold">1</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-800">Initial Reading</h3>
                <p className="text-gray-600">Read through the entire manuscript to get an overall understanding</p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold">2</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-800">Detailed Review</h3>
                <p className="text-gray-600">Analyze each section carefully, taking notes on strengths and weaknesses</p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold">3</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-800">Recommendation</h3>
                <p className="text-gray-600">Provide your recommendation and detailed feedback for authors</p>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Ethics and Conflicts */}
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <h2 className="text-xl font-semibold text-gray-800 mb-6">Ethics & Conflicts of Interest</h2>
        <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
          <div className="flex items-start space-x-3">
            <FaExclamationTriangle className="text-yellow-600 mt-1" />
            <div>
              <h3 className="font-semibold text-yellow-800 mb-2">Important Reminders</h3>
              <ul className="space-y-2 text-yellow-700">
                <li>• Decline to review if you have a conflict of interest</li>
                <li>• Report any ethical concerns to the editor immediately</li>
                <li>• Do not use information from unpublished manuscripts in your own work</li>
                <li>• Maintain anonymity if this is a double-blind review</li>
              </ul>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default ReviewerGuidelines; 