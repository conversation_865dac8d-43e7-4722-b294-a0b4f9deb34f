import React, { useState, useEffect } from 'react';
import { 
  FaServer, 
  FaUsers, 
  FaFileAlt, 
  FaExclamationTriangle, 
  FaInfoCircle, 
  FaSyncAlt, 
  FaDownload,
  FaExclamationCircle,
  FaBug,
  FaUserCheck,
  FaClock,
  FaDatabase,
  FaMemory,
  FaHdd,
  FaChartLine,
  FaChartPie,
  FaChartBar
} from 'react-icons/fa';
import { toast } from 'react-toastify';
import adminService from '../../services/adminService';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
} from 'chart.js';
import { Line, Bar, Pie, Doughnut } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const SystemMonitoring = () => {
  const [users, setUsers] = useState([]);
  const [logs, setLogs] = useState([]);
  const [systemStats, setSystemStats] = useState({});
  const [systemResources, setSystemResources] = useState({});
  const [activityAnalytics, setActivityAnalytics] = useState({});
  const [systemMetrics, setSystemMetrics] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [timePeriod, setTimePeriod] = useState('24h');

  const tabs = [
    { id: 'overview', label: 'System Overview', icon: <FaServer /> },
    { id: 'graphs', label: 'Analytics Graphs', icon: <FaChartLine /> },
    { id: 'users', label: 'All Users', icon: <FaUsers /> },
    { id: 'logs', label: 'Activity Logs', icon: <FaFileAlt /> },
    { id: 'performance', label: 'Performance', icon: <FaMemory /> }
  ];

  const timePeriods = [
    { value: '1h', label: 'Last Hour' },
    { value: '24h', label: 'Last 24 Hours' },
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' }
  ];

  useEffect(() => {
    fetchMonitoringData();
  }, []);

  useEffect(() => {
    if (activeTab === 'graphs') {
      fetchGraphData();
    }
  }, [activeTab, timePeriod]);

  // Auto-refresh system resources every 30 seconds when on performance tab
  useEffect(() => {
    let interval = null;
    if (activeTab === 'performance' || activeTab === 'graphs') {
      interval = setInterval(() => {
        fetchSystemResources();
      }, 30000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [activeTab]);

  const fetchMonitoringData = async () => {
    try {
      setLoading(true);
      
      // Use activity logs instead of system logs
      const [usersResponse, activityLogsResponse, analyticsResponse] = await Promise.all([
        fetch(`${adminService.baseURL}/users`, {
          headers: adminService.getAuthHeaders()
        }),
        adminService.getActivityLogs({ per_page: 100 }),
        fetch(`${adminService.baseURL}/admin/analytics?period=24h`, {
          headers: adminService.getAuthHeaders()
        })
      ]);
      
      // Handle responses properly
      const usersData = usersResponse.ok ? await usersResponse.json() : { data: [] };
      const activityLogsData = activityLogsResponse || { data: [] };
      const analyticsData = analyticsResponse.ok ? await analyticsResponse.json() : { data: {} };
      
      setUsers(usersData.data || usersData || []);
      setLogs(activityLogsData.data || activityLogsData || []);
      setSystemStats(analyticsData.data || analyticsData || {});
    } catch (error) {
      console.error('Error fetching monitoring data:', error);
      toast.error('Failed to fetch system monitoring data');
      setUsers([]);
      setLogs([]);
      setSystemStats({});
    } finally {
      setLoading(false);
    }
  };

  const fetchGraphData = async () => {
    try {
      const [metricsResponse, resourcesResponse, activityResponse] = await Promise.all([
        adminService.getSystemMetrics({ period: timePeriod }),
        adminService.getSystemResources(),
        adminService.getActivityAnalytics({ period: timePeriod })
      ]);
      
      setSystemMetrics(metricsResponse.data || {});
      setSystemResources(resourcesResponse.data || {});
      setActivityAnalytics(activityResponse.data || {});
    } catch (error) {
      console.error('Error fetching graph data:', error);
      toast.error('Failed to fetch graph data');
    }
  };

  const fetchSystemResources = async () => {
    try {
      const response = await adminService.getSystemResources();
      setSystemResources(response.data || {});
    } catch (error) {
      console.error('Error fetching system resources:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchMonitoringData();
    if (activeTab === 'graphs') {
      await fetchGraphData();
    }
    setRefreshing(false);
    toast.success('System monitoring data refreshed');
  };

  const handleExportReport = async () => {
    try {
      const report = {
        timestamp: new Date().toISOString(),
        users: users.map(user => ({
          id: user.id,
          name: user.name,
          email: user.email || 'N/A',
          role: user.role,
          status: user.email_verified_at ? 'verified' : 'unverified',
          affiliation: user.affiliation,
          country: user.country,
          created_at: user.created_at
        })),
        activity_logs: logs,
        systemStats,
        summary: {
          totalUsers: users.length,
          verifiedUsers: users.filter(u => u.email_verified_at).length,
          unverifiedUsers: users.filter(u => !u.email_verified_at).length,
          usersByRole: {
            admin: users.filter(u => u.role === 'admin').length,
            editor: users.filter(u => u.role === 'editor').length,
            reviewer: users.filter(u => u.role === 'reviewer').length,
            author: users.filter(u => u.role === 'author').length
          },
          errorCount: logs.filter(log => log.level === 'ERROR').length,
          warningCount: logs.filter(log => log.level === 'WARNING').length,
          totalActivities: logs.length,
          systemHealth: systemStats.system?.uptime || 'Unknown'
        }
      };
      
      const dataStr = JSON.stringify(report, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `system_monitoring_report_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast.success('System monitoring report exported successfully');
    } catch (error) {
      console.error('Error exporting report:', error);
      toast.error('Failed to export system report');
    }
  };

  const getLevelIcon = (level) => {
    switch (level?.toLowerCase()) {
      case 'error':
      case 'critical':
        return <FaExclamationTriangle className="text-red-500" />;
      case 'warning':
        return <FaExclamationCircle className="text-yellow-500" />;
      case 'info':
        return <FaInfoCircle className="text-blue-500" />;
      case 'debug':
        return <FaBug className="text-gray-500" />;
      default:
        return <FaInfoCircle className="text-gray-500" />;
    }
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  const getUserStatusBadge = (user) => {
    if (!user.email_verified_at) {
      return <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">Unverified</span>;
    }
    // Since we don't have last_login_at in the API response, we'll just show verified status
    return <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">Verified</span>;
  };

  const getRoleColor = (role) => {
    const colors = {
      admin: 'bg-purple-100 text-purple-800',
      editor: 'bg-blue-100 text-blue-800',
      reviewer: 'bg-green-100 text-green-800',
      author: 'bg-orange-100 text-orange-800'
    };
    return colors[role] || 'bg-gray-100 text-gray-800';
  };

  // Chart configurations
  const getActivityTimelineChart = () => {
    const timeline = activityAnalytics.timeline || [];
    
    return {
      labels: timeline.map(item => item.period),
      datasets: [
        {
          label: 'Total Activity',
          data: timeline.map(item => item.total),
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          fill: true,
          tension: 0.4
        },
        {
          label: 'Created',
          data: timeline.map(item => item.created),
          borderColor: 'rgb(34, 197, 94)',
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          fill: false,
          tension: 0.4
        },
        {
          label: 'Updated',
          data: timeline.map(item => item.updated),
          borderColor: 'rgb(251, 191, 36)',
          backgroundColor: 'rgba(251, 191, 36, 0.1)',
          fill: false,
          tension: 0.4
        }
      ]
    };
  };

  const getSystemResourcesChart = () => {
    const memory = systemResources.memory || {};
    const disk = systemResources.disk || {};
    const cpu = systemResources.cpu || {};
    
    return {
      labels: ['Memory', 'Disk', 'CPU'],
      datasets: [
        {
          label: 'Usage %',
          data: [
            memory.usage_percentage || 0,
            disk.usage_percentage || 0,
            cpu.percentage || 0
          ],
          backgroundColor: [
            'rgba(59, 130, 246, 0.8)',
            'rgba(251, 191, 36, 0.8)',
            'rgba(239, 68, 68, 0.8)'
          ],
          borderColor: [
            'rgb(59, 130, 246)',
            'rgb(251, 191, 36)',
            'rgb(239, 68, 68)'
          ],
          borderWidth: 2
        }
      ]
    };
  };

  const getActivityByTypeChart = () => {
    const byType = activityAnalytics.by_type || [];
    
    return {
      labels: byType.map(item => item.type),
      datasets: [
        {
          data: byType.map(item => item.count),
          backgroundColor: [
            'rgba(59, 130, 246, 0.8)',
            'rgba(34, 197, 94, 0.8)',
            'rgba(251, 191, 36, 0.8)',
            'rgba(239, 68, 68, 0.8)',
            'rgba(168, 85, 247, 0.8)',
            'rgba(236, 72, 153, 0.8)'
          ],
          borderColor: [
            'rgb(59, 130, 246)',
            'rgb(34, 197, 94)',
            'rgb(251, 191, 36)',
            'rgb(239, 68, 68)',
            'rgb(168, 85, 247)',
            'rgb(236, 72, 153)'
          ],
          borderWidth: 2
        }
      ]
    };
  };

  const getMemoryUsageChart = () => {
    const memory = systemResources.memory || {};
    
    return {
      labels: ['Used', 'Available'],
      datasets: [
        {
          data: [
            memory.current_mb || 0,
            (memory.limit_mb || 0) - (memory.current_mb || 0)
          ],
          backgroundColor: [
            'rgba(239, 68, 68, 0.8)',
            'rgba(34, 197, 94, 0.8)'
          ],
          borderColor: [
            'rgb(239, 68, 68)',
            'rgb(34, 197, 94)'
          ],
          borderWidth: 2
        }
      ]
    };
  };

  const getActivityHeatmapChart = () => {
    const heatmap = activityAnalytics.heatmap || [];
    const hours = Array.from({ length: 24 }, (_, i) => i);
    const data = hours.map(hour => {
      const hourData = heatmap.find(h => h.hour === hour);
      return hourData ? hourData.count : 0;
    });
    
    return {
      labels: hours.map(h => `${h}:00`),
      datasets: [
        {
          label: 'Activity Count',
          data: data,
          backgroundColor: 'rgba(168, 85, 247, 0.8)',
          borderColor: 'rgb(168, 85, 247)',
          borderWidth: 1
        }
      ]
    };
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
      },
    },
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <span className="ml-4 text-lg text-gray-600">Loading system monitoring data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">System Monitoring</h1>
            <p className="text-gray-600">Complete system monitoring dashboard with users, logs, and performance metrics</p>
          </div>
          
          <div className="flex items-center gap-3">
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
            >
              <FaSyncAlt className={refreshing ? 'animate-spin' : ''} />
              Refresh
            </button>
            
            <button
              onClick={handleExportReport}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2"
            >
              <FaDownload />
              Export Report
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.icon}
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* System Overview Tab */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white p-6 rounded-lg border shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Users</p>
                  <p className="text-2xl font-bold text-blue-600">{users.length}</p>
                  <p className="text-sm text-gray-600">
                    Verified: {users.filter(u => u.email_verified_at).length}
                  </p>
                </div>
                <div className="p-3 bg-blue-50 rounded-full">
                  <FaUsers className="h-8 w-8 text-blue-600" />
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg border shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Verified Users</p>
                  <p className="text-2xl font-bold text-green-600">{users.filter(u => u.email_verified_at).length}</p>
                  <p className="text-sm text-gray-600">
                    Unverified: {users.filter(u => !u.email_verified_at).length}
                  </p>
                </div>
                <div className="p-3 bg-green-50 rounded-full">
                  <FaUserCheck className="h-8 w-8 text-green-600" />
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg border shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">System Logs</p>
                  <p className="text-2xl font-bold text-gray-900">{logs.length}</p>
                  <p className="text-sm text-gray-600">
                    Errors: {logs.filter(log => log.level === 'ERROR').length}
                  </p>
                </div>
                <div className="p-3 bg-yellow-50 rounded-full">
                  <FaFileAlt className="h-8 w-8 text-yellow-600" />
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg border shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Manuscripts</p>
                  <p className="text-2xl font-bold text-purple-600">{systemStats?.manuscripts?.total || 0}</p>
                  <p className="text-sm text-gray-600">Under review: {systemStats?.manuscripts?.by_status?.under_review || 0}</p>
                </div>
                <div className="p-3 bg-purple-50 rounded-full">
                  <FaFileAlt className="h-8 w-8 text-purple-600" />
                </div>
              </div>
            </div>
          </div>

          {/* User Roles Breakdown */}
          <div className="bg-white p-6 rounded-lg border shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">User Roles Distribution</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <p className="text-2xl font-bold text-gray-900">{systemStats?.users?.by_role?.admin || 0}</p>
                <p className="text-sm text-gray-600">Admins</p>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <p className="text-2xl font-bold text-gray-900">{systemStats?.users?.by_role?.editor || 0}</p>
                <p className="text-sm text-gray-600">Editors</p>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <p className="text-2xl font-bold text-gray-900">{systemStats?.users?.by_role?.reviewer || 0}</p>
                <p className="text-sm text-gray-600">Reviewers</p>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <p className="text-2xl font-bold text-gray-900">{systemStats?.users?.by_role?.author || 0}</p>
                <p className="text-sm text-gray-600">Authors</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Analytics Graphs Tab */}
      {activeTab === 'graphs' && (
        <div className="space-y-6">
          {/* Time Period Selector */}
          <div className="bg-white p-4 rounded-lg border shadow-sm">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Analytics Period</h3>
              <select
                value={timePeriod}
                onChange={(e) => setTimePeriod(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {timePeriods.map(period => (
                  <option key={period.value} value={period.value}>
                    {period.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Activity Timeline Chart */}
          <div className="bg-white p-6 rounded-lg border shadow-sm">
            <div className="flex items-center gap-2 mb-4">
              <FaChartLine className="text-blue-500" />
              <h3 className="text-lg font-semibold text-gray-900">Activity Timeline</h3>
            </div>
            <div className="h-80">
              <Line data={getActivityTimelineChart()} options={chartOptions} />
            </div>
          </div>

          {/* System Resources and Activity Distribution */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* System Resources Usage */}
            <div className="bg-white p-6 rounded-lg border shadow-sm">
              <div className="flex items-center gap-2 mb-4">
                <FaChartBar className="text-purple-500" />
                <h3 className="text-lg font-semibold text-gray-900">System Resources</h3>
              </div>
              <div className="h-64">
                <Bar data={getSystemResourcesChart()} options={chartOptions} />
              </div>
              <div className="mt-4 grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-sm text-gray-500">Memory</p>
                  <p className="text-lg font-bold text-blue-600">
                    {systemResources?.memory?.current_mb || 0}MB
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Disk</p>
                  <p className="text-lg font-bold text-yellow-600">
                    {systemResources?.disk?.used_gb || 0}GB
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">CPU</p>
                  <p className="text-lg font-bold text-red-600">
                    {systemResources?.cpu?.percentage || 0}%
                  </p>
                </div>
              </div>
            </div>

            {/* Activity by Type */}
            <div className="bg-white p-6 rounded-lg border shadow-sm">
              <div className="flex items-center gap-2 mb-4">
                <FaChartPie className="text-green-500" />
                <h3 className="text-lg font-semibold text-gray-900">Activity Distribution</h3>
              </div>
              <div className="h-64">
                <Pie data={getActivityByTypeChart()} options={pieChartOptions} />
              </div>
            </div>
          </div>

          {/* Memory Usage and Activity Heatmap */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Memory Usage Breakdown */}
            <div className="bg-white p-6 rounded-lg border shadow-sm">
              <div className="flex items-center gap-2 mb-4">
                <FaMemory className="text-red-500" />
                <h3 className="text-lg font-semibold text-gray-900">Memory Usage</h3>
              </div>
              <div className="h-64">
                <Doughnut data={getMemoryUsageChart()} options={pieChartOptions} />
              </div>
              <div className="mt-4 grid grid-cols-2 gap-4 text-center">
                <div>
                  <p className="text-sm text-gray-500">Used</p>
                  <p className="text-lg font-bold text-red-600">
                    {systemResources?.memory?.current_mb || 0}MB
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Available</p>
                  <p className="text-lg font-bold text-green-600">
                    {((systemResources?.memory?.limit_mb || 0) - (systemResources?.memory?.current_mb || 0))}MB
                  </p>
                </div>
              </div>
            </div>

            {/* Activity Heatmap by Hour */}
            <div className="bg-white p-6 rounded-lg border shadow-sm">
              <div className="flex items-center gap-2 mb-4">
                <FaClock className="text-purple-500" />
                <h3 className="text-lg font-semibold text-gray-900">Activity by Hour</h3>
              </div>
              <div className="h-64">
                <Bar data={getActivityHeatmapChart()} options={chartOptions} />
              </div>
            </div>
          </div>

          {/* Summary Statistics */}
          <div className="bg-white p-6 rounded-lg border shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Analytics Summary</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <p className="text-2xl font-bold text-blue-600">
                  {systemMetrics?.activity_summary?.total_activities || 0}
                </p>
                <p className="text-sm text-gray-600">Total Activities</p>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <p className="text-2xl font-bold text-green-600">
                  {systemMetrics?.activity_summary?.unique_users || 0}
                </p>
                <p className="text-sm text-gray-600">Active Users</p>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <p className="text-2xl font-bold text-purple-600">
                  {systemMetrics?.activity_summary?.most_active_hour || 0}:00
                </p>
                <p className="text-sm text-gray-600">Peak Hour</p>
              </div>
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <p className="text-2xl font-bold text-yellow-600">
                  {systemMetrics?.trends?.activity_change || 0}%
                </p>
                <p className="text-sm text-gray-600">Activity Change</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* All Users Tab */}
      {activeTab === 'users' && (
        <div className="bg-white rounded-lg border shadow-sm">
          <div className="p-4 border-b">
            <h3 className="text-lg font-semibold text-gray-900">All Users ({users.length})</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Affiliation</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {users.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <img 
                          src={user.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}&background=random`} 
                          alt={user.name}
                          className="h-10 w-10 rounded-full mr-3"
                        />
                        <div>
                          <div className="text-sm font-medium text-gray-900">{user.name}</div>
                          <div className="text-sm text-gray-500">{user.email || 'No email'}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full capitalize ${getRoleColor(user.role)}`}>
                        {user.role}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getUserStatusBadge(user)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.affiliation || 'Not specified'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatTimestamp(user.created_at)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Activity Logs Tab */}
      {activeTab === 'logs' && (
        <div className="bg-white rounded-lg border shadow-sm">
          <div className="p-4 border-b">
            <h3 className="text-lg font-semibold text-gray-900">Activity Logs ({logs.length})</h3>
          </div>
          <div className="max-h-96 overflow-y-auto">
            {logs.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                <FaInfoCircle className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                <p>No activity logs found.</p>
              </div>
            ) : (
              <div className="space-y-1">
                {logs.map((log) => (
                  <div key={log.id} className="p-4 hover:bg-gray-50 border-b border-gray-100 last:border-b-0">
                    <div className="flex items-start gap-3">
                      {getLevelIcon(log.level)}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900">
                          {log.description || log.message}
                        </p>
                        <div className="mt-1 flex items-center gap-4 text-xs text-gray-500">
                          <span>Event: {log.event}</span>
                          <span>Level: {log.level}</span>
                          <span>Module: {log.module}</span>
                          <span>User: {log.causer_name || 'System'}</span>
                          <span>{formatTimestamp(log.timestamp || log.created_at)}</span>
                        </div>
                        {log.subject_type && (
                          <div className="mt-1 text-xs text-gray-400">
                            Subject: {log.subject_type} #{log.subject_id}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Performance Tab */}
      {activeTab === 'performance' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* System Metrics */}
            <div className="bg-white p-6 rounded-lg border shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">System Metrics</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <FaMemory className="text-blue-500" />
                    <span className="text-sm font-medium text-gray-600">Memory Usage</span>
                  </div>
                  <span className="text-sm font-bold text-blue-600">
                    {systemStats?.system?.memory_usage?.current ? `${Math.round(systemStats.system.memory_usage.current / 1024 / 1024)}MB` : 'N/A'}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <FaHdd className="text-purple-500" />
                    <span className="text-sm font-medium text-gray-600">Storage Used</span>
                  </div>
                  <span className="text-sm font-bold text-purple-600">
                    {systemStats?.system?.storage_used?.used_mb ? `${systemStats.system.storage_used.used_mb}MB` : 'N/A'}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <FaDatabase className="text-green-500" />
                    <span className="text-sm font-medium text-gray-600">Database Status</span>
                  </div>
                  <span className="text-sm font-bold text-green-600">Connected</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <FaClock className="text-gray-500" />
                    <span className="text-sm font-medium text-gray-600">System Uptime</span>
                  </div>
                  <span className="text-sm font-bold text-gray-600">
                    {systemStats?.system?.uptime || '99.9%'}
                  </span>
                </div>
              </div>
            </div>

            {/* Quick Actions & Info */}
            <div className="bg-white p-6 rounded-lg border shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">System Information</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-600">PHP Version</span>
                  <span className="text-sm font-bold text-gray-600">8.1+</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-600">Laravel Version</span>
                  <span className="text-sm font-bold text-gray-600">10.x</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-600">Environment</span>
                  <span className="text-sm font-bold text-gray-600">Production</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-600">Last Backup</span>
                  <span className="text-sm font-bold text-gray-600">
                    {new Date().toLocaleDateString()}
                  </span>
                </div>
              </div>
              
              {/* Quick Actions */}
              <div className="mt-6 pt-4 border-t">
                <h4 className="text-sm font-medium text-gray-700 mb-3">Quick Actions</h4>
                <div className="grid grid-cols-1 gap-2">
                  <button 
                    onClick={() => toast.info('Backup functionality coming soon')}
                    className="text-sm px-3 py-2 bg-blue-100 hover:bg-blue-200 rounded transition-colors flex items-center gap-2"
                  >
                    <FaDownload />
                    Create System Backup
                  </button>
                  <button 
                    onClick={() => toast.info('Cache clear functionality coming soon')}
                    className="text-sm px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded transition-colors flex items-center gap-2"
                  >
                    <FaServer />
                    Clear System Cache
                  </button>
                  <button 
                    onClick={handleRefresh}
                    className="text-sm px-3 py-2 bg-green-100 hover:bg-green-200 rounded transition-colors flex items-center gap-2"
                  >
                    <FaSyncAlt />
                    Refresh All Data
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SystemMonitoring;