import React, { useState, useEffect } from 'react';
import { 
  FaHome, 
  FaFileAlt, 
  FaUsers, 
  FaBookOpen, 
  FaCog, 
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye,
  FaFilter,
  FaSearch,
  FaBullhorn,
  FaNewspaper,
  FaInfoCircle,
  FaStar,
  FaLanguage,
  FaCheckCircle,
  FaTimes,
  FaSpinner
} from 'react-icons/fa';
import { toast } from 'react-toastify';
import adminService from '../../services/adminService';

const ContentManagement = () => {
  const [content, setContent] = useState([]);
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [selectedContent, setSelectedContent] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'view'
  const [currentContent, setCurrentContent] = useState(null);
  const [filters, setFilters] = useState({
    type: '',
    language: '',
    status: '',
    search: ''
  });
  const [pagination, setPagination] = useState({
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0
  });

  const contentTypes = [
    { value: 'announcement', label: 'Announcements', icon: <FaBullhorn className="text-blue-500" />, color: 'blue' },
    { value: 'editorial', label: 'Editorial Content', icon: <FaNewspaper className="text-green-500" />, color: 'green' },
    { value: 'guidelines', label: 'Guidelines', icon: <FaFileAlt className="text-purple-500" />, color: 'purple' },
    { value: 'homepage', label: 'Homepage Content', icon: <FaHome className="text-yellow-500" />, color: 'yellow' },
    { value: 'about', label: 'About Page', icon: <FaInfoCircle className="text-indigo-500" />, color: 'indigo' },
    { value: 'contact', label: 'Contact Info', icon: <FaUsers className="text-red-500" />, color: 'red' },
    { value: 'policies', label: 'Policies', icon: <FaBookOpen className="text-gray-500" />, color: 'gray' }
  ];

  const languages = [
    { value: 'en', label: 'English' },
    { value: 'fr', label: 'French' }
  ];

  const statuses = [
    { value: 'published', label: 'Published', color: 'green' },
    { value: 'draft', label: 'Draft', color: 'yellow' }
  ];

  useEffect(() => {
    fetchContent();
    fetchStats();
  }, [filters, pagination.current_page]);

  const fetchContent = async () => {
    try {
      setLoading(true);
      const params = {
        ...filters,
        page: pagination.current_page,
        per_page: pagination.per_page
      };
      
      // Remove empty filters
      Object.keys(params).forEach(key => {
        if (!params[key]) delete params[key];
      });

      const response = await adminService.getContent(params);
      setContent(response.data || []);
      setPagination(response.meta || pagination);
    } catch (error) {
      console.error('Error fetching content:', error);
      toast.error('Failed to fetch content');
      setContent([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await adminService.getContentStats();
      setStats(response.data || {});
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const handleCreateContent = () => {
    setCurrentContent({
      type: 'announcement',
      language: 'en',
      title: { en: '', fr: '' },
      content: { en: '', fr: '' },
      status: 'published',
      featured: false,
      priority: 0
    });
    setModalMode('create');
    setShowModal(true);
  };

  const handleEditContent = (item) => {
    setCurrentContent(item);
    setModalMode('edit');
    setShowModal(true);
  };

  const handleViewContent = (item) => {
    setCurrentContent(item);
    setModalMode('view');
    setShowModal(true);
  };

  const handleDeleteContent = async (id) => {
    if (!window.confirm('Are you sure you want to delete this content?')) return;
    
    try {
      await adminService.deleteContent(id);
      toast.success('Content deleted successfully');
      fetchContent();
      fetchStats();
    } catch (error) {
      console.error('Error deleting content:', error);
      toast.error('Failed to delete content');
    }
  };

  const handleSaveContent = async (formData) => {
    try {
      if (modalMode === 'create') {
        await adminService.createContent(formData);
        toast.success('Content created successfully');
      } else if (modalMode === 'edit') {
        await adminService.updateContent(currentContent.id, formData);
        toast.success('Content updated successfully');
      }
      setShowModal(false);
      fetchContent();
      fetchStats();
    } catch (error) {
      console.error('Error saving content:', error);
      toast.error('Failed to save content');
    }
  };

  const handleBulkAction = async (action) => {
    if (selectedContent.length === 0) {
      toast.warning('Please select items first');
      return;
    }

    if (!window.confirm(`Are you sure you want to ${action} ${selectedContent.length} items?`)) return;

    try {
      await adminService.bulkContentAction(action, selectedContent);
      toast.success(`Bulk ${action} completed successfully`);
      setSelectedContent([]);
      fetchContent();
      fetchStats();
    } catch (error) {
      console.error('Error performing bulk action:', error);
      toast.error(`Failed to ${action} content`);
    }
  };

  const getTypeIcon = (type) => {
    const typeConfig = contentTypes.find(t => t.value === type);
    return typeConfig ? typeConfig.icon : <FaFileAlt className="text-gray-500" />;
  };

  const getTypeColor = (type) => {
    const typeConfig = contentTypes.find(t => t.value === type);
    return typeConfig ? typeConfig.color : 'gray';
  };

  const getStatusBadge = (status) => {
    const statusConfig = statuses.find(s => s.value === status);
    const color = statusConfig ? statusConfig.color : 'gray';
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full bg-${color}-100 text-${color}-800`}>
        {statusConfig ? statusConfig.label : status}
      </span>
    );
  };

  const truncateText = (text, maxLength = 100) => {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  };

  if (loading && content.length === 0) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <span className="ml-4 text-lg text-gray-600">Loading content...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Content Management</h1>
            <p className="text-gray-600 mt-2">Manage all journal content, announcements, and pages</p>
          </div>
          <button
            onClick={handleCreateContent}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
          >
            <FaPlus />
            Create Content
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg border shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Content</p>
              <p className="text-2xl font-bold text-blue-600">{stats.total_content || 0}</p>
            </div>
            <div className="p-3 bg-blue-50 rounded-full">
              <FaFileAlt className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Published</p>
              <p className="text-2xl font-bold text-green-600">{stats.by_status?.published || 0}</p>
            </div>
            <div className="p-3 bg-green-50 rounded-full">
              <FaCheckCircle className="h-6 w-6 text-green-600" />
            </div>
          </div>
                    </div>

        <div className="bg-white p-6 rounded-lg border shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Featured</p>
              <p className="text-2xl font-bold text-yellow-600">{stats.featured_count || 0}</p>
                    </div>
            <div className="p-3 bg-yellow-50 rounded-full">
              <FaStar className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border shadow-sm">
          <div className="flex items-center justify-between">
        <div>
              <p className="text-sm font-medium text-gray-600">Recent</p>
              <p className="text-2xl font-bold text-purple-600">{stats.recent_count || 0}</p>
            </div>
            <div className="p-3 bg-purple-50 rounded-full">
              <FaInfoCircle className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white p-6 rounded-lg border shadow-sm mb-6">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex-1 min-w-64">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search content..."
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
                    </div>
                  </div>

          <select
            value={filters.type}
            onChange={(e) => setFilters({ ...filters, type: e.target.value })}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Types</option>
            {contentTypes.map(type => (
              <option key={type.value} value={type.value}>{type.label}</option>
            ))}
          </select>

          <select
            value={filters.language}
            onChange={(e) => setFilters({ ...filters, language: e.target.value })}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Languages</option>
            {languages.map(lang => (
              <option key={lang.value} value={lang.value}>{lang.label}</option>
            ))}
          </select>

          <select
            value={filters.status}
            onChange={(e) => setFilters({ ...filters, status: e.target.value })}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Status</option>
            {statuses.map(status => (
              <option key={status.value} value={status.value}>{status.label}</option>
            ))}
          </select>

          <button
            onClick={() => setFilters({ type: '', language: '', status: '', search: '' })}
            className="px-3 py-2 text-gray-600 hover:text-gray-800"
          >
            Clear Filters
          </button>
              </div>

        {/* Bulk Actions */}
        {selectedContent.length > 0 && (
          <div className="mt-4 pt-4 border-t flex items-center gap-4">
            <span className="text-sm text-gray-600">
              {selectedContent.length} items selected
            </span>
            <div className="flex gap-2">
              <button
                onClick={() => handleBulkAction('publish')}
                className="px-3 py-1 text-sm bg-green-100 text-green-800 rounded hover:bg-green-200"
              >
                Publish
              </button>
              <button
                onClick={() => handleBulkAction('unpublish')}
                className="px-3 py-1 text-sm bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200"
              >
                Unpublish
              </button>
              <button
                onClick={() => handleBulkAction('feature')}
                className="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded hover:bg-blue-200"
              >
                Feature
              </button>
              <button
                onClick={() => handleBulkAction('delete')}
                className="px-3 py-1 text-sm bg-red-100 text-red-800 rounded hover:bg-red-200"
              >
                Delete
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Content Table */}
      <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
        {content.length === 0 ? (
          <div className="p-8 text-center">
            <FaFileAlt className="mx-auto h-12 w-12 text-gray-300 mb-4" />
            <p className="text-gray-500">No content found.</p>
            <button
              onClick={handleCreateContent}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Create First Content
            </button>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left">
                      <input
                        type="checkbox"
                        checked={selectedContent.length === content.length && content.length > 0}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedContent(content.map(item => item.id));
                          } else {
                            setSelectedContent([]);
                          }
                        }}
                        className="rounded border-gray-300"
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Content
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Language
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Updated
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {content.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <input
                          type="checkbox"
                          checked={selectedContent.includes(item.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedContent([...selectedContent, item.id]);
                            } else {
                              setSelectedContent(selectedContent.filter(id => id !== item.id));
                            }
                          }}
                          className="rounded border-gray-300"
                        />
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-start gap-3">
                          {getTypeIcon(item.type)}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <h3 className="text-sm font-medium text-gray-900">
                                {item.title?.en || item.title || 'Untitled'}
                              </h3>
                              {item.featured && (
                                <FaStar className="h-4 w-4 text-yellow-500" />
                              )}
                            </div>
                            <p className="text-sm text-gray-500 mt-1">
                              {truncateText(item.content?.en || item.content || '')}
                            </p>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full bg-${getTypeColor(item.type)}-100 text-${getTypeColor(item.type)}-800 capitalize`}>
                          {item.type}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-1">
                          <FaLanguage className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-900 uppercase">{item.language}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        {getStatusBadge(item.status)}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500">
                        {new Date(item.updated_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleViewContent(item)}
                            className="p-1 text-gray-600 hover:text-blue-600"
                            title="View"
                          >
                            <FaEye />
                          </button>
                          <button
                            onClick={() => handleEditContent(item)}
                            className="p-1 text-gray-600 hover:text-green-600"
                            title="Edit"
                          >
                            <FaEdit />
                          </button>
                          <button
                            onClick={() => handleDeleteContent(item.id)}
                            className="p-1 text-gray-600 hover:text-red-600"
                            title="Delete"
                          >
                            <FaTrash />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {pagination.last_page > 1 && (
              <div className="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
                <div className="text-sm text-gray-500">
                  Showing {((pagination.current_page - 1) * pagination.per_page) + 1} to {Math.min(pagination.current_page * pagination.per_page, pagination.total)} of {pagination.total} results
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setPagination({ ...pagination, current_page: pagination.current_page - 1 })}
                    disabled={pagination.current_page === 1}
                    className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 hover:bg-gray-50"
                  >
                    Previous
                  </button>
                  <span className="px-3 py-1 text-sm">
                    Page {pagination.current_page} of {pagination.last_page}
                  </span>
                  <button
                    onClick={() => setPagination({ ...pagination, current_page: pagination.current_page + 1 })}
                    disabled={pagination.current_page === pagination.last_page}
                    className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 hover:bg-gray-50"
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Content Modal */}
      {showModal && (
        <ContentModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          mode={modalMode}
          content={currentContent}
          onSave={handleSaveContent}
          contentTypes={contentTypes}
          languages={languages}
          statuses={statuses}
        />
      )}
    </div>
  );
};

// Content Modal Component
const ContentModal = ({ isOpen, onClose, mode, content, onSave, contentTypes, languages, statuses }) => {
  const [formData, setFormData] = useState(content || {});
  const [loading, setLoading] = useState(false);
  const [activeLanguage, setActiveLanguage] = useState('en');

  useEffect(() => {
    setFormData(content || {});
  }, [content]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      await onSave(formData);
    } catch (error) {
      console.error('Error saving:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field, value) => {
    setFormData({ ...formData, [field]: value });
  };

  const updateTranslation = (field, language, value) => {
    setFormData({
      ...formData,
      [field]: {
        ...formData[field],
        [language]: value
      }
    });
  };

  if (!isOpen) return null;

  const isReadOnly = mode === 'view';

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              {mode === 'create' ? 'Create Content' : mode === 'edit' ? 'Edit Content' : 'View Content'}
            </h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600"
            >
              <FaTimes />
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Content Type
              </label>
              <select
                value={formData.type || ''}
                onChange={(e) => updateFormData('type', e.target.value)}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                required
              >
                <option value="">Select Type</option>
                {contentTypes.map(type => (
                  <option key={type.value} value={type.value}>{type.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Primary Language
              </label>
              <select
                value={formData.language || 'en'}
                onChange={(e) => updateFormData('language', e.target.value)}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                required
              >
                {languages.map(lang => (
                  <option key={lang.value} value={lang.value}>{lang.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                value={formData.status || 'published'}
                onChange={(e) => updateFormData('status', e.target.value)}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
              >
                {statuses.map(status => (
                  <option key={status.value} value={status.value}>{status.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Priority
              </label>
              <input
                type="number"
                value={formData.priority || 0}
                onChange={(e) => updateFormData('priority', parseInt(e.target.value))}
                disabled={isReadOnly}
                min="0"
                max="100"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
              />
            </div>
          </div>

          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={formData.featured || false}
                onChange={(e) => updateFormData('featured', e.target.checked)}
                disabled={isReadOnly}
                className="rounded border-gray-300"
              />
              <span className="text-sm font-medium text-gray-700">Featured Content</span>
            </label>
          </div>

          {/* Language Tabs */}
          <div>
            <div className="border-b border-gray-200 mb-4">
              <nav className="-mb-px flex space-x-8">
                {languages.map(lang => (
                  <button
                    key={lang.value}
                    type="button"
                    onClick={() => setActiveLanguage(lang.value)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeLanguage === lang.value
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    {lang.label}
                  </button>
                ))}
              </nav>
              </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Title ({languages.find(l => l.value === activeLanguage)?.label})
                </label>
                <input
                  type="text"
                  value={formData.title?.[activeLanguage] || ''}
                  onChange={(e) => updateTranslation('title', activeLanguage, e.target.value)}
                  disabled={isReadOnly}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                  required={activeLanguage === 'en'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Content ({languages.find(l => l.value === activeLanguage)?.label})
                </label>
                <textarea
                  value={formData.content?.[activeLanguage] || ''}
                  onChange={(e) => updateTranslation('content', activeLanguage, e.target.value)}
                  disabled={isReadOnly}
                  rows={8}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                  required={activeLanguage === 'en'}
                />
              </div>
            </div>
          </div>

          {!isReadOnly && (
            <div className="flex justify-end gap-4 pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
              >
                {loading && <FaSpinner className="animate-spin" />}
                {mode === 'create' ? 'Create' : 'Update'}
              </button>
        </div>
          )}
        </form>
      </div>
    </div>
  );
};

export default ContentManagement;