import { useState, useEffect, useCallback, memo } from 'react';
import { FaInfoCircle, FaUsers, FaFileAlt, FaEnvelope, FaGlobe, FaLock, FaPalette } from 'react-icons/fa';
import { toast } from 'react-hot-toast';
import settingsService from '../../services/settingsService';
import LoadingSpinner from '../../components/common/LoadingSpinner';

const SettingSection = memo(({ 
  title, 
  children,
  icon: IconComponent
}) => (
  <div className="mb-8 border-b border-gray-200 pb-6">
    <div className="flex items-center mb-4">
      <div className="p-2 rounded-lg bg-blue-50 text-blue-600 mr-3">
        <IconComponent className="h-5 w-5" />
      </div>
      <h2 className="text-xl font-semibold text-gray-800">{title}</h2>
    </div>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {children}
    </div>
  </div>
));

export default function JournalSettings() {
  const [initialSettings, setInitialSettings] = useState({
    // General Settings
    journalName: "",
    journalAbbreviation: "",
    issn: "",
    publisher: "",
    contactEmail: "",
    
    // Appearance
    primaryColor: "#3b82f6",
    secondaryColor: "#10b981",
    logo: "",
    favicon: "",
    
    // Submission Settings
    submissionsOpen: true,
    submissionFee: 0,
    allowedFileTypes: [".doc", ".docx", ".pdf"],
    maxFileSize: 10, // MB
    
    // Review Process
    reviewProcess: "double-blind",
    minReviewers: 2,
    reviewDeadline: 14, // days
    
    // Email Notifications
    notifyAuthorsOnSubmission: true,
    notifyReviewersOnAssignment: true,
    notifyEditorsOnDecision: true,
    
    // Privacy & Security
    requireORCID: false,
    enableTwoFactorAuth: true,
    dataRetentionPeriod: 5, // years
  });

  const [settings, setSettings] = useState(initialSettings);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const data = await settingsService.getAllSettings();
      const transformedSettings = Object.values(data).flat().reduce((acc, setting) => {
        acc[setting.key] = setting.value;
        return acc;
      }, {});
      setInitialSettings(transformedSettings);
      setSettings(transformedSettings);
    } catch (error) {
      console.error('Error loading settings:', error);
      toast.error('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = useCallback((e) => {
    const { name, value, type, checked, multiple } = e.target;
    
    setSettings(prev => {
      let newValue;
      if (type === 'checkbox') {
        newValue = checked;
      } else if (multiple) {
        newValue = Array.from(e.target.selectedOptions, option => option.value);
      } else {
        newValue = value;
      }
      
      if (prev[name] === newValue) return prev;
      
      return {
        ...prev,
        [name]: newValue
      };
    });
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    try {
      const settingsToUpdate = Object.entries(settings)
        .filter(([key, value]) => JSON.stringify(value) !== JSON.stringify(initialSettings[key]))
        .map(([key, value]) => ({
          key,
          value: value === '' ? null : value
        }));

      if (settingsToUpdate.length === 0) {
        toast.success('No changes to save');
        setSaving(false);
        setIsEditing(false);
        return;
      }

      const response = await settingsService.updateSettings(settingsToUpdate);
      
      if (response.success) {
        toast.success('Settings saved successfully');
        setInitialSettings(settings);
        setIsEditing(false);
      } else {
        if (response.errors) {
          Object.entries(response.errors).forEach(([field, messages]) => {
            toast.error(`${field}: ${messages.join(', ')}`);
          });
        } else {
          toast.error(response.message || 'Failed to save settings');
        }
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      if (error.errors) {
        Object.entries(error.errors).forEach(([field, messages]) => {
          toast.error(`${field}: ${messages.join(', ')}`);
        });
      } else {
        toast.error(error.message || 'Failed to save settings');
      }
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setSettings(initialSettings);
    setIsEditing(false);
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="max-w-8xl mx-auto px-4 py-8">
      <form onSubmit={handleSubmit}>
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Journal Settings
          </h1>
          {!isEditing ? (
            <button
              type="button"
              onClick={() => setIsEditing(true)}
              className="px-6 py-3 bg-blue-600 text-white font-medium rounded-lg shadow hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Edit Settings
            </button>
          ) : (
            <div className="flex gap-4">
              <button
                type="button"
                onClick={handleCancel}
                className="px-6 py-3 bg-gray-600 text-white font-medium rounded-lg shadow hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={saving}
                className="px-6 py-3 bg-blue-600 text-white font-medium rounded-lg shadow hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
              >
                {saving ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          )}
        </div>
        
        <div className="bg-white rounded-xl shadow-md p-6">
          {/* General Settings */}
          <SettingSection title="General Information" icon={FaInfoCircle}>
            <div>
              <label htmlFor="journalName" className="block text-sm font-medium text-gray-700 mb-1">Journal Name</label>
              <input
                id="journalName"
                type="text"
                name="journalName"
                value={settings.journalName}
                onChange={handleChange}
                disabled={!isEditing}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:text-gray-500"
              />
            </div>
            <div>
              <label htmlFor="journalAbbreviation" className="block text-sm font-medium text-gray-700 mb-1">Journal Abbreviation</label>
              <input
                id="journalAbbreviation"
                type="text"
                name="journalAbbreviation"
                value={settings.journalAbbreviation}
                onChange={handleChange}
                disabled={!isEditing}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:text-gray-500"
              />
            </div>
            <div>
              <label htmlFor="issn" className="block text-sm font-medium text-gray-700 mb-1">ISSN</label>
              <input
                id="issn"
                type="text"
                name="issn"
                value={settings.issn}
                onChange={handleChange}
                disabled={!isEditing}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:text-gray-500"
              />
            </div>
            <div>
              <label htmlFor="publisher" className="block text-sm font-medium text-gray-700 mb-1">Publisher</label>
              <input
                id="publisher"
                type="text"
                name="publisher"
                value={settings.publisher}
                onChange={handleChange}
                disabled={!isEditing}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:text-gray-500"
              />
            </div>
          </SettingSection>

          {/* Appearance */}
          <SettingSection title="Appearance" icon={FaPalette}>
            <div>
              <label htmlFor="primaryColor" className="block text-sm font-medium text-gray-700 mb-1">Primary Color</label>
              <div className="flex items-center">
                <input
                  id="primaryColor"
                  type="color"
                  name="primaryColor"
                  value={settings.primaryColor}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="h-10 w-10 rounded border border-gray-300 cursor-pointer disabled:bg-gray-100 disabled:text-gray-500"
                />
                <span className="ml-2 text-sm text-gray-600">{settings.primaryColor}</span>
              </div>
            </div>
            <div>
              <label htmlFor="secondaryColor" className="block text-sm font-medium text-gray-700 mb-1">Secondary Color</label>
              <div className="flex items-center">
                <input
                  id="secondaryColor"
                  type="color"
                  name="secondaryColor"
                  value={settings.secondaryColor}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="h-10 w-10 rounded border border-gray-300 cursor-pointer disabled:bg-gray-100 disabled:text-gray-500"
                />
                <span className="ml-2 text-sm text-gray-600">{settings.secondaryColor}</span>
              </div>
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">Journal Logo</label>
              <div className="mt-1 flex items-center">
                <span className="inline-block h-12 w-12 rounded-full overflow-hidden bg-gray-100">
                  {settings.logo ? (
                    <img src={settings.logo} alt="Journal logo" className="h-full w-full" />
                  ) : (
                    <div className="h-full w-full flex items-center justify-center text-gray-400">
                      <FaFileAlt className="h-6 w-6" />
                    </div>
                  )}
                </span>
                <button
                  type="button"
                  disabled={!isEditing}
                  className="ml-5 bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-100 disabled:text-gray-500 disabled:cursor-not-allowed"
                >
                  Change
                </button>
              </div>
            </div>
          </SettingSection>

          {/* Submission Settings */}
          <SettingSection title="Submission Settings" icon={FaFileAlt}>
            <div className="flex items-center">
              <input
                id="submissionsOpen"
                type="checkbox"
                name="submissionsOpen"
                checked={settings.submissionsOpen}
                onChange={handleChange}
                disabled={!isEditing}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:bg-gray-100 disabled:text-gray-500"
              />
              <label htmlFor="submissionsOpen" className="ml-2 block text-sm text-gray-700">Accepting Submissions</label>
            </div>
            <div>
              <label htmlFor="submissionFee" className="block text-sm font-medium text-gray-700 mb-1">Submission Fee (USD)</label>
              <input
                id="submissionFee"
                type="number"
                name="submissionFee"
                value={settings.submissionFee}
                onChange={handleChange}
                min="0"
                disabled={!isEditing}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:text-gray-500"
              />
            </div>
            <div>
              <label htmlFor="allowedFileTypes" className="block text-sm font-medium text-gray-700 mb-1">Allowed File Types</label>
              <select
                id="allowedFileTypes"
                name="allowedFileTypes"
                value={settings.allowedFileTypes}
                onChange={handleChange}
                multiple
                disabled={!isEditing}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:text-gray-500"
              >
                <option value=".doc">Word Document (.doc)</option>
                <option value=".docx">Word Document (.docx)</option>
                <option value=".pdf">PDF (.pdf)</option>
                <option value=".odt">OpenDocument (.odt)</option>
                <option value=".rtf">Rich Text (.rtf)</option>
              </select>
            </div>
            <div>
              <label htmlFor="maxFileSize" className="block text-sm font-medium text-gray-700 mb-1">Max File Size (MB)</label>
              <input
                id="maxFileSize"
                type="number"
                name="maxFileSize"
                value={settings.maxFileSize}
                onChange={handleChange}
                min="1"
                disabled={!isEditing}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:text-gray-500"
              />
            </div>
          </SettingSection>

          {/* Review Process */}
          <SettingSection title="Review Process" icon={FaUsers}>
            <div>
              <label htmlFor="reviewProcess" className="block text-sm font-medium text-gray-700 mb-1">Review Process</label>
              <select
                id="reviewProcess"
                name="reviewProcess"
                value={settings.reviewProcess}
                onChange={handleChange}
                disabled={!isEditing}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:text-gray-500"
              >
                <option value="single-blind">Single-blind</option>
                <option value="double-blind">Double-blind</option>
                <option value="open">Open</option>
              </select>
            </div>
            <div>
              <label htmlFor="minReviewers" className="block text-sm font-medium text-gray-700 mb-1">Minimum Reviewers per Submission</label>
              <input
                id="minReviewers"
                type="number"
                name="minReviewers"
                value={settings.minReviewers}
                onChange={handleChange}
                min="1"
                max="5"
                disabled={!isEditing}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:text-gray-500"
              />
            </div>
            <div>
              <label htmlFor="reviewDeadline" className="block text-sm font-medium text-gray-700 mb-1">Review Deadline (days)</label>
              <input
                id="reviewDeadline"
                type="number"
                name="reviewDeadline"
                value={settings.reviewDeadline}
                onChange={handleChange}
                min="7"
                max="30"
                disabled={!isEditing}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:text-gray-500"
              />
            </div>
          </SettingSection>

          {/* Email Notifications */}
          <SettingSection title="Email Notifications" icon={FaEnvelope}>
            <div className="flex items-center">
              <input
                id="notifyAuthorsOnSubmission"
                type="checkbox"
                name="notifyAuthorsOnSubmission"
                checked={settings.notifyAuthorsOnSubmission}
                onChange={handleChange}
                disabled={!isEditing}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:bg-gray-100 disabled:text-gray-500"
              />
              <label htmlFor="notifyAuthorsOnSubmission" className="ml-2 block text-sm text-gray-700">Notify authors on submission</label>
            </div>
            <div className="flex items-center">
              <input
                id="notifyReviewersOnAssignment"
                type="checkbox"
                name="notifyReviewersOnAssignment"
                checked={settings.notifyReviewersOnAssignment}
                onChange={handleChange}
                disabled={!isEditing}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:bg-gray-100 disabled:text-gray-500"
              />
              <label htmlFor="notifyReviewersOnAssignment" className="ml-2 block text-sm text-gray-700">Notify reviewers on assignment</label>
            </div>
            <div className="flex items-center">
              <input
                id="notifyEditorsOnDecision"
                type="checkbox"
                name="notifyEditorsOnDecision"
                checked={settings.notifyEditorsOnDecision}
                onChange={handleChange}
                disabled={!isEditing}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:bg-gray-100 disabled:text-gray-500"
              />
              <label htmlFor="notifyEditorsOnDecision" className="ml-2 block text-sm text-gray-700">Notify editors on decision</label>
            </div>
            <div>
              <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700 mb-1">Contact Email</label>
              <input
                id="contactEmail"
                type="email"
                name="contactEmail"
                value={settings.contactEmail}
                onChange={handleChange}
                disabled={!isEditing}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:text-gray-500"
              />
            </div>
          </SettingSection>

          {/* Privacy & Security */}
          <SettingSection title="Privacy & Security" icon={FaLock}>
            <div className="flex items-center">
              <input
                id="requireORCID"
                type="checkbox"
                name="requireORCID"
                checked={settings.requireORCID}
                onChange={handleChange}
                disabled={!isEditing}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:bg-gray-100 disabled:text-gray-500"
              />
              <label htmlFor="requireORCID" className="ml-2 block text-sm text-gray-700">Require ORCID for submissions</label>
            </div>
            <div className="flex items-center">
              <input
                id="enableTwoFactorAuth"
                type="checkbox"
                name="enableTwoFactorAuth"
                checked={settings.enableTwoFactorAuth}
                onChange={handleChange}
                disabled={!isEditing}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:bg-gray-100 disabled:text-gray-500"
              />
              <label htmlFor="enableTwoFactorAuth" className="ml-2 block text-sm text-gray-700">Enable Two-Factor Authentication</label>
            </div>
            <div>
              <label htmlFor="dataRetentionPeriod" className="block text-sm font-medium text-gray-700 mb-1">Data Retention Period (years)</label>
              <input
                id="dataRetentionPeriod"
                type="number"
                name="dataRetentionPeriod"
                value={settings.dataRetentionPeriod}
                onChange={handleChange}
                min="1"
                max="10"
                disabled={!isEditing}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:text-gray-500"
              />
            </div>
          </SettingSection>
        </div>
      </form>
    </div>
  );
}