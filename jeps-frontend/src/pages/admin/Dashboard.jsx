import { FaUsers, FaCog, FaBook, FaChartLine, FaDatabase, FaShieldAlt, FaChevronRight, FaMoneyBillWave, FaFileAlt, FaUserCheck, FaChartBar } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import adminService from '../../services/adminService';
import { useEffect, useState } from 'react';

const AdminDashboard = () => {
  const [systemStats, setSystemStats] = useState([
    { name: 'Total Users', value: '243', icon: <FaUsers className="text-blue-500" />, color: 'bg-blue-100' },
    { name: 'Published Articles', value: '187', icon: <FaBook className="text-green-500" />, color: 'bg-green-100' },
    { name: 'Storage Used', value: '4.2/10 GB', icon: <FaDatabase className="text-purple-500" />, color: 'bg-purple-100' },
    { name: 'System Status', value: 'Operational', icon: <FaShieldAlt className="text-teal-500" />, color: 'bg-teal-100' }
  ]);

  const [recentActivities] = useState([
    { id: 1, user: 'Dr. Tambi', action: 'Assigned reviewers to JEMS-2025-011', time: '10 mins ago', icon: <FaUsers className="text-blue-500" /> },
    { id: 2, user: 'System', action: 'Automated backup completed', time: '1 hour ago', icon: <FaDatabase className="text-purple-500" /> },
    { id: 3, user: 'Admin', action: 'Updated journal submission guidelines', time: '2 hours ago', icon: <FaCog className="text-yellow-500" /> },
    { id: 4, user: 'Dr. Ngwa', action: 'Submitted new manuscript', time: '3 hours ago', icon: <FaBook className="text-green-500" /> }
  ]);

  const fetchDashboardData = async () => {
    try {
      // Try to fetch real data from the admin service
      const response = await adminService.getDashboardStats();
      const data = response.data;
      
      // Convert API response to the expected array format
      const statsArray = [
        {
          name: 'Total Manuscripts',
          value: data.manuscripts?.total || 0,
          icon: <FaFileAlt className="text-blue-500" />,
          color: 'bg-blue-100'
        },
        {
          name: 'Total Payments',
          value: data.payments?.total || 0,
          icon: <FaMoneyBillWave className="text-green-500" />,
          color: 'bg-green-100'
        },
        {
          name: 'Total Reviews',
          value: data.reviews?.total || 0,
          icon: <FaUserCheck className="text-purple-500" />,
          color: 'bg-purple-100'
        },
        {
          name: 'Total Users',
          value: data.users?.total || 0,
          icon: <FaUsers className="text-yellow-500" />,
          color: 'bg-yellow-100'
        }
      ];
      
      setSystemStats(statsArray);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      // Use mock data as fallback - keep the original array structure
      setSystemStats([
        { name: 'Total Users', value: '0', icon: <FaUsers className="text-blue-500" />, color: 'bg-blue-100' },
        { name: 'Published Articles', value: '0', icon: <FaBook className="text-green-500" />, color: 'bg-green-100' },
        { name: 'Storage Used', value: '0/10 GB', icon: <FaDatabase className="text-purple-500" />, color: 'bg-purple-100' },
        { name: 'System Status', value: 'Offline', icon: <FaShieldAlt className="text-red-500" />, color: 'bg-red-100' }
      ]);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  return (
    <div className="p-6">
      <div className="mb-8">
        <motion.h1 
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="text-2xl font-bold text-gray-800"
        >
          Administrator Dashboard
        </motion.h1>
        <motion.p 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.1, duration: 0.3 }}
          className="text-gray-600"
        >
          Manage system settings and oversee journal operations.
        </motion.p>
      </div>
      
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
      >
        {systemStats.map((stat, index) => (
          <motion.div 
            key={index}
            whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)" }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
            className={`bg-white p-6 rounded-xl shadow-md border border-gray-100 hover:border-transparent transition-all duration-200`}
          >
            <div className="flex items-center">
              <div className={`p-3 rounded-full ${stat.color} mr-4`}>
                {stat.icon}
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                <p className="text-2xl font-semibold text-gray-800">{stat.value}</p>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <motion.div 
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="lg:col-span-2 bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden"
        >
          <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h2 className="text-lg font-medium text-gray-800">Recent Activities</h2>
          </div>
          <div className="divide-y divide-gray-200">
            {recentActivities.map((activity) => (
              <motion.div 
                key={activity.id}
                whileHover={{ backgroundColor: "rgba(249, 250, 251, 1)" }}
                className="p-6 transition-colors duration-200 flex items-start"
              >
                <div className={`p-2 rounded-lg ${activity.icon.props.className.includes('blue') ? 'bg-blue-50' : 
                                 activity.icon.props.className.includes('green') ? 'bg-green-50' :
                                 activity.icon.props.className.includes('purple') ? 'bg-purple-50' : 'bg-yellow-50'} mr-4`}>
                  {activity.icon}
                </div>
                <div className="flex-1">
                  <p className="text-gray-800 font-medium">{activity.action}</p>
                  <div className="flex items-center mt-2 text-sm text-gray-600">
                    <span className="mr-4">By: {activity.user}</span>
                    <span>{activity.time}</span>
                  </div>
                </div>
                <FaChevronRight className="text-gray-400" />
              </motion.div>
            ))}
          </div>
        </motion.div>
        
        <motion.div 
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden"
        >
          <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h2 className="text-lg font-medium text-gray-800">System Quick Links</h2>
          </div>
          <div className="p-6">
            <div className="space-y-3">
              <motion.a 
                whileHover={{ x: 5 }}
                href="#" 
                className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200 flex items-center"
              >
                <div className="p-2 rounded-lg bg-blue-50 mr-3">
                  <FaUsers className="text-blue-500" />
                </div>
                <span className="font-medium">User Management</span>
                <FaChevronRight className="ml-auto text-gray-400" />
              </motion.a>
              <motion.a 
                whileHover={{ x: 5 }}
                href="#" 
                className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200 flex items-center"
              >
                <div className="p-2 rounded-lg bg-yellow-50 mr-3">
                  <FaCog className="text-yellow-500" />
                </div>
                <span className="font-medium">Journal Settings</span>
                <FaChevronRight className="ml-auto text-gray-400" />
              </motion.a>
              <motion.a 
                whileHover={{ x: 5 }}
                href="#" 
                className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200 flex items-center"
              >
                <div className="p-2 rounded-lg bg-green-50 mr-3">
                  <FaBook className="text-green-500" />
                </div>
                <span className="font-medium">Content Management</span>
                <FaChevronRight className="ml-auto text-gray-400" />
              </motion.a>
              <motion.a 
                whileHover={{ x: 5 }}
                href="#" 
                className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200 flex items-center"
              >
                <div className="p-2 rounded-lg bg-purple-50 mr-3">
                  <FaChartLine className="text-purple-500" />
                </div>
                <span className="font-medium">Analytics Dashboard</span>
                <FaChevronRight className="ml-auto text-gray-400" />
              </motion.a>
            </div>
          </div>
        </motion.div>
      </div>
      
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="bg-white p-6 rounded-xl shadow-md border border-gray-100"
      >
        <h3 className="text-lg font-medium text-gray-800 mb-6">System Maintenance</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <motion.div 
            whileHover={{ scale: 1.01 }}
            className="border border-gray-200 rounded-xl p-6 hover:shadow-sm transition-all"
          >
            <h4 className="font-medium text-gray-800 mb-4">Backup Status</h4>
            <div className="w-full bg-gray-200 rounded-full h-2.5 mb-3">
              <div 
                className="bg-blue-600 h-2.5 rounded-full transition-all duration-1000 ease-out" 
                style={{ width: '85%' }}
              ></div>
            </div>
            <p className="text-sm text-gray-600 mb-4">Last backup: Today, 01:00 AM</p>
            <motion.button 
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
            >
              Run Manual Backup
            </motion.button>
          </motion.div>
          <motion.div 
            whileHover={{ scale: 1.01 }}
            className="border border-gray-200 rounded-xl p-6 hover:shadow-sm transition-all"
          >
            <h4 className="font-medium text-gray-800 mb-4">System Updates</h4>
            <p className="text-sm text-gray-600 mb-3">Current version: 2.3.1</p>
            <div className="flex items-center text-sm text-gray-600 mb-6">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
              <span>All systems operational</span>
            </div>
            <motion.button 
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="px-4 py-2 bg-gray-100 text-gray-800 text-sm rounded-lg hover:bg-gray-200 transition-colors"
            >
              Check for Updates
            </motion.button>
          </motion.div>
        </div>
      </motion.div>

      {/* Quick Links */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-white rounded-lg shadow-md p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-2 gap-4">
          <Link
            to="/admin/users"
            className="flex items-center p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
          >
            <FaUsers className="text-blue-600 mr-3" />
            <span className="text-sm font-medium text-blue-900">User Management</span>
          </Link>
          <Link
            to="/admin/payments"
            className="flex items-center p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
          >
            <FaMoneyBillWave className="text-green-600 mr-3" />
            <span className="text-sm font-medium text-green-900">Payment Management</span>
          </Link>
          <Link
            to="/admin/manuscripts"
            className="flex items-center p-3 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors"
          >
            <FaFileAlt className="text-purple-600 mr-3" />
            <span className="text-sm font-medium text-purple-900">Manuscript Management</span>
          </Link>
          <Link
            to="/admin/reviews"
            className="flex items-center p-3 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors"
          >
            <FaUserCheck className="text-orange-600 mr-3" />
            <span className="text-sm font-medium text-orange-900">Review Management</span>
          </Link>
          <Link
            to="/admin/settings"
            className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <FaCog className="text-gray-600 mr-3" />
            <span className="text-sm font-medium text-gray-900">Journal Settings</span>
          </Link>
          <Link
            to="/admin/analytics"
            className="flex items-center p-3 bg-indigo-50 rounded-lg hover:bg-indigo-100 transition-colors"
          >
            <FaChartBar className="text-indigo-600 mr-3" />
            <span className="text-sm font-medium text-indigo-900">Analytics Dashboard</span>
          </Link>
        </div>
      </motion.div>
    </div>
  );
};

export default AdminDashboard;