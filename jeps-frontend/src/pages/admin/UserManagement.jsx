import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FaUserPlus,
  FaSearch,
  FaEdit,
  FaTrash,
  FaUserShield,
  FaUserEdit,
  FaUserCheck,
  FaGlobe,
  FaUniversity,
  FaLanguage,
  FaRandom
} from 'react-icons/fa';
import userService from '../../services/userService';

const defaultUser = {
  name: '',
  email: '',
  password: '',
  role: 'author',
  affiliation: '',
  country: '',
  expertise: [],
  preferred_language: 'en',
};

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [formUser, setFormUser] = useState(defaultUser);
  const [isLoading, setIsLoading] = useState(false);
  const [editUserId, setEditUserId] = useState(null);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(1);
  const [meta, setMeta] = useState({});
  const [links, setLinks] = useState([]);

  // Load users from backend
  useEffect(() => {
    fetchUsers(page, searchTerm);
    // eslint-disable-next-line
  }, [page]);

  const fetchUsers = async (page = 1, search = '') => {
    setIsLoading(true);
    try {
      const data = await userService.getUsers(null, page, search);
      setUsers(data.data || data);
      setMeta(data.meta || {});
      setLinks(data.links || []);
    } catch (err) {
      setError('Failed to load users');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setPage(1);
    fetchUsers(1, e.target.value);
  };

  const handleOpenCreate = () => {
    setFormUser(defaultUser);
    setEditUserId(null);
    setIsCreateModalOpen(true);
  };

  const handleOpenEdit = (user) => {
    setFormUser({
      ...user,
      password: '', // Don't show password
      preferred_language: user.preferred_language || 'en',
      expertise: Array.isArray(user.expertise) ? user.expertise : (user.expertise ? [user.expertise] : []),
    });
    setEditUserId(user.id);
    setIsEditModalOpen(true);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormUser((prev) => ({ ...prev, [name]: value }));
  };

  const handleExpertiseChange = (e) => {
    setFormUser((prev) => ({ ...prev, expertise: e.target.value.split(',').map(s => s.trim()) }));
  };

  const handleCreateUser = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const userToCreate = { ...formUser };
      if (!userToCreate.password) {
        setError('Password is required');
        setIsLoading(false);
        return;
      }
      await userService.createUser(userToCreate);
      setIsCreateModalOpen(false);
      fetchUsers();
    } catch (err) {
      setError('Failed to create user');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditUser = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const userToUpdate = { ...formUser };
      if (!userToUpdate.password) {
        delete userToUpdate.password;
      }
      await userService.updateUser(editUserId, userToUpdate);
      setIsEditModalOpen(false);
      fetchUsers();
    } catch (err) {
      setError('Failed to update user');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteUser = async (id) => {
    if (!window.confirm('Are you sure you want to delete this user?')) return;
    setIsLoading(true);
    try {
      await userService.deleteUser(id);
      fetchUsers();
    } catch (err) {
      setError('Failed to delete user');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGeneratePassword = () => {
    const randomPass = Math.random().toString(36).slice(-10) + Math.random().toString(36).slice(-2);
    setFormUser((prev) => ({ ...prev, password: randomPass }));
  };

  const filteredUsers = users.filter(user =>
    (user.name && user.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (user.email && user.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (user.role && user.role.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const getRoleIcon = (role) => {
    switch (role) {
      case 'admin':
        return <FaUserShield className="text-red-500" />;
      case 'editor':
        return <FaUserEdit className="text-blue-500" />;
      case 'reviewer':
        return <FaUserCheck className="text-green-500" />;
      default:
        return <FaUserEdit className="text-gray-500" />;
    }
  };

  return (
    <div className="p-6">
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">User Management</h1>
            <p className="text-gray-600 mt-2">Manage all system users and their permissions</p>
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleOpenCreate}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <FaUserPlus className="mr-2" />
            Create User
          </motion.button>
        </div>
      </motion.div>

      {/* Search Bar */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.1 }}
        className="mb-6"
      >
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search users by name, email or role..."
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
      </motion.div>

      {/* Users Table */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="bg-white rounded-xl shadow-md overflow-hidden"
      >
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {isLoading ? (
                <tr><td colSpan={5} className="text-center py-8">Loading...</td></tr>
              ) : filteredUsers.length > 0 ? (
                filteredUsers.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                          {getRoleIcon(user.role)}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{user.name}</div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        {user.affiliation && (
                          <span className="inline-flex items-center text-xs text-gray-500">
                            <FaUniversity className="mr-1" /> {user.affiliation}
                          </span>
                        )}
                        {user.country && (
                          <span className="inline-flex items-center text-xs text-gray-500">
                            <FaGlobe className="mr-1" /> {user.country}
                          </span>
                        )}
                      </div>
                      {user.expertise?.length > 0 && (
                        <div className="mt-1 flex flex-wrap gap-1">
                          {user.expertise.map((exp, index) => (
                            <span key={index} className="px-2 py-1 text-xs rounded-full bg-blue-50 text-blue-700">
                              {exp}
                            </span>
                          ))}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        user.role === 'admin' ? 'bg-red-100 text-red-800' :
                        user.role === 'editor' ? 'bg-blue-100 text-blue-800' :
                        user.role === 'reviewer' ? 'bg-green-100 text-green-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {user.role?.charAt(0).toUpperCase() + user.role?.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        user.email_verified_at ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {user.email_verified_at ? 'Verified' : 'Pending Verification'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button className="text-blue-600 hover:text-blue-900 mr-4" onClick={() => handleOpenEdit(user)}>
                        <FaEdit />
                      </button>
                      <button 
                        onClick={() => handleDeleteUser(user.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <FaTrash />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr><td colSpan={5} className="text-center py-8">No users found.</td></tr>
              )}
            </tbody>
          </table>
        </div>
        {/* Pagination Controls */}
        {meta && meta.last_page > 1 && (
          <div className="flex justify-center items-center gap-2 py-4">
            <button
              className="px-3 py-1 rounded border bg-gray-100 hover:bg-gray-200"
              onClick={() => setPage(page - 1)}
              disabled={page <= 1}
            >
              Previous
            </button>
            {Array.from({ length: meta.last_page }, (_, i) => i + 1).map((p) => (
              <button
                key={p}
                className={`px-3 py-1 rounded border ${p === page ? 'bg-blue-600 text-white' : 'bg-gray-100 hover:bg-gray-200'}`}
                onClick={() => setPage(p)}
              >
                {p}
              </button>
            ))}
            <button
              className="px-3 py-1 rounded border bg-gray-100 hover:bg-gray-200"
              onClick={() => setPage(page + 1)}
              disabled={page >= meta.last_page}
            >
              Next
            </button>
          </div>
        )}
      </motion.div>

      {/* Create/Edit User Modal */}
      {(isCreateModalOpen || isEditModalOpen) && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="bg-white rounded-lg shadow-lg p-8 w-full max-w-lg relative">
            <button className="absolute top-2 right-2 text-gray-400 hover:text-gray-600" onClick={() => { setIsCreateModalOpen(false); setIsEditModalOpen(false); setError(null); }}>&times;</button>
            <h2 className="text-xl font-bold mb-4">{isCreateModalOpen ? 'Create User' : 'Edit User'}</h2>
            {error && <div className="mb-2 text-red-600 text-sm">{error}</div>}
            <form onSubmit={e => { e.preventDefault(); isCreateModalOpen ? handleCreateUser() : handleEditUser(); }}>
              <div className="mb-3">
                <label className="block text-sm font-medium text-gray-700">Name</label>
                <input type="text" name="name" value={formUser.name} onChange={handleChange} required className="w-full border rounded px-3 py-2" />
              </div>
              <div className="mb-3">
                <label className="block text-sm font-medium text-gray-700">Email</label>
                <input type="email" name="email" value={formUser.email} onChange={handleChange} required className="w-full border rounded px-3 py-2" />
              </div>
              <div className="mb-3">
                <label className="block text-sm font-medium text-gray-700">Role</label>
                <select name="role" value={formUser.role} onChange={handleChange} className="w-full border rounded px-3 py-2">
                  <option value="author">Author</option>
                  <option value="reviewer">Reviewer</option>
                  <option value="editor">Editor</option>
                  <option value="admin">Admin</option>
                </select>
              </div>
              <div className="mb-3">
                <label className="block text-sm font-medium text-gray-700">Affiliation</label>
                <input type="text" name="affiliation" value={formUser.affiliation} onChange={handleChange} className="w-full border rounded px-3 py-2" />
              </div>
              <div className="mb-3">
                <label className="block text-sm font-medium text-gray-700">Country</label>
                <input type="text" name="country" value={formUser.country} onChange={handleChange} className="w-full border rounded px-3 py-2" />
              </div>
              <div className="mb-3">
                <label className="block text-sm font-medium text-gray-700">Expertise (comma separated)</label>
                <input type="text" name="expertise" value={formUser.expertise.join(', ')} onChange={handleExpertiseChange} className="w-full border rounded px-3 py-2" />
              </div>
              <div className="mb-3">
                <label className="block text-sm font-medium text-gray-700">Preferred Language</label>
                <select name="preferred_language" value={formUser.preferred_language} onChange={handleChange} className="w-full border rounded px-3 py-2">
                  <option value="en">English</option>
                  <option value="fr">French</option>
                </select>
              </div>
              <div className="mb-3 flex items-center">
                <label className="block text-sm font-medium text-gray-700 mr-2">Password{isCreateModalOpen && <span className="text-red-500 ml-1">*</span>}</label>
                <input type="password" name="password" value={formUser.password} onChange={handleChange} className="w-full border rounded px-3 py-2" placeholder={isEditModalOpen ? "Leave blank to keep current password" : "Enter password"} required={isCreateModalOpen} />
                <button type="button" className="ml-2 p-2 bg-gray-100 rounded hover:bg-gray-200" title="Auto-generate password" onClick={handleGeneratePassword}><FaRandom /></button>
              </div>
              <div className="flex justify-end mt-6">
                <button type="button" className="mr-3 px-4 py-2 rounded bg-gray-200 hover:bg-gray-300" onClick={() => { setIsCreateModalOpen(false); setIsEditModalOpen(false); setError(null); }}>Cancel</button>
                <button type="submit" className="px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700" disabled={isLoading}>{isCreateModalOpen ? 'Create' : 'Update'}</button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagement;