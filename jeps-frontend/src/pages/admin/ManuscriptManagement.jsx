import React, { useState, useEffect } from 'react';
import { 
  FaFileAlt, 
  FaEye, 
  FaTrash, 
  FaSearch, 
  FaFilter,
  FaDownload,
  FaEdit,
  FaCheck,
  FaTimes,
  FaClock,
  FaUser,
  FaCalendar,
  FaChartBar,
  FaSpinner,
  FaTimesCircle,
  FaIdBadge,
  FaTags,
  FaBookOpen,
  FaUserTie,
  FaEnvelope,
  FaInfoCircle,
  FaSave
} from 'react-icons/fa';
import { toast } from 'react-toastify';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import adminService from '../../services/adminService';

const ManuscriptManagement = () => {
  const [manuscripts, setManuscripts] = useState([]);
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  
  // Modal states
  const [showViewModal, setShowViewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedManuscript, setSelectedManuscript] = useState(null);
  const [editingStatus, setEditingStatus] = useState('');
  const [downloading, setDownloading] = useState(null);

  useEffect(() => {
    fetchManuscripts();
    fetchManuscriptStats();
  }, []);

  const fetchManuscripts = async () => {
    setLoading(true);
    try {
      const response = await adminService.getManuscripts();
      setManuscripts(response.data || []);
    } catch (error) {
      console.error('Error fetching manuscripts:', error);
      toast.error('Failed to fetch manuscripts');
      setManuscripts([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchManuscriptStats = async () => {
    try {
      const response = await adminService.getManuscriptStats();
      setStats(response.data || {
        total: 0,
        submitted: 0,
        under_review: 0,
        accepted: 0,
        published: 0,
        rejected: 0
      });
    } catch (error) {
      console.error('Error fetching manuscript stats:', error);
      toast.error('Failed to fetch manuscript statistics');
      setStats({
        total: 0,
        submitted: 0,
        under_review: 0,
        accepted: 0,
        published: 0,
        rejected: 0
      });
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'submitted': return <FaClock className="text-yellow-500" />;
      case 'under_review': return <FaEye className="text-blue-500" />;
      case 'accepted': return <FaCheck className="text-green-500" />;
      case 'published': return <FaFileAlt className="text-purple-500" />;
      case 'rejected': return <FaTimes className="text-red-500" />;
      default: return <FaFileAlt className="text-gray-500" />;
    }
  };

  const getStatusBadge = (status) => {
    const baseClasses = "px-2 py-1 rounded-full text-xs font-medium";
    switch (status) {
      case 'submitted':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case 'under_review':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'accepted':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'published':
        return `${baseClasses} bg-purple-100 text-purple-800`;
      case 'rejected':
        return `${baseClasses} bg-red-100 text-red-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const filteredManuscripts = manuscripts.filter(manuscript => {
    const matchesSearch = manuscript.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         manuscript.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         manuscript.submission_id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || manuscript.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleDeleteManuscript = async (id) => {
    if (window.confirm('Are you sure you want to delete this manuscript? This action cannot be undone.')) {
      try {
        await adminService.deleteManuscript(id);
        setManuscripts(manuscripts.filter(m => m.id !== id));
        toast.success('Manuscript deleted successfully');
        fetchManuscriptStats(); // Refresh stats after deletion
      } catch (error) {
        console.error('Error deleting manuscript:', error);
        toast.error('Failed to delete manuscript');
      }
    }
  };

  // New action handlers
  const handleViewManuscript = (manuscript) => {
    setSelectedManuscript(manuscript);
    setShowViewModal(true);
  };

  const handleEditManuscript = (manuscript) => {
    setSelectedManuscript(manuscript);
    setEditingStatus(manuscript.status);
    setShowEditModal(true);
  };

  const handleUpdateStatus = async () => {
    if (!selectedManuscript || !editingStatus) return;
    
    try {
      await adminService.updateManuscriptStatus(selectedManuscript.id, editingStatus);
      
      // Update local state
      setManuscripts(manuscripts.map(m => 
        m.id === selectedManuscript.id 
          ? { ...m, status: editingStatus }
          : m
      ));
      
      setShowEditModal(false);
      toast.success('Manuscript status updated successfully');
      fetchManuscriptStats();
    } catch (error) {
      console.error('Error updating manuscript status:', error);
      toast.error('Failed to update manuscript status');
    }
  };

  const handleDownloadManuscript = async (manuscript) => {
    setDownloading(manuscript.id);
    try {
      const blob = await adminService.downloadManuscript(manuscript.id);
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${manuscript.submission_id}_${manuscript.title.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      toast.success('Manuscript downloaded successfully');
    } catch (error) {
      console.error('Error downloading manuscript:', error);
      toast.error('Failed to download manuscript. File may not be available.');
    } finally {
      setDownloading(null);
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Manuscript Management</h1>
        <p className="text-gray-600">Monitor and manage all manuscript submissions</p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-md border-l-4 border-blue-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total || 0}</p>
            </div>
            <FaChartBar className="text-blue-500 text-2xl" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md border-l-4 border-yellow-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Submitted</p>
              <p className="text-2xl font-bold text-gray-900">{stats.submitted || 0}</p>
            </div>
            <FaClock className="text-yellow-500 text-2xl" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md border-l-4 border-blue-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Under Review</p>
              <p className="text-2xl font-bold text-gray-900">{stats.under_review || 0}</p>
            </div>
            <FaEye className="text-blue-500 text-2xl" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md border-l-4 border-green-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Accepted</p>
              <p className="text-2xl font-bold text-gray-900">{stats.accepted || 0}</p>
            </div>
            <FaCheck className="text-green-500 text-2xl" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md border-l-4 border-purple-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Published</p>
              <p className="text-2xl font-bold text-gray-900">{stats.published || 0}</p>
            </div>
            <FaFileAlt className="text-purple-500 text-2xl" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md border-l-4 border-red-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Rejected</p>
              <p className="text-2xl font-bold text-gray-900">{stats.rejected || 0}</p>
            </div>
            <FaTimes className="text-red-500 text-2xl" />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search manuscripts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="flex gap-4">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="submitted">Submitted</option>
              <option value="under_review">Under Review</option>
              <option value="accepted">Accepted</option>
              <option value="published">Published</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
        </div>
      </div>

      {/* Manuscripts Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Manuscript
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Author
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Reviewers
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Submitted
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredManuscripts.map((manuscript) => (
                <tr
                  key={manuscript.id}
                  className="bg-white divide-y divide-gray-200"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getStatusIcon(manuscript.status)}
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {manuscript.submission_id}
                        </div>
                        <div className="text-sm text-gray-500 max-w-xs truncate">
                          {manuscript.title}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <FaUser className="text-gray-400 mr-2" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {manuscript.author}
                        </div>
                        <div className="text-sm text-gray-500">
                          {manuscript.author_email}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={getStatusBadge(manuscript.status)}>
                      {manuscript.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {manuscript.reviewers_count} reviewers
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center text-sm text-gray-500">
                      <FaCalendar className="mr-2" />
                      {new Date(manuscript.submitted_at).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleViewManuscript(manuscript)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50 transition-colors"
                        title="View Details"
                      >
                        <FaEye />
                      </button>
                      <button
                        onClick={() => handleEditManuscript(manuscript)}
                        className="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50 transition-colors"
                        title="Edit Status"
                      >
                        <FaEdit />
                      </button>
                      <button
                        onClick={() => handleDownloadManuscript(manuscript)}
                        disabled={downloading === manuscript.id}
                        className="text-gray-600 hover:text-gray-900 p-1 rounded hover:bg-gray-50 transition-colors disabled:opacity-50"
                        title="Download"
                      >
                        {downloading === manuscript.id ? <FaSpinner className="animate-spin" /> : <FaDownload />}
                      </button>
                      <button
                        onClick={() => handleDeleteManuscript(manuscript.id)}
                        className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50 transition-colors"
                        title="Delete"
                      >
                        <FaTrash />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {filteredManuscripts.length === 0 && (
        <div className="text-center py-12">
          <FaFileAlt className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No manuscripts found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm || statusFilter !== 'all' 
              ? 'Try adjusting your search or filter criteria.' 
              : 'No manuscripts have been submitted yet.'}
          </p>
        </div>
      )}

      {/* View Manuscript Modal */}
      {showViewModal && selectedManuscript && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold text-gray-900">Manuscript Details</h3>
              <button
                onClick={() => setShowViewModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <FaTimesCircle size={24} />
              </button>
            </div>
            
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <FaIdBadge className="inline mr-2" />
                    Submission ID
                  </label>
                  <p className="text-sm text-gray-900">{selectedManuscript.submission_id}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <FaTags className="inline mr-2" />
                    Status
                  </label>
                  <span className={getStatusBadge(selectedManuscript.status)}>
                    {selectedManuscript.status.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FaBookOpen className="inline mr-2" />
                  Title
                </label>
                <p className="text-sm text-gray-900">{selectedManuscript.title}</p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <FaUserTie className="inline mr-2" />
                    Author
                  </label>
                  <p className="text-sm text-gray-900">{selectedManuscript.author}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <FaEnvelope className="inline mr-2" />
                    Author Email
                  </label>
                  <p className="text-sm text-gray-900">{selectedManuscript.author_email}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <FaCalendar className="inline mr-2" />
                    Submitted Date
                  </label>
                  <p className="text-sm text-gray-900">
                    {new Date(selectedManuscript.submitted_at).toLocaleDateString()}
                  </p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <FaInfoCircle className="inline mr-2" />
                    Reviewers Count
                  </label>
                  <p className="text-sm text-gray-900">{selectedManuscript.reviewers_count} reviewers</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Status Modal */}
      {showEditModal && selectedManuscript && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold text-gray-900">Edit Manuscript Status</h3>
              <button
                onClick={() => setShowEditModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <FaTimesCircle size={24} />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Manuscript: {selectedManuscript.title}
                </label>
                <p className="text-xs text-gray-500">ID: {selectedManuscript.submission_id}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  value={editingStatus}
                  onChange={(e) => setEditingStatus(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="submitted">Submitted</option>
                  <option value="under_review">Under Review</option>
                  <option value="accepted">Accepted</option>
                  <option value="published">Published</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>
              
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  onClick={() => setShowEditModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleUpdateStatus}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                >
                  <FaSave className="mr-2" />
                  Update Status
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ManuscriptManagement; 