import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { 
  FaUser, 
  FaEnvelope, 
  FaLock, 
  FaUniversity, 
  FaGlobe, 
  FaTags, 
  FaUserGraduate,
  FaBookOpen
} from 'react-icons/fa';
import authService from '../../../services/authService';
import LoadingSpinner from '../../../components/common/LoadingSpinner';

const RegisterPage = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    role: 'author',
    affiliation: '',
    country: '',
    expertise: []
  });
  const [expertiseInput, setExpertiseInput] = useState('');

  const totalSteps = 3;

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleExpertiseChange = (e) => {
    setExpertiseInput(e.target.value);
  };

  const addExpertise = () => {
    if (expertiseInput.trim() && !formData.expertise.includes(expertiseInput.trim())) {
      setFormData({
        ...formData,
        expertise: [...formData.expertise, expertiseInput.trim()]
      });
      setExpertiseInput('');
    }
  };

  const removeExpertise = (index) => {
    setFormData({
      ...formData,
      expertise: formData.expertise.filter((_, i) => i !== index)
    });
  };

  const getInputClassName = (fieldName) => {
    return `w-full pl-10 pr-3 py-2 border ${errors[fieldName] ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent`;
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const validateStep = (step) => {
    const stepErrors = {};
    
    if (step === 1) {
      if (!formData.name.trim()) stepErrors.name = ['Name is required'];
      if (!formData.email.trim()) stepErrors.email = ['Email is required'];
      if (!formData.password) stepErrors.password = ['Password is required'];
      if (formData.password !== formData.password_confirmation) {
        stepErrors.password_confirmation = ['Passwords do not match'];
      }
    } else if (step === 2) {
      if (!formData.affiliation.trim()) stepErrors.affiliation = ['Affiliation is required'];
      if (!formData.country.trim()) stepErrors.country = ['Country is required'];
    }
    
    setErrors(stepErrors);
    return Object.keys(stepErrors).length === 0;
  };

  const handleNext = (e) => {
    e.preventDefault();
    if (validateStep(currentStep)) {
      nextStep();
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Only submit if we're on the final step
    if (currentStep !== totalSteps) {
      return;
    }

    if (!validateStep(currentStep)) return;

    setLoading(true);
    setErrors({});

    try {
      const response = await authService.register(formData);
      if (response.success) {
        toast.success(response.message || 'Registration successful! Please check your email for verification.');
        navigate('/login', { state: { message: response.message || 'Registration successful! Please check your email for verification.' } });
      } else {
        toast.error(response.message || 'Registration failed. Please try again.');
        setErrors(response.errors || {});
      }
    } catch (error) {
      console.error('Registration error:', error);
      if (error.response?.data?.errors) {
        setErrors(error.response.data.errors);
      } else {
        setErrors({ general: [error.response?.data?.message || 'Registration failed. Please try again.'] });
      }
      toast.error(error.response?.data?.message || 'Registration failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Step 1: Basic Information
  const renderStep1 = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="name">
          Full Name *
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaUser className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            id="name"
            name="name"
            required
            className={getInputClassName('name')}
            placeholder="Enter your full name"
            value={formData.name}
            onChange={handleChange}
          />
        </div>
        {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name[0]}</p>}
      </div>

      <div>
        <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="email">
          Email Address *
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaEnvelope className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="email"
            id="email"
            name="email"
            required
            className={getInputClassName('email')}
            placeholder="Enter your email address"
            value={formData.email}
            onChange={handleChange}
          />
        </div>
        {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email[0]}</p>}
      </div>

      <div>
        <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="password">
          Password *
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaLock className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="password"
            id="password"
            name="password"
            required
            className={getInputClassName('password')}
            placeholder="Create a password"
            value={formData.password}
            onChange={handleChange}
          />
        </div>
        {errors.password && <p className="mt-1 text-sm text-red-600">{errors.password[0]}</p>}
      </div>

      <div>
        <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="password_confirmation">
          Confirm Password *
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaLock className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="password"
            id="password_confirmation"
            name="password_confirmation"
            required
            className={getInputClassName('password_confirmation')}
            placeholder="Confirm your password"
            value={formData.password_confirmation}
            onChange={handleChange}
          />
        </div>
        {errors.password_confirmation && <p className="mt-1 text-sm text-red-600">{errors.password_confirmation[0]}</p>}
      </div>
    </div>
  );

  // Step 2: Professional Information
  const renderStep2 = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="affiliation">
          Affiliation *
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaUniversity className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            id="affiliation"
            name="affiliation"
            required
            className={getInputClassName('affiliation')}
            placeholder="Your institution or organization"
            value={formData.affiliation}
            onChange={handleChange}
          />
        </div>
        {errors.affiliation && <p className="mt-1 text-sm text-red-600">{errors.affiliation[0]}</p>}
      </div>

      <div>
        <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="country">
          Country *
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaGlobe className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            id="country"
            name="country"
            required
            className={getInputClassName('country')}
            placeholder="Your country"
            value={formData.country}
            onChange={handleChange}
          />
        </div>
        {errors.country && <p className="mt-1 text-sm text-red-600">{errors.country[0]}</p>}
      </div>

      <div>
        <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="role">
          Role
        </label>
        <select
          id="role"
          name="role"
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          value={formData.role}
          onChange={handleChange}
        >
          <option value="author">Author</option>
          <option value="reviewer">Reviewer</option>
        </select>
      </div>
    </div>
  );

  // Step 3: Expertise (Optional)
  const renderStep3 = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-gray-700 text-sm font-medium mb-2">
          Areas of Expertise (Optional)
        </label>
        <div className="flex space-x-2">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FaTags className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Add an area of expertise"
              value={expertiseInput}
              onChange={handleExpertiseChange}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addExpertise();
                }
              }}
            />
          </div>
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              addExpertise();
            }}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Add
          </button>
        </div>

        {formData.expertise.length > 0 && (
          <div className="mt-3 flex flex-wrap gap-2">
            {formData.expertise.map((item, index) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
              >
                {item}
                <button
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    removeExpertise(index);
                  }}
                  className="ml-2 text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
        )}
      </div>

      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <h4 className="text-green-800 font-medium mb-2">Almost Done!</h4>
        <p className="text-green-700 text-sm">
          You're about to complete your registration. Click "Register" to create your JEMS account.
        </p>
      </div>
    </div>
  );

  const floatingIcons = Array(15).fill(0).map((_, i) => {
    const icons = [
      <FaBookOpen className="text-blue-300 opacity-25" size={24} />,
      <FaUserGraduate className="text-green-300 opacity-25" size={28} />,
      <FaUser className="text-orange-300 opacity-25" size={22} />,
      <FaUniversity className="text-yellow-300 opacity-25" size={26} />,
      <FaGlobe className="text-blue-200 opacity-25" size={20} />,
      <FaTags className="text-green-200 opacity-25" size={18} />
    ];
    return {
      id: i,
      icon: icons[i % icons.length],
      x: Math.random() * 100,
      y: Math.random() * 100,
      delay: Math.random() * 5,
      duration: 5 + Math.random() * 10
    };
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-green-700 to-orange-600 relative overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <img
          className="w-full h-full object-cover opacity-50"
          src="/assets/uba.jpeg"
          alt="University of Bamenda"
        />
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/80 via-green-600/60 to-orange-500/70 mix-blend-multiply"></div>
      </div>

      {/* Decorative wave pattern */}
      <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-r from-green-500 via-yellow-400 to-orange-400 opacity-80"></div>

      {/* Simple decorative elements */}
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-32 right-20 w-40 h-40 bg-yellow-400 opacity-10 rounded-full blur-2xl"></div>
        <div className="absolute bottom-32 left-20 w-36 h-36 bg-blue-400 opacity-10 rounded-full blur-2xl"></div>
        <div className="absolute top-1/3 left-1/3 w-28 h-28 bg-green-400 opacity-10 rounded-full blur-xl"></div>
      </div>

      {/* Floating animated icons in background */}
      {floatingIcons.map((icon) => (
        <motion.div
          key={icon.id}
          className="absolute z-10"
          style={{ left: `${icon.x}%`, top: `${icon.y}%` }}
          animate={{
            y: [0, -20, 0],
            x: [0, 10, 0],
            rotate: [0, 5, 0]
          }}
          transition={{
            duration: icon.duration,
            repeat: Infinity,
            repeatType: "reverse",
            delay: icon.delay
          }}
        >
          {icon.icon}
        </motion.div>
      ))}

      <div className="container mx-auto px-4 py-12 relative z-20">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-xl mx-auto bg-white rounded-xl shadow-2xl border border-orange-200 overflow-hidden"
        >
          <div className="bg-gradient-to-r from-blue-900 to-blue-700 py-6 px-8">
            <div className="flex items-center space-x-3">
              <FaUserGraduate className="text-orange-300" size={24} />
              <h2 className="text-xl font-semibold text-white">JEMS Author Registration</h2>
            </div>
          </div>

          <div className="p-8">
            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Step {currentStep} of {totalSteps}</span>
                <span className="text-sm text-gray-500">
                  {currentStep === 1 && "Basic Information"}
                  {currentStep === 2 && "Professional Details"}
                  {currentStep === 3 && "Expertise & Finish"}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-green-500 via-yellow-400 to-orange-400 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(currentStep / totalSteps) * 100}%` }}
                ></div>
              </div>
            </div>

            {/* Show general error message if exists */}
            {errors.general && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600">{errors.general[0]}</p>
              </div>
            )}

            <form onSubmit={handleSubmit}>
              {/* Step Content */}
              <div className="mb-6">
                {currentStep === 1 && renderStep1()}
                {currentStep === 2 && renderStep2()}
                {currentStep === 3 && renderStep3()}
              </div>

              {/* Navigation Buttons */}
              <div className="flex justify-between space-x-4">
                {currentStep > 1 && (
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    type="button"
                    onClick={(e) => {
                      e.preventDefault();
                      prevStep();
                    }}
                    className="flex-1 border-2 border-blue-900 text-blue-900 bg-white hover:bg-blue-900 hover:text-white font-medium py-3 px-4 rounded-lg shadow-md transition-all duration-300"
                  >
                    Previous
                  </motion.button>
                )}

                {currentStep < totalSteps ? (
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    type="button"
                    onClick={handleNext}
                    className={`${currentStep === 1 ? 'w-full' : 'flex-1'} bg-blue-600 hover:bg-green-600 text-white font-medium py-3 px-4 rounded-lg shadow-md transition-all duration-300`}
                  >
                    Next
                  </motion.button>
                ) : (
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    type="submit"
                    disabled={loading}
                    className="flex-1 bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white font-medium py-3 px-4 rounded-lg shadow-md transition-all duration-300 flex justify-center items-center"
                  >
                    {loading ? <LoadingSpinner size="sm" className="text-white" /> : 'Register'}
                  </motion.button>
                )}
              </div>
            </form>

            <div className="mt-8 text-center text-sm text-gray-600">
              Already have an account?{" "}
              <Link to="/login" className="font-medium text-blue-600 hover:text-blue-500 hover:underline">
                Login here
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default RegisterPage;
