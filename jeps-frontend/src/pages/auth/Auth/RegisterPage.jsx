import { Link, useNavigate } from "react-router-dom";
import { FaUser<PERSON>raduate, FaLock, FaBookOpen, FaUser, FaEnvelope, FaUniversity, FaGlobe, FaTags } from "react-icons/fa";
import { useState } from "react";
import { toast } from 'react-toastify';
import { motion } from "framer-motion";
import authService from "../../../services/authService";
import LoadingSpinner from "../../../components/common/LoadingSpinner";

const RegisterPage = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    role: 'author',
    affiliation: '',
    country: '',
    expertise: []
  });
  const [expertiseInput, setExpertiseInput] = useState('');

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    // Clear error for the field being changed
    if (errors[e.target.name]) {
      setErrors(prev => ({
        ...prev,
        [e.target.name]: null
      }));
    }
  };

  const handleExpertiseChange = (e) => {
    setExpertiseInput(e.target.value);
  };

  const addExpertise = () => {
    if (expertiseInput.trim() && !formData.expertise.includes(expertiseInput.trim())) {
      setFormData({
        ...formData,
        expertise: [...formData.expertise, expertiseInput.trim()]
      });
      setExpertiseInput('');
    }
  };

  const removeExpertise = (index) => {
    setFormData({
      ...formData,
      expertise: formData.expertise.filter((_, i) => i !== index)
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setErrors({});

    try {
      const response = await authService.register(formData);
      if (response.success) {
        toast.success(response.message || 'Registration successful! Please check your email for verification.');
        navigate('/login', { state: { message: response.message || 'Registration successful! Please check your email for verification.' } });
      } else {
        toast.error(response.message || 'Registration failed. Please try again.');
      }
    } catch (err) {
      const responseData = err.response?.data;
      console.log('Error response:', responseData); // Debug log
      
      if (responseData?.errors && Object.keys(responseData.errors).length > 0) {
        // Handle validation errors
        setErrors(responseData.errors);
        Object.values(responseData.errors).forEach(messages => {
          messages.forEach(message => {
            toast.error(message);
          });
        });
      } else {
        // Handle general errors
        const errorMessage = responseData?.message || 'An unexpected error occurred. Please try again.';
        toast.error(errorMessage);
        // Also show the error in the UI
        setErrors({ general: [errorMessage] });
      }
    } finally {
      setLoading(false);
    }
  };

  const getInputClassName = (fieldName) => {
    return `w-full pl-10 pr-3 py-2 border ${errors[fieldName] ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent`;
  };

  const floatingIcons = Array(18).fill(0).map((_, i) => {
    const icons = [
      <FaBookOpen className="text-blue-300 opacity-25" size={24} />,
      <FaUserGraduate className="text-green-300 opacity-25" size={28} />,
      <FaUser className="text-orange-300 opacity-25" size={22} />,
      <FaUniversity className="text-yellow-300 opacity-25" size={26} />,
      <FaGlobe className="text-blue-200 opacity-25" size={20} />,
      <FaTags className="text-green-200 opacity-25" size={18} />
    ];
    return {
      id: i,
      icon: icons[i % icons.length],
      x: Math.random() * 100,
      y: Math.random() * 100,
      delay: Math.random() * 5,
      duration: 5 + Math.random() * 10
    };
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-700 via-blue-600 to-purple-600 relative overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <img
          className="w-full h-full object-cover opacity-15"
          src="/assets/uba.jpeg"
          alt="University of Bamenda"
        />
        <div className="absolute inset-0 bg-gradient-to-br from-green-700/85 via-blue-600/70 to-purple-600/80"></div>
      </div>

      {/* Decorative shapes */}
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-32 right-20 w-40 h-40 bg-yellow-400 opacity-10 rounded-full blur-2xl"></div>
        <div className="absolute bottom-32 left-20 w-36 h-36 bg-orange-400 opacity-10 rounded-full blur-2xl"></div>
        <div className="absolute top-1/3 left-1/3 w-28 h-28 bg-green-400 opacity-10 rounded-full blur-xl"></div>
        <div className="absolute bottom-1/4 right-1/3 w-32 h-32 bg-blue-400 opacity-10 rounded-full blur-xl"></div>
      </div>

      {/* Floating animated icons in background */}
      {floatingIcons.map((icon) => (
        <motion.div
          key={icon.id}
          className="absolute z-10"
          style={{ left: `${icon.x}%`, top: `${icon.y}%` }}
          animate={{
            y: [0, -20, 0],
            x: [0, 10, 0],
            rotate: [0, 5, 0]
          }}
          transition={{
            duration: icon.duration,
            repeat: Infinity,
            repeatType: "reverse",
            delay: icon.delay
          }}
        >
          {icon.icon}
        </motion.div>
      ))}

      <div className="container mx-auto px-4 py-12 relative z-10">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-xl mx-auto bg-white rounded-xl shadow-xl border border-orange-200 overflow-hidden"
        >
          <div className="bg-gradient-to-r from-blue-900 to-blue-700 py-6 px-8">
            <div className="flex items-center space-x-3">
              <FaUserGraduate className="text-white" size={24} />
              <h2 className="text-xl font-semibold text-white">JEMS Author Registration</h2>
            </div>
          </div>
          
          <div className="p-8">
            {/* Show general error message if exists */}
            {errors.general && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600">{errors.general[0]}</p>
              </div>
            )}

            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="name">
                  Full Name *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaUser className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    className={getInputClassName('name')}
                    placeholder="John Doe"
                    value={formData.name}
                    onChange={handleChange}
                  />
                </div>
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name[0]}</p>
                )}
              </div>

              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="email">
                  Email Address *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaEnvelope className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    className={getInputClassName('email')}
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={handleChange}
                  />
                </div>
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email[0]}</p>
                )}
              </div>

              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="role">
                  Role *
                </label>
                <select
                  id="role"
                  name="role"
                  required
                  className={getInputClassName('role')}
                  value={formData.role}
                  onChange={handleChange}
                >
                  <option value="author">Author</option>
                  <option value="reviewer">Reviewer</option>
                </select>
                {errors.role && (
                  <p className="mt-1 text-sm text-red-600">{errors.role[0]}</p>
                )}
              </div>
              
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="password">
                  Password *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaLock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    required
                    className={getInputClassName('password')}
                    placeholder="••••••••"
                    value={formData.password}
                    onChange={handleChange}
                  />
                </div>
                {errors.password && (
                  <p className="mt-1 text-sm text-red-600">{errors.password[0]}</p>
                )}
              </div>

              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="password_confirmation">
                  Confirm Password *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaLock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="password"
                    id="password_confirmation"
                    name="password_confirmation"
                    required
                    className={getInputClassName('password_confirmation')}
                    placeholder="••••••••"
                    value={formData.password_confirmation}
                    onChange={handleChange}
                  />
                </div>
                {errors.password_confirmation && (
                  <p className="mt-1 text-sm text-red-600">{errors.password_confirmation[0]}</p>
                )}
              </div>

              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="affiliation">
                  Affiliation
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaUniversity className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    id="affiliation"
                    name="affiliation"
                    className={getInputClassName('affiliation')}
                    placeholder="University of Example"
                    value={formData.affiliation}
                    onChange={handleChange}
                  />
                </div>
                {errors.affiliation && (
                  <p className="mt-1 text-sm text-red-600">{errors.affiliation[0]}</p>
                )}
              </div>

              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="country">
                  Country
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaGlobe className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    id="country"
                    name="country"
                    className={getInputClassName('country')}
                    placeholder="Your Country"
                    value={formData.country}
                    onChange={handleChange}
                  />
                </div>
                {errors.country && (
                  <p className="mt-1 text-sm text-red-600">{errors.country[0]}</p>
                )}
              </div>

              <div className="mb-6">
                <label className="block text-gray-700 text-sm font-medium mb-2">
                  Areas of Expertise
                </label>
                <div className="flex gap-2 mb-2">
                  <div className="relative flex-grow">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FaTags className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      value={expertiseInput}
                      onChange={handleExpertiseChange}
                      className={getInputClassName('expertise')}
                      placeholder="Add expertise"
                    />
                  </div>
                  <button
                    type="button"
                    onClick={addExpertise}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    Add
                  </button>
                </div>
                {errors.expertise && (
                  <p className="mt-1 text-sm text-red-600">{errors.expertise[0]}</p>
                )}
                <div className="flex flex-wrap gap-2">
                  {formData.expertise.map((exp, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                    >
                      {exp}
                      <button
                        type="button"
                        onClick={() => removeExpertise(index)}
                        className="ml-2 text-blue-600 hover:text-blue-800"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              </div>
              
              <div className="flex items-center mb-6">
                <input
                  id="terms"
                  name="terms"
                  type="checkbox"
                  required
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="terms" className="ml-2 block text-sm text-gray-700">
                  I agree to the <Link to="/terms" className="text-blue-600 hover:underline">Terms and Conditions</Link>
                </label>
              </div>
              
              <div className="mt-6 grid grid-cols-2 gap-3">
                <motion.button
                  whileHover={{ y: -2 }}
                  type="button"
                  className="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                >
                  <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                    <path
                      fill="#4285F4"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="#34A853"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="#FBBC05"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="#EA4335"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                  Google
                </motion.button>
                
                <motion.button
                  whileHover={{ y: -2 }}
                  type="button"
                  className="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                >
                  <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                    <path
                      fill="#A6CE39"
                      d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2zm0 3c-3.866 0-7 3.134-7 7s3.134 7 7 7 7-3.134 7-7-3.134-7-7-7zm0 2c2.761 0 5 2.239 5 5s-2.239 5-5 5-5-2.239-5-5 2.239-5 5-5z"
                    />
                  </svg>
                  ORCID
                </motion.button>
              </div>

              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="submit"
                disabled={loading}
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white mt-5 font-medium py-3 px-4 rounded-lg shadow-md transition-all duration-300 flex justify-center items-center"
              >
                {loading ? <LoadingSpinner size="sm" className="text-white" /> : 'Register'}
              </motion.button>
            </form>
            
            <div className="mt-8 text-center text-sm text-gray-600">
              Already have an account?{" "}
              <Link to="/login" className="font-medium text-blue-600 hover:text-blue-500 hover:underline">
                Login here
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default RegisterPage;