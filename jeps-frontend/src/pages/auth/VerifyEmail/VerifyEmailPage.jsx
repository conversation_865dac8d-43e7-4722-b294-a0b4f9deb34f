import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { FaCheckCircle, FaTimesCircle } from 'react-icons/fa';
import authService from '../../../services/authService';
import LoadingSpinner from '../../../components/common/LoadingSpinner';

const VerifyEmailPage = () => {
  console.log('VerifyEmailPage component rendered');
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    console.log('VerifyEmailPage useEffect triggered');
    console.log('Search params:', Object.fromEntries(searchParams.entries()));
    
    const verifyEmail = async () => {
      console.log('Verify Email Page Mounted');
      try {
        const id = searchParams.get('id');
        const hash = searchParams.get('hash');
        const expires = searchParams.get('expires');
        const signature = searchParams.get('signature');

        console.log('Verification parameters:', { id, hash, expires, signature });

        if (!id || !hash || !expires || !signature) {
          console.log('Missing verification parameters');
          setError('Invalid verification link');
          setLoading(false);
          return;
        }

        console.log('Making verification request...');
        const response = await authService.verifyEmail({
          id,
          hash,
          expires,
          signature,
        });

        console.log('Verification response:', response);

        if (response.success) {
          setSuccess(true);
          setTimeout(() => {
            navigate('/login', {
              state: { message: 'Email verified successfully. Please login.' },
            });
          }, 3000);
        }
      } catch (err) {
        console.error('Verification error:', err);
        setError(err.response?.data?.message || 'Failed to verify email');
      } finally {
        setLoading(false);
      }
    };

    verifyEmail();
  }, [searchParams, navigate]);

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            {loading ? (
              <div className="flex flex-col items-center">
                <LoadingSpinner size="lg" />
                <h2 className="mt-4 text-xl font-semibold text-gray-900">
                  Verifying your email...
                </h2>
              </div>
            ) : success ? (
              <div className="flex flex-col items-center">
                <FaCheckCircle className="h-12 w-12 text-green-500" />
                <h2 className="mt-4 text-xl font-semibold text-gray-900">
                  Email Verified Successfully!
                </h2>
                <p className="mt-2 text-sm text-gray-600">
                  Redirecting to login page...
                </p>
              </div>
            ) : (
              <div className="flex flex-col items-center">
                <FaTimesCircle className="h-12 w-12 text-red-500" />
                <h2 className="mt-4 text-xl font-semibold text-gray-900">
                  Verification Failed
                </h2>
                <p className="mt-2 text-sm text-red-600">{error}</p>
                <button
                  onClick={() => navigate('/forgot-password')}
                  className="mt-4 text-sm text-indigo-600 hover:text-indigo-500"
                >
                  Request a new verification link
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default VerifyEmailPage; 