import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { submissionService } from '../../../services/submissionService';
import { manuscriptService } from '../../../services/manuscriptService';
import LoadingSpinner from '../../../components/common/LoadingSpinner';
import { 
  FaUniversity,
  FaUser, 
  FaCalendarAlt, 
  FaTag, 
  FaCreditCard, 
  FaSpinner, 
  FaSearch, 
  FaUserEdit,
  FaUpload,
  FaFilePdf,
  FaCheck,
  FaDownload,
  FaExclamationTriangle,
  FaTimesCircle,
  FaClock,
  FaArrowLeft,
  FaGlobe,
  FaBook,
  FaFileUpload,
  FaUserFriends,
  FaFileAlt,
  
} from 'react-icons/fa';
import { useAuth } from '../../../contexts/AuthContext';

const ViewSubmissionPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [submission, setSubmission] = useState(null);
  const [checkingPlagiarism, setCheckingPlagiarism] = useState(false);
  
  // Resubmission states
  const [showResubmissionForm, setShowResubmissionForm] = useState(false);
  const [resubmissionData, setResubmissionData] = useState({
    title: '',
    abstract: '',
    keywords: [],
    translated_abstract: '',
    file: null,
    revision_notes: ''
  });
  const [isResubmitting, setIsResubmitting] = useState(false);
  const [keywordInput, setKeywordInput] = useState('');

  const handleResubmissionDataChange = (field, value) => {
    setResubmissionData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleKeywordAdd = () => {
    if (keywordInput.trim() && resubmissionData.keywords.length < 5) {
      handleResubmissionDataChange('keywords', [...resubmissionData.keywords, keywordInput.trim()]);
      setKeywordInput('');
    }
  };

  const handleKeywordRemove = (index) => {
    const newKeywords = resubmissionData.keywords.filter((_, i) => i !== index);
    handleResubmissionDataChange('keywords', newKeywords);
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      handleResubmissionDataChange('file', file);
    }
  };

  const handleResubmit = async (e) => {
    e.preventDefault();
    setIsResubmitting(true);
    
    try {
      const formData = new FormData();
      
      // Add text fields
      if (resubmissionData.title) formData.append('title', resubmissionData.title);
      if (resubmissionData.abstract) formData.append('abstract', resubmissionData.abstract);
      if (resubmissionData.translated_abstract) formData.append('translated_abstract', resubmissionData.translated_abstract);
      
      // Add keywords as individual array items
      if (resubmissionData.keywords.length > 0) {
        resubmissionData.keywords.forEach((keyword, index) => {
          formData.append(`keywords[${index}]`, keyword);
        });
      }
      
      formData.append('revision_notes', resubmissionData.revision_notes);
      
      // Add file
      if (resubmissionData.file) {
        formData.append('file', resubmissionData.file);
      }

      await manuscriptService.resubmitManuscript(id, formData);
      
      toast.success('Manuscript resubmitted successfully!');
      setShowResubmissionForm(false);
      
      // Refresh submission data
      const data = await submissionService.getSubmission(id);
      setSubmission(data);
    } catch (error) {
      console.error('Resubmission error:', error);
      toast.error(error.message || 'Failed to resubmit manuscript');
    } finally {
      setIsResubmitting(false);
    }
  };

  const initializeResubmissionForm = () => {
    setResubmissionData({
      title: submission.title || '',
      abstract: submission.abstract || '',
      keywords: Array.isArray(submission.keywords) ? [...submission.keywords] : [],
      translated_abstract: submission.translated_abstract || '',
      file: null,
      revision_notes: ''
    });
    setShowResubmissionForm(true);
  };

  useEffect(() => {
    const fetchSubmission = async () => {
      try {
        const data = await submissionService.getSubmission(id);
        // Parse keywords if they're stored as a JSON string
        if (data.keywords && typeof data.keywords === 'string') {
          try {
            data.keywords = JSON.parse(data.keywords);
          } catch {
            data.keywords = data.keywords.split(',').map(k => k.trim());
          }
        } else if (!Array.isArray(data.keywords)) {
          data.keywords = [];
        }
        setSubmission(data);
      } catch (err) {
        console.error('Error fetching submission:', err);
        setError(err.message || 'Failed to load submission details');
      } finally {
        setLoading(false);
      }
    };

    fetchSubmission();
  }, [id]);

  const getStatusBadgeClass = (status) => {
    const statusClasses = {
      'submitted': 'bg-gray-100 text-gray-800',
      'under_review': 'bg-yellow-100 text-yellow-800',
      'payment_pending': 'bg-blue-100 text-blue-800',
      'accepted': 'bg-green-100 text-green-800',
      'rejected': 'bg-red-100 text-red-800',
      'revisions_required': 'bg-orange-100 text-orange-800'
    };
    return statusClasses[status] || 'bg-gray-100 text-gray-800';
  };

  const getPaymentStatusBadgeClass = (status) => {
    const statusClasses = {
      'pending': 'bg-yellow-100 text-yellow-800',
      'paid': 'bg-green-100 text-green-800'
    };
    return statusClasses[status] || 'bg-gray-100 text-gray-800';
  };

  const handlePlagiarismCheck = async () => {
    try {
      setCheckingPlagiarism(true);
      // TODO: Implement plagiarism check API call
      toast.success('Plagiarism check initiated');
    } catch {
      toast.error('Failed to initiate plagiarism check');
    } finally {
      setCheckingPlagiarism(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <FaExclamationTriangle className="mx-auto h-12 w-12 text-red-500" />
          <h2 className="mt-4 text-xl font-semibold text-gray-900">Error Loading Submission</h2>
          <p className="mt-2 text-sm text-gray-600">{error}</p>
          <button
            onClick={() => navigate(-1)}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  if (!submission) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <FaExclamationTriangle className="mx-auto h-12 w-12 text-red-500" />
          <h2 className="mt-4 text-xl font-semibold text-gray-900">Submission Not Found</h2>
          <p className="mt-2 text-sm text-gray-600">The submission you're looking for doesn't exist.</p>
          <button
            onClick={() => navigate(-1)}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const isEditor = user?.role === 'editor';
  const isAuthor = user?.role === 'author';
  const isReviewer = user?.role === 'reviewer';
  const isAdmin = user?.role === 'admin';
  const canViewAll = isEditor || isAdmin;


  // Normalize authors to always be an array
  let authors = [];
  if (Array.isArray(submission.authors)) {
    authors = submission.authors;
  } else if (typeof submission.authors === 'string') {
    try {
      const parsed = JSON.parse(submission.authors);
      authors = Array.isArray(parsed) ? parsed : [submission.authors];
    } catch {
      authors = [submission.authors];
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-9xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">Submission Details</h1>
            <div className="flex space-x-4">
              <button
                onClick={() => navigate(-1)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Go Back
              </button>
              {submission?.links?.file && (
                <a
                  href={submission.links.file}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  <FaDownload className="mr-2" />
                  Download Manuscript
                </a>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-9xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            {/* Left Column - Main Info */}
            <div className="lg:col-span-2 space-y-6">
              {/* Title and Status */}
              <div className="bg-white shadow rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold text-gray-900">{submission.title}</h2>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(submission.status)}`}>
                      {submission.status.replace('_', ' ').toUpperCase()}
                    </span>
                    {submission.payment_status && (
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getPaymentStatusBadgeClass(submission.payment_status)}`}>
                        {submission.payment_status.toUpperCase()}
                      </span>
                    )}
                  </div>
                </div>
                <p className="mt-2 text-sm text-gray-500">Submission ID: {submission.submission_id}</p>
              </div>

              {/* Authors */}
              {authors.length > 0 && (
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Authors</h3>
                  <div className="flex flex-wrap gap-2">
                  {authors.map((author, index) => (
  <span
    key={index}
    className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800"
  >
    {typeof author === 'string'
      ? author
      : [author.name, author.email, author.affiliation].filter(Boolean).join(' | ')
    }
  </span>
))}
                  </div>
                </div>
              )}

              {/* Abstract */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Abstract</h3>
                <div 
                  className="prose max-w-none text-gray-700"
                  dangerouslySetInnerHTML={{ __html: submission.abstract || 'No abstract provided' }}
                />
                {submission.translated_abstract && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-gray-500">Translated Abstract</h4>
                    <div 
                      className="prose max-w-none text-gray-700 mt-2"
                      dangerouslySetInnerHTML={{ __html: submission.translated_abstract }}
                    />
                  </div>
                )}
              </div>

              {/* Keywords */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Keywords</h3>
                <div className="flex flex-wrap gap-2">
                  {submission.keywords.map((keyword, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                    >
                      {keyword}
                    </span>
                  ))}
                </div>
              </div>

              {/* Version History Section */}
              {submission.versions && submission.versions.length > 0 && (
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <FaClock className="mr-2 text-indigo-500" />
                    Version History ({submission.versions.length} version{submission.versions.length > 1 ? 's' : ''})
                  </h3>
                  
                  <div className="space-y-4">
                    {submission.versions.map((version, index) => (
                      <div 
                        key={version.id} 
                        className={`border rounded-lg p-4 ${
                          index === 0 ? 'border-blue-200 bg-blue-50' : 'border-gray-200'
                        }`}
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              index === 0 
                                ? 'bg-blue-100 text-blue-800' 
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              Version {version.version_number}
                              {index === 0 && ' (Latest)'}
                            </span>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(version.status)}`}>
                              {version.status.replace('_', ' ').toUpperCase()}
                            </span>
                          </div>
                          <div className="text-sm text-gray-500">
                            {new Date(version.submitted_at).toLocaleDateString()} at {new Date(version.submitted_at).toLocaleTimeString()}
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                          <div>
                            <h4 className="text-sm font-medium text-gray-700 mb-1">Title</h4>
                            <p className="text-sm text-gray-900">{version.title}</p>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-gray-700 mb-1">Keywords</h4>
                            <div className="flex flex-wrap gap-1">
                              {Array.isArray(version.keywords) ? version.keywords.map((keyword, idx) => (
                                <span key={idx} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                  {keyword}
                                </span>
                              )) : (
                                <span className="text-sm text-gray-500">No keywords</span>
                              )}
                            </div>
                          </div>
                        </div>

                        {version.abstract && (
                          <div className="mb-3">
                            <h4 className="text-sm font-medium text-gray-700 mb-1">Abstract</h4>
                            <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">
                              {version.abstract.length > 200 
                                ? `${version.abstract.substring(0, 200)}...` 
                                : version.abstract
                              }
                            </p>
                          </div>
                        )}

                        {version.revision_notes && (
                          <div className="mb-3">
                            <h4 className="text-sm font-medium text-gray-700 mb-1">Revision Notes</h4>
                            <p className="text-sm text-gray-900 bg-orange-50 p-3 rounded-md border border-orange-200">
                              {version.revision_notes}
                            </p>
                          </div>
                        )}

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            {version.file_path && (
                              <a
                                href={`${import.meta.env.VITE_API_URL}/api/v1/manuscripts/${submission.id}/versions/${version.id}/download`}
                                className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                <FaDownload className="mr-1" />
                                Download Version {version.version_number}
                              </a>
                            )}
                          </div>
                          {version.authors && (
                            <div className="text-sm text-gray-500">
                              {Array.isArray(version.authors) ? version.authors.length : 1} author{Array.isArray(version.authors) && version.authors.length > 1 ? 's' : ''}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Reviews */}
              {(isEditor || isAuthor || isAdmin) && submission.reviews && (
                <div className="bg-white shadow rounded-lg p-6">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium text-gray-900">Reviews</h3>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-500">
                        {submission.reviews?.length || 0} reviews submitted
                      </span>
                    </div>
                  </div>
                  <div className="space-y-6">
                    {submission.reviews.map((review) => (
                      <div key={review.id} className="border rounded-lg p-4">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center">
                              <span className="text-indigo-600 font-medium">
                                {canViewAll ? review.reviewer?.name.split(' ').map(n => n[0]).join('') : 'R'}
                              </span>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-900">
                                {canViewAll ? review.reviewer?.name : 'Reviewer'}
                              </p>
                              <p className="text-xs text-gray-500">
                                Submitted on {new Date(review.submitted_at).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                          <div>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              review.recommendation === 'accept' ? 'bg-green-100 text-green-800' :
                              review.recommendation === 'minor' ? 'bg-blue-100 text-blue-800' :
                              review.recommendation === 'major' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {review.recommendation.charAt(0).toUpperCase() + review.recommendation.slice(1)}
                            </span>
                          </div>
                        </div>
                        <div className="space-y-4">
                          <div>
                            <h4 className="text-sm font-medium text-gray-900 mb-2">Feedback for Authors</h4>
                            <div 
                              className="prose prose-sm max-w-none text-gray-600"
                              dangerouslySetInnerHTML={{ __html: review.feedback }}
                            />
                          </div>
                          {canViewAll && review.confidential_comments && (
                            <div className="bg-gray-50 p-3 rounded-md">
                              <h4 className="text-sm font-medium text-gray-900 mb-2">Confidential Comments (Editors Only)</h4>
                              <div 
                                className="prose prose-sm max-w-none text-gray-600"
                                dangerouslySetInnerHTML={{ __html: review.confidential_comments }}
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Reviewers Section - Only visible to editors and admin */}
              {canViewAll && submission.reviewers && submission.reviewers.length > 0 && (
                <div className="bg-white shadow rounded-lg p-6">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium text-gray-900">Assigned Reviewers</h3>
                    {isEditor && (
                      <button
                        onClick={() => navigate(`/editor/submissions/${id}/assign-reviewers`)}
                        className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200"
                      >
                        <FaUserFriends className="mr-2" />
                        Manage
                      </button>
                    )}
                  </div>
                  <div className="space-y-4">
                    {submission.reviewers.map((reviewer) => (
                      <div key={reviewer.id} className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                            <span className="text-sm font-medium text-gray-600">
                              {reviewer.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                            </span>
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">{reviewer.name}</p>
                          {reviewer.affiliation && (
                            <p className="text-sm text-gray-500 truncate">{reviewer.affiliation}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Right Column - Metadata */}
            <div className="space-y-6">
              {/* Author Info - Hidden from reviewers */}
              {!isReviewer && (
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Author Information</h3>
                  <dl className="space-y-4">
                    <div>
                      <dt className="text-sm font-medium text-gray-500 flex items-center">
                        <FaUser className="mr-2" /> Name
                      </dt>
                      <dd className="mt-1 text-sm text-gray-900">{submission.author.name}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500 flex items-center">
                        <FaUniversity className="mr-2" /> Affiliation
                      </dt>
                      <dd className="mt-1 text-sm text-gray-900">{submission.author.affiliation}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500 flex items-center">
                        <FaGlobe className="mr-2" /> Country
                      </dt>
                      <dd className="mt-1 text-sm text-gray-900">{submission.author.country}</dd>
                    </div>
                  </dl>
                </div>
              )}

              {/* Submission Details */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Submission Details</h3>
                <dl className="space-y-4">
                  <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center">
                      <FaCalendarAlt className="mr-2" /> Submission Date
                    </dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {new Date(submission.created_at).toLocaleDateString()}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center">
                      <FaBook className="mr-2" /> Issue
                    </dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      Volume {submission.issue.volume.number} ({submission.issue.volume.year}), 
                      Issue {submission.issue.number} ({submission.issue.type})
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center">
                      <FaClock className="mr-2" /> Submission Deadline
                    </dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {new Date(submission.issue.submission_deadline).toLocaleDateString()}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center">
                      <FaFileUpload className="mr-2" /> Language
                    </dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {submission.language.toUpperCase()}
                    </dd>
                  </div>
                </dl>
              </div>

              {/* Actions - Only for editors */}
              {isEditor && (
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Actions</h3>
                  <div className="space-y-3">
                    <button
                      onClick={handlePlagiarismCheck}
                      disabled={checkingPlagiarism}
                      className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 disabled:bg-indigo-400"
                    >
                      {checkingPlagiarism ? (
                        <>
                          <FaSpinner className="animate-spin mr-2" />
                          Checking...
                        </>
                      ) : (
                        <>
                          <FaSearch className="mr-2" />
                          Check for Plagiarism
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}

              {/* Resubmission Actions - Only for authors with revisions_required status */}
              {isAuthor && submission.status === 'revisions_required' && (
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Revisions Required</h3>
                  
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <FaUserEdit className="h-5 w-5 text-orange-400" />
                      </div>
                      <div className="ml-3">
                        <h4 className="text-sm font-medium text-orange-800">
                          Revisions Requested
                        </h4>
                        <p className="text-sm text-orange-700 mt-1">
                          Please review the feedback and submit a revised version of your manuscript.
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <button
                    onClick={initializeResubmissionForm}
                    className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
                  >
                    <FaUpload className="mr-2" />
                    Resubmit
                  </button>
                </div>
              )}

              {/* Resubmission Modal */}
              {showResubmissionForm && (
                <div className="fixed inset-0 z-50 overflow-y-auto">
                  <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                    {/* Background overlay */}
                    <div 
                      className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                      onClick={() => setShowResubmissionForm(false)}
                    ></div>

                    {/* Modal */}
                    <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full ">
                      <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div className="sm:flex sm:items-start">
                          <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                              Submit Revised Manuscript
                            </h3>
                            
                            <form onSubmit={handleResubmit} className="space-y-4">
                              {/* Title */}
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                  Title (optional update)
                                </label>
                                <input
                                  type="text"
                                  value={resubmissionData.title}
                                  onChange={(e) => handleResubmissionDataChange('title', e.target.value)}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                  placeholder="Update title if needed"
                                />
                              </div>

                              {/* Abstract */}
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                  Abstract (optional update)
                                </label>
                                <textarea
                                  value={resubmissionData.abstract}
                                  onChange={(e) => handleResubmissionDataChange('abstract', e.target.value)}
                                  rows={3}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                  placeholder="Update abstract if needed"
                                />
                              </div>

                              {/* Keywords */}
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                  Keywords (optional update)
                                </label>
                                <div className="flex flex-wrap gap-2 mb-2">
                                  {resubmissionData.keywords.map((keyword, index) => (
                                    <span
                                      key={index}
                                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800"
                                    >
                                      {keyword}
                                      <button
                                        type="button"
                                        onClick={() => handleKeywordRemove(index)}
                                        className="ml-1 text-orange-600 hover:text-orange-800"
                                      >
                                        ×
                                      </button>
                                    </span>
                                  ))}
                                </div>
                                <div className="flex">
                                  <input
                                    type="text"
                                    value={keywordInput}
                                    onChange={(e) => setKeywordInput(e.target.value)}
                                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleKeywordAdd())}
                                    className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                    placeholder="Add keyword"
                                    disabled={resubmissionData.keywords.length >= 5}
                                  />
                                  <button
                                    type="button"
                                    onClick={handleKeywordAdd}
                                    disabled={!keywordInput.trim() || resubmissionData.keywords.length >= 5}
                                    className="px-3 py-2 bg-orange-600 text-white rounded-r-lg hover:bg-orange-700 disabled:opacity-50"
                                  >
                                    Add
                                  </button>
                                </div>
                              </div>

                              {/* File Upload */}
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                  Revised Manuscript File <span className="text-red-500">*</span>
                                </label>
                                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                                  <div className="text-center">
                                    <FaFilePdf className="mx-auto h-6 w-6 text-gray-400" />
                                    <div className="mt-2">
                                      <label className="cursor-pointer">
                                        <span className="text-orange-600 font-medium hover:text-orange-500">
                                          Choose file
                                        </span>
                                        <input
                                          type="file"
                                          onChange={handleFileChange}
                                          accept=".doc,.docx,.pdf"
                                          className="sr-only"
                                          required
                                        />
                                      </label>
                                      <p className="text-gray-500 text-sm">or drag and drop</p>
                                    </div>
                                    <p className="text-xs text-gray-500 mt-1">DOC, DOCX, PDF up to 10MB</p>
                                    {resubmissionData.file && (
                                      <p className="text-sm text-orange-600 mt-2">{resubmissionData.file.name}</p>
                                    )}
                                  </div>
                                </div>
                              </div>

                              {/* Revision Notes */}
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                  Revision Notes <span className="text-red-500">*</span>
                                </label>
                                <textarea
                                  value={resubmissionData.revision_notes}
                                  onChange={(e) => handleResubmissionDataChange('revision_notes', e.target.value)}
                                  rows={3}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                  placeholder="Describe the changes you made in response to reviewer feedback..."
                                  required
                                />
                              </div>
                            </form>
                          </div>
                        </div>
                      </div>
                      
                      {/* Modal Footer */}
                      <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button
                          onClick={handleResubmit}
                          disabled={isResubmitting || !resubmissionData.revision_notes.trim() || !resubmissionData.file}
                          className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-orange-600 text-base font-medium text-white hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
                        >
                          {isResubmitting ? (
                            <>
                              <FaSpinner className="animate-spin mr-2" />
                              Submitting...
                            </>
                          ) : (
                            <>
                              <FaUpload className="mr-2" />
                              Submit Revision
                            </>
                          )}
                        </button>
                        <button
                          type="button"
                          onClick={() => setShowResubmissionForm(false)}
                          className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Payment Actions - Only for authors with payment_pending status */}
              {isAuthor && submission.status === 'payment_pending' && (
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Required</h3>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <FaCreditCard className="h-5 w-5 text-green-400" />
                      </div>
                      <div className="ml-3">
                        <h4 className="text-sm font-medium text-green-800">
                          Congratulations! Your manuscript has been accepted.
                        </h4>
                        <p className="text-sm text-green-700 mt-1">
                          Please complete the publication fee payment to proceed with publication.
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-green-600">75,000 CFA</p>
                      <p className="text-sm text-gray-500">Publication Fee</p>
                    </div>
                    <button
                      onClick={() => navigate(`/author/submissions/${id}/payment`)}
                      className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700"
                    >
                      <FaCreditCard className="mr-2" />
                      Pay Now
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewSubmissionPage; 