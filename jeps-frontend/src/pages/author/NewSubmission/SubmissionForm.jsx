import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from 'react-hot-toast';
import { manuscriptService } from '../../../services/manuscriptService';
import { 
  FaUpload, 
  FaCalendarAlt, 
  FaLanguage, 
  FaFileWord, 
  FaInfoCircle,
  FaCheckCircle,
  FaRegClock,
  FaBookOpen,
  FaLock,
  FaChevronDown,
  FaTimes,
  FaFilePdf,
  FaSpinner
} from "react-icons/fa";

const SubmissionForm = () => {
  const [formData, setFormData] = useState({
    title: "",
    abstract: "",
    keywords: [],
    authors: [],
    translatedAbstract: "",
    language: "en",
    issue_id: "",
    volume_id: "",
    file: null,
    agreeTerms: false
  });

  const [keywordInput, setKeywordInput] = useState("");
  const keywordInputRef = useRef(null);

  const [authorInput, setAuthorInput] = useState("");
  const authorInputRef = useRef(null);

  const [errors, setErrors] = useState({});
  const [issues, setIssues] = useState([]);
  const [volumes, setVolumes] = useState([]);
  const [activeHelpSection, setActiveHelpSection] = useState("guidelines");
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [expandedSections, setExpandedSections] = useState({
    title: false,
    abstract: false,
    keywords: false,
    translation: false,
    issue: false,
    file: false,
    terms: false,
    authors: false
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const [issuesData, volumesData] = await Promise.all([
          manuscriptService.getIssues(),
          manuscriptService.getVolumes()
        ]);
        setIssues(issuesData);
        setVolumes(volumesData);
      } catch (error) {
        toast.error('Failed to load issues and volumes');
        console.error('Error loading data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleChange = (e) => {
    const { name, value, type, checked, files } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : type === "file" ? files[0] : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }

    if (type === "file" && files[0]) {
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.random() * 10;
        if (progress >= 100) {
          clearInterval(interval);
          setUploadProgress(100);
        } else {
          setUploadProgress(progress);
        }
      }, 200);
    }
  };

  const handleKeywordInputChange = (e) => {
    setKeywordInput(e.target.value);
  };

  const handleKeywordInputKeyDown = (e) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      const keyword = keywordInput.trim();
      if (keyword && !formData.keywords.includes(keyword)) {
        if (formData.keywords.length < 5) {
          setFormData(prev => ({
            ...prev,
            keywords: [...prev.keywords, keyword]
          }));
          setKeywordInput('');
        } else {
          toast.error('Maximum 5 keywords allowed');
        }
      }
    } else if (e.key === 'Backspace' && !keywordInput && formData.keywords.length > 0) {
      // Remove last keyword when backspace is pressed on empty input
      setFormData(prev => ({
        ...prev,
        keywords: prev.keywords.slice(0, -1)
      }));
    }
  };

  const removeKeyword = (indexToRemove) => {
    setFormData(prev => ({
      ...prev,
      keywords: prev.keywords.filter((_, index) => index !== indexToRemove)
    }));
  };

  const handleAuthorInputChange = (e) => {
    setAuthorInput(e.target.value);
  };

  const handleAuthorInputKeyDown = (e) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      const author = authorInput.trim();
      if (author && !formData.authors.includes(author)) {
        setFormData(prev => ({
          ...prev,
          authors: [...prev.authors, author]
        }));
        setAuthorInput('');
      }
    } else if (e.key === 'Backspace' && !authorInput && formData.authors.length > 0) {
      setFormData(prev => ({
        ...prev,
        authors: prev.authors.slice(0, -1)
      }));
    }
  };

  const removeAuthor = (indexToRemove) => {
    setFormData(prev => ({
      ...prev,
      authors: prev.authors.filter((_, index) => index !== indexToRemove)
    }));
  };

  // Helper to count words
  const countWords = (text) => {
    return text.trim() === '' ? 0 : text.trim().split(/\s+/).length;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrors({});
    
    // Validate authors
    if (!formData.authors || formData.authors.length === 0) {
      setErrors(prev => ({ ...prev, authors: 'At least one author is required.' }));
      setIsSubmitting(false);
      return;
    }
    
    // Validate abstract word count
    if (countWords(formData.abstract) > 250) {
      setErrors(prev => ({ ...prev, abstract: 'Abstract must not exceed 250 words.' }));
      setIsSubmitting(false);
      return;
    }
    
    try {
      const formDataToSend = new FormData();
      Object.keys(formData).forEach(key => {
        if (formData[key] !== null) {
          if (key === 'keywords' || key === 'authors') {
            formDataToSend.append(key, JSON.stringify(formData[key]));
          } else {
            formDataToSend.append(key, formData[key]);
          }
        }
      });

      // Log the form data for debugging
      console.log('Submitting form data:', {
        ...formData,
        keywords: formData.keywords,
        authors: formData.authors
      });

      await manuscriptService.submitManuscript(formDataToSend);
      setSubmitSuccess(true);
      toast.success('Manuscript submitted successfully!');
    } catch (error) {
      if (error.errors) {
        setErrors(error.errors);
        toast.error('Please correct the errors in the form');
      } else {
        toast.error(error.message || 'Failed to submit manuscript');
      }
      console.error('Submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setFormData({
      title: "",
      abstract: "",
      keywords: [],
      authors: [],
      translatedAbstract: "",
      language: "en",
      issue_id: "",
      volume_id: "",
      file: null,
      agreeTerms: false
    });
    setKeywordInput("");
    setAuthorInput("");
    setSubmitSuccess(false);
    setUploadProgress(0);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <FaSpinner className="w-8 h-8 text-blue-600 animate-spin" />
        <span className="ml-3 text-lg text-gray-700">Loading...</span>
      </div>
    );
  }

  return (
    <div className="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <AnimatePresence>
        {submitSuccess ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl shadow-lg overflow-hidden p-8 text-center"
          >
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
              <FaCheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <h2 className="mt-3 text-2xl font-bold text-gray-800">Submission Successful!</h2>
            <p className="mt-2 text-gray-600">
              Your manuscript has been submitted for review. You will receive a confirmation email shortly.
            </p>
            <div className="mt-6">
              <motion.button
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.98 }}
                onClick={resetForm}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                Submit Another Manuscript
              </motion.button>
            </div>
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex flex-col lg:flex-row gap-8"
          >
            {/* Main Form Content */}
            <div className="lg:w-2/3">
              <motion.div 
                whileHover={{ boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)" }}
                className="bg-white rounded-xl shadow-lg overflow-hidden"
              >
                <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
                  <h2 className="text-2xl font-bold text-white">Submit Your Manuscript</h2>
                  <p className="mt-1 text-blue-100">
                    Complete the form below to submit your paper for consideration in JEPS
                  </p>
                </div>
                
                <form onSubmit={handleSubmit} className="p-6 md:p-8 space-y-8">
                  {/* Title */}
                  <div className="border-b border-gray-200 pb-6">
                    <div 
                      className="flex justify-between items-center cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors"
                      onClick={() => toggleSection('title')}
                    >
                      <h3 className="text-lg font-medium text-gray-900 flex items-center">
                        <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center mr-3">1</span>
                        Manuscript Title
                      </h3>
                      <FaChevronDown className={`transition-transform ${expandedSections.title ? 'rotate-180' : ''}`} />
                    </div>
                    
                    <AnimatePresence>
                      {expandedSections.title && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="mt-4"
                        >
                          <div>
                            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                              Title <span className="text-red-500">*</span>
                            </label>
                            <input
                              type="text"
                              name="title"
                              id="title"
                              value={formData.title}
                              onChange={handleChange}
                              className={`block w-full px-4 py-3 border ${
                                errors.title ? 'border-red-500' : 'border-gray-300'
                              } rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 transition-colors`}
                              placeholder="Enter the title of your manuscript"
                              required
                            />
                            {errors.title && (
                              <p className="mt-1 text-sm text-red-600">
                                {Array.isArray(errors.title) ? errors.title[0] : errors.title}
                              </p>
                            )}
                            <p className="mt-1 text-xs text-gray-500">
                              Make your title concise yet descriptive (max 20 words)
                            </p>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                  
                  {/* Abstract */}
                  <div className="border-b border-gray-200 pb-6">
                    <div 
                      className="flex justify-between items-center cursor-pointer"
                      onClick={() => toggleSection('abstract')}
                    >
                      <h3 className="text-lg font-medium text-gray-900 flex items-center">
                        <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center mr-3">2</span>
                        Abstract
                      </h3>
                      <FaChevronDown className={`transition-transform ${expandedSections.abstract ? 'rotate-180' : ''}`} />
                    </div>
                    
                    <AnimatePresence>
                      {expandedSections.abstract && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="mt-4"
                        >
                          <div>
                            <label htmlFor="abstract" className="block text-sm font-medium text-gray-700 mb-1">
                              Abstract <span className="text-red-500">*</span>
                              <span className="ml-2 text-xs text-gray-500">(max 250 words)</span>
                            </label>
                            <div className="relative">
                              <textarea
                                name="abstract"
                                id="abstract"
                                rows={5}
                                value={formData.abstract}
                                onChange={handleChange}
                                // maxLength={250}
                                className={`block w-full px-4 py-3 border ${
                                  errors.abstract ? 'border-red-500' : 'border-gray-300'
                                } rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500`}
                                placeholder="Provide a concise summary of your research"
                                required
                              />
                              <div className="absolute bottom-3 right-3 text-xs text-gray-500 bg-white px-2 py-1 rounded-full shadow-sm">
                                {countWords(formData.abstract)}/250 words
                                {countWords(formData.abstract) > 250 && (
                                  <span className="text-red-500 ml-2">Word limit exceeded!</span>
                                )}
                              </div>
                            </div>
                            {errors.abstract && (
                              <p className="mt-1 text-sm text-red-600">
                                {Array.isArray(errors.abstract) ? errors.abstract[0] : errors.abstract}
                              </p>
                            )}
                            <p className="mt-1 text-xs text-gray-500">
                              Include research objectives, methods, results, and conclusions
                            </p>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                  
                  {/* Keywords */}
                  <div className="border-b border-gray-200 pb-6">
                    <div 
                      className="flex justify-between items-center cursor-pointer"
                      onClick={() => toggleSection('keywords')}
                    >
                      <h3 className="text-lg font-medium text-gray-900 flex items-center">
                        <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center mr-3">3</span>
                        Keywords
                      </h3>
                      <FaChevronDown className={`transition-transform ${expandedSections.keywords ? 'rotate-180' : ''}`} />
                    </div>
                    
                    <AnimatePresence>
                      {expandedSections.keywords && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="mt-4"
                        >
                          <div>
                            <label htmlFor="keywords" className="block text-sm font-medium text-gray-700 mb-1">
                              Keywords <span className="text-red-500">*</span>
                              <span className="ml-2 text-xs text-gray-500">(up to 5 keywords)</span>
                            </label>
                            <div className={`min-h-[42px] px-4 py-2 border ${
                              errors.keywords ? 'border-red-500' : 'border-gray-300'
                            } rounded-lg shadow-sm focus-within:ring-1 focus-within:ring-blue-500 focus-within:border-blue-500`}>
                              <div className="flex flex-wrap gap-2">
                                {formData.keywords.map((keyword, index) => (
                                  <span
                                    key={index}
                                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                                  >
                                    {keyword}
                                    <button
                                      type="button"
                                      onClick={() => removeKeyword(index)}
                                      className="ml-1.5 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-blue-200 focus:outline-none"
                                    >
                                      <FaTimes className="w-3 h-3" />
                                    </button>
                                  </span>
                                ))}
                                <input
                                  ref={keywordInputRef}
                                  type="text"
                                  value={keywordInput}
                                  onChange={handleKeywordInputChange}
                                  onKeyDown={handleKeywordInputKeyDown}
                                  className="flex-1 min-w-[120px] outline-none bg-transparent"
                                  placeholder={formData.keywords.length === 0 ? "Type keywords and press Enter or comma" : ""}
                                />
                              </div>
                            </div>
                            {errors.keywords && (
                              <p className="mt-1 text-sm text-red-600">
                                {Array.isArray(errors.keywords) ? errors.keywords[0] : errors.keywords}
                              </p>
                            )}
                            <p className="mt-1 text-xs text-gray-500">
                              Press Enter or comma to add a keyword. Maximum 5 keywords allowed.
                            </p>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                  
                  {/* Authors */}
                  <div className="border-b border-gray-200 pb-6">
                    <div 
                      className="flex justify-between items-center cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors"
                      onClick={() => toggleSection('authors')}
                    >
                      <h3 className="text-lg font-medium text-gray-900 flex items-center">
                        <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center mr-3">A</span>
                        Authors <span className="text-red-500 ml-1">*</span>
                      </h3>
                      <FaChevronDown className={`transition-transform ${expandedSections.authors ? 'rotate-180' : ''}`} />
                    </div>
                    <AnimatePresence>
                      {expandedSections.authors && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="mt-4"
                        >
                          <div>
                            <label htmlFor="authors" className="block text-sm font-medium text-gray-700 mb-1">
                              Authors <span className="text-red-500">*</span>
                            </label>
                            <div className={`min-h-[42px] px-4 py-2 border ${
                              errors.authors ? 'border-red-500' : 'border-gray-300'
                            } rounded-lg shadow-sm focus-within:ring-1 focus-within:ring-blue-500 focus-within:border-blue-500`}>
                              {formData.authors.map((author, index) => (
                                <span
                                  key={index}
                                  className="inline-flex items-center bg-blue-100 text-blue-800 rounded-full px-3 py-1 text-sm mr-2 mb-2"
                                >
                                  {author}
                                  <button
                                    type="button"
                                    className="ml-2 text-blue-600 hover:text-red-600 focus:outline-none"
                                    onClick={() => removeAuthor(index)}
                                  >
                                    <FaTimes className="w-3 h-3" />
                                  </button>
                                </span>
                              ))}
                              <input
                                type="text"
                                id="authors"
                                name="authors"
                                ref={authorInputRef}
                                value={authorInput}
                                onChange={handleAuthorInputChange}
                                onKeyDown={handleAuthorInputKeyDown}
                                className="border-none outline-none bg-transparent text-sm py-1 px-2"
                                placeholder="Add author and press Enter"
                              />
                            </div>
                            {errors.authors && (
                              <p className="mt-1 text-sm text-red-600">
                                {Array.isArray(errors.authors) ? errors.authors[0] : errors.authors}
                              </p>
                            )}
                            <p className="mt-1 text-xs text-gray-500">
                              List all authors (press Enter after each name)
                            </p>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                  
                  {/* Language and Translation */}
                  <div className="border-b border-gray-200 pb-6">
                    <div 
                      className="flex justify-between items-center cursor-pointer"
                      onClick={() => toggleSection('translation')}
                    >
                      <h3 className="text-lg font-medium text-gray-900 flex items-center">
                        <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center mr-3">4</span>
                        Language & Translation
                      </h3>
                      <FaChevronDown className={`transition-transform ${expandedSections.translation ? 'rotate-180' : ''}`} />
                    </div>
                    
                    <AnimatePresence>
                      {expandedSections.translation && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="mt-4"
                        >
                          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <div>
                              <label htmlFor="language" className="block text-sm font-medium text-gray-700 mb-1">
                                Manuscript Language <span className="text-red-500">*</span>
                              </label>
                              <div className="relative rounded-md shadow-sm">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                  <FaLanguage className="h-5 w-5 text-gray-400" />
                                </div>
                                <select
                                  name="language"
                                  id="language"
                                  value={formData.language}
                                  onChange={handleChange}
                                  className={`block w-full pl-10 pr-12 py-3 border ${
                                    errors.language ? 'border-red-500' : 'border-gray-300'
                                  } rounded-lg focus:ring-blue-500 focus:border-blue-500`}
                                  required
                                >
                                  <option value="en">English</option>
                                  <option value="fr">French</option>
                                </select>
                              </div>
                              {errors.language && (
                                <p className="mt-1 text-sm text-red-600">
                                  {Array.isArray(errors.language) ? errors.language[0] : errors.language}
                                </p>
                              )}
                            </div>
                            
                            <div>
                              <label htmlFor="translatedAbstract" className="block text-sm font-medium text-gray-700 mb-1">
                                Translated Abstract (Optional)
                              </label>
                              <textarea
                                name="translatedAbstract"
                                id="translatedAbstract"
                                rows={4}
                                value={formData.translatedAbstract}
                                onChange={handleChange}
                                className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                placeholder={`If submitting in ${formData.language === 'en' ? 'English' : 'French'}, provide ${formData.language === 'en' ? 'French' : 'English'} translation`}
                              />
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                  
                  {/* Issue and Volume Selection */}
                  <div className="border-b border-gray-200 pb-6">
                    <div 
                      className="flex justify-between items-center cursor-pointer"
                      onClick={() => toggleSection('issue')}
                    >
                      <h3 className="text-lg font-medium text-gray-900 flex items-center">
                        <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center mr-3">5</span>
                        Publication Details
                      </h3>
                      <FaChevronDown className={`transition-transform ${expandedSections.issue ? 'rotate-180' : ''}`} />
                    </div>
                    
                    <AnimatePresence>
                      {expandedSections.issue && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="mt-4"
                        >
                          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <div>
                              <label htmlFor="volume_id" className="block text-sm font-medium text-gray-700 mb-1">
                                Volume <span className="text-red-500">*</span>
                              </label>
                              <select
                                name="volume_id"
                                id="volume_id"
                                value={formData.volume_id}
                                onChange={handleChange}
                                className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                required
                              >
                                <option value="">Select a volume</option>
                                {volumes.map(volume => (
                                  <option key={volume.id} value={volume.id}>
                                    Volume {volume.number} ({volume.year})
                                  </option>
                                ))}
                              </select>
                            </div>
                            
                            <div>
                              <label htmlFor="issue_id" className="block text-sm font-medium text-gray-700 mb-1">
                                Issue <span className="text-red-500">*</span>
                              </label>
                              <select
                                name="issue_id"
                                id="issue_id"
                                value={formData.issue_id}
                                onChange={handleChange}
                                className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                required
                              >
                                <option value="">Select an issue</option>
                                {issues.map(issue => (
                                  <option key={issue.id} value={issue.id}>
                                    Issue {issue.number} - {issue.title}
                                  </option>
                                ))}
                              </select>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                  
                  {/* File Upload */}
                  <div className="border-b border-gray-200 pb-6">
                    <div 
                      className="flex justify-between items-center cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors"
                      onClick={() => toggleSection('file')}
                    >
                      <h3 className="text-lg font-medium text-gray-900 flex items-center">
                        <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center mr-3">6</span>
                        Manuscript File
                      </h3>
                      <FaChevronDown className={`transition-transform ${expandedSections.file ? 'rotate-180' : ''}`} />
                    </div>
                    
                    <AnimatePresence>
                      {expandedSections.file && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="mt-4"
                        >
                          <div>
                            <label htmlFor="file" className="block text-sm font-medium text-gray-700 mb-1">
                              Manuscript File <span className="text-red-500">*</span>
                              <span className="ml-2 text-xs text-gray-500">(MS Word format)</span>
                            </label>
                            <div className={`mt-1 flex justify-center px-6 pt-5 pb-6 border-2 ${
                              errors.file ? 'border-red-500' : 'border-gray-300'
                            } border-dashed rounded-lg hover:border-blue-400 transition-colors relative shadow-sm hover:shadow-md`}>
                              {formData.file ? (
                                <div className="text-center">
                                  <div className="flex items-center justify-center text-gray-500 mb-2">
                                    <FaFileWord className="mr-2" size={20} />
                                    <span>{formData.file.name}</span>
                                    <button 
                                      type="button"
                                      onClick={() => setFormData(prev => ({ ...prev, file: null }))}
                                      className="ml-2 text-gray-400 hover:text-gray-600 transition-colors"
                                    >
                                      <FaTimes />
                                    </button>
                                  </div>
                                  {uploadProgress > 0 && uploadProgress < 100 && (
                                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                                      <div 
                                        className="bg-blue-600 h-2.5 rounded-full transition-all duration-300" 
                                        style={{ width: `${uploadProgress}%` }}
                                      ></div>
                                    </div>
                                  )}
                                </div>
                              ) : (
                                <div className="space-y-1 text-center">
                                  <FaUpload className="mx-auto h-12 w-12 text-gray-400" />
                                  <div className="flex text-sm text-gray-600">
                                    <label
                                      htmlFor="file"
                                      className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                                    >
                                      <span>Upload a file</span>
                                      <input
                                        id="file"
                                        name="file"
                                        type="file"
                                        accept=".doc,.docx"
                                        onChange={handleChange}
                                        className="sr-only"
                                        required
                                      />
                                    </label>
                                    <p className="pl-1">or drag and drop</p>
                                  </div>
                                  <div className="flex items-center justify-center text-gray-500">
                                    <FaFileWord className="mr-2" size={20} />
                                    <p className="text-xs">MS Word (.doc, .docx) up to 10MB</p>
                                  </div>
                                </div>
                              )}
                            </div>
                            {errors.file && (
                              <p className="mt-1 text-sm text-red-600">
                                {Array.isArray(errors.file) ? errors.file[0] : errors.file}
                              </p>
                            )}
                            <p className="mt-2 text-xs text-gray-500">
                              Ensure your manuscript follows our <a href="#" className="text-blue-600 hover:underline">formatting guidelines</a>
                            </p>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                  
                  {/* Terms and Conditions */}
                  <div>
                    <div 
                      className="flex justify-between items-center cursor-pointer"
                      onClick={() => toggleSection('terms')}
                    >
                      <h3 className="text-lg font-medium text-gray-900 flex items-center">
                        <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center mr-3">7</span>
                        Certification
                      </h3>
                      <FaChevronDown className={`transition-transform ${expandedSections.terms ? 'rotate-180' : ''}`} />
                    </div>
                    
                    <AnimatePresence>
                      {expandedSections.terms && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="mt-4"
                        >
                          <div className="flex items-start">
                            <div className="flex items-center h-5">
                              <input
                                id="agreeTerms"
                                name="agreeTerms"
                                type="checkbox"
                                checked={formData.agreeTerms}
                                onChange={handleChange}
                                className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
                                required
                              />
                            </div>
                            <div className="ml-3 text-sm">
                              <label htmlFor="agreeTerms" className="font-medium text-gray-700">
                                I certify that: <span className="text-red-500">*</span>
                              </label>
                              <ul className="mt-1 text-gray-500 list-disc pl-5 space-y-1">
                                <li>This manuscript has not been previously published</li>
                                <li>It is not currently under consideration elsewhere</li>
                                <li>I agree to pay the publication fee of 75,000 CFA if accepted</li>
                                <li>I have read and agree to the <a href="#" className="text-blue-600 hover:underline">submission terms</a></li>
                              </ul>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                  
                  {/* Submit Button */}
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="pt-6"
                  >
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className={`w-full flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white transition-colors ${
                        isSubmitting ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'
                      }`}
                    >
                      {isSubmitting ? (
                        <>
                          <FaSpinner className="animate-spin -ml-1 mr-3 h-5 w-5" />
                          Submitting...
                        </>
                      ) : (
                        'Submit Manuscript'
                      )}
                    </button>
                  </motion.div>
                </form>
              </motion.div>
            </div>

            {/* Right Sidebar */}
            <div className="lg:w-1/3">
              <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
                  <h2 className="text-xl font-bold text-white">Help & Guidelines</h2>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    <div className="flex space-x-4 border-b border-gray-200 pb-4">
                      <button
                        onClick={() => setActiveHelpSection("guidelines")}
                        className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
                          activeHelpSection === "guidelines"
                            ? "bg-blue-100 text-blue-700"
                            : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                        }`}
                      >
                        Guidelines
                      </button>
                      <button
                        onClick={() => setActiveHelpSection("faq")}
                        className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
                          activeHelpSection === "faq"
                            ? "bg-blue-100 text-blue-700"
                            : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                        }`}
                      >
                        FAQ
                      </button>
                    </div>

                    <AnimatePresence mode="wait">
                      <motion.div
                        key={activeHelpSection}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.2 }}
                      >
                        {activeHelpSection === "guidelines" && (
                          <div className="space-y-6">
                            <div className="bg-gray-50 rounded-lg p-4 shadow-sm">
                              <h4 className="text-sm font-medium text-gray-900">File Format</h4>
                              <p className="text-sm text-gray-500 mt-1">
                                Submit your manuscript as a Microsoft Word (.doc or .docx) file. PDFs are not accepted.
                              </p>
                            </div>
                            <div className="bg-gray-50 rounded-lg p-4 shadow-sm">
                              <h4 className="text-sm font-medium text-gray-900">Publication Fee</h4>
                              <p className="text-sm text-gray-500 mt-1">
                                The publication fee is 75,000 CFA (or equivalent in other currencies) for accepted manuscripts.
                              </p>
                            </div>
                            <div className="bg-gray-50 rounded-lg p-4 shadow-sm">
                              <h4 className="text-sm font-medium text-gray-900">Submission Policy</h4>
                              <p className="text-sm text-gray-500 mt-1">
                                Each manuscript can only be submitted to one issue. Duplicate submissions will be rejected.
                              </p>
                            </div>
                          </div>
                        )}

                        {activeHelpSection === "faq" && (
                          <div className="space-y-6">
                            <div className="bg-gray-50 rounded-lg p-4 shadow-sm">
                              <h4 className="text-sm font-medium text-gray-900">What file format should I use?</h4>
                              <p className="text-sm text-gray-500 mt-1">
                                Submit your manuscript as a Microsoft Word (.doc or .docx) file. PDFs are not accepted.
                              </p>
                            </div>
                            <div className="bg-gray-50 rounded-lg p-4 shadow-sm">
                              <h4 className="text-sm font-medium text-gray-900">What is the publication fee?</h4>
                              <p className="text-sm text-gray-500 mt-1">
                                The publication fee is 75,000 CFA (or equivalent in other currencies) for accepted manuscripts.
                              </p>
                            </div>
                            <div className="bg-gray-50 rounded-lg p-4 shadow-sm">
                              <h4 className="text-sm font-medium text-gray-900">Can I submit to both issues?</h4>
                              <p className="text-sm text-gray-500 mt-1">
                                No, each manuscript can only be submitted to one issue. Duplicate submissions will be rejected.
                              </p>
                            </div>
                          </div>
                        )}
                      </motion.div>
                    </AnimatePresence>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SubmissionForm;