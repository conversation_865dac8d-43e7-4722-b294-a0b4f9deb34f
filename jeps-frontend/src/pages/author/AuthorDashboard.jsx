/**
 * <AUTHOR> (andrew21-mch)
 * @github https://github.com/andrew21-mch
 * @description Author Dashboard Component - Main dashboard for authors showing manuscript statistics and recent submissions
 */

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaFileUpload, FaFileAlt, FaClock, FaCheckCircle, FaTimesCircle, FaChevronRight, FaUser, FaCalendarAlt, FaChartLine, FaAward, FaTrophy, FaBook, FaEye, FaCreditCard } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { Line, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js';
import { manuscriptService } from '../../services/manuscriptService';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { toast } from 'react-hot-toast';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const AuthorDashboard = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [manuscripts, setManuscripts] = useState([]);
  const [stats, setStats] = useState({
    total: 0,
    underReview: 0,
    published: 0,
    rejected: 0,
    accepted: 0,
    successRate: 0,
    averageReviewTime: 0
  });
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    fetchManuscripts();
  }, []);

  const fetchManuscripts = async () => {
    try {
      setLoading(true);
      const response = await manuscriptService.getAuthorManuscripts();
      const manuscriptData = response.data || response;
      setManuscripts(manuscriptData);
      
      // Calculate comprehensive stats including payment information
      const calculatedStats = {
        total: manuscriptData.length,
        totalSubmissions: manuscriptData.length,
        underReview: manuscriptData.filter(m => m.status === 'under_review').length,
        published: manuscriptData.filter(m => m.status === 'published').length,
        rejected: manuscriptData.filter(m => m.status === 'rejected').length,
        accepted: manuscriptData.filter(m => m.status === 'accepted').length,
        paymentPending: manuscriptData.filter(m => m.status === 'payment_pending').length,
        paymentRequired: manuscriptData.filter(m => m.status === 'payment_pending' && m.payment_status === 'pending').length,
        totalPaymentsDue: manuscriptData.filter(m => m.status === 'payment_pending' && m.payment_status === 'pending').length * 75000, // 75,000 CFA per manuscript
        successRate: manuscriptData.length > 0 ? ((manuscriptData.filter(m => ['accepted', 'published', 'payment_pending'].includes(m.status)).length / manuscriptData.length) * 100) : 0,
        averageReviewTime: calculateAverageReviewTime(manuscriptData)
      };
      
      setStats(calculatedStats);
      setError(null);
    } catch (err) {
      console.error('Error fetching manuscripts:', err);
      setError(err.message || 'Failed to load dashboard data');
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const calculateAverageReviewTime = (manuscripts) => {
    const completedReviews = manuscripts.filter(m => 
      ['accepted', 'rejected', 'published', 'payment_pending'].includes(m.status) && m.updated_at && m.created_at
    );
    
    if (completedReviews.length === 0) return 0;
    
    const totalDays = completedReviews.reduce((sum, manuscript) => {
      const created = new Date(manuscript.created_at);
      const updated = new Date(manuscript.updated_at);
      const diffTime = Math.abs(updated - created);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return sum + diffDays;
    }, 0);
    
    return Math.round(totalDays / completedReviews.length);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'under_review':
        return <FaClock className="text-yellow-500" />;
      case 'published':
        return <FaCheckCircle className="text-green-500" />;
      case 'accepted':
        return <FaCheckCircle className="text-blue-500" />;
      case 'rejected':
        return <FaTimesCircle className="text-red-500" />;
      default:
        return <FaFileAlt className="text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800';
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'accepted':
        return 'bg-blue-100 text-blue-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredManuscripts = manuscripts.filter(manuscript =>
    manuscript.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    manuscript.submission_id.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Chart data for submission activity over time
  const generateSubmissionChartData = (submissionsData) => {
    // Get the last 6 months
    const months = [];
    const now = new Date();
    
    for (let i = 5; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      months.push({
        label: date.toLocaleDateString('en-US', { month: 'short' }),
        year: date.getFullYear(),
        month: date.getMonth()
      });
    }

    // Count submissions per month
    const submissionCounts = months.map(month => {
      return submissionsData.filter(submission => {
        const submissionDate = new Date(submission.created_at);
        return submissionDate.getFullYear() === month.year && 
               submissionDate.getMonth() === month.month;
      }).length;
    });

    return {
      labels: months.map(m => m.label),
      datasets: [
        {
          label: 'Submissions',
          data: submissionCounts,
          borderColor: 'rgb(79, 70, 229)',
          backgroundColor: 'rgba(79, 70, 229, 0.1)',
          tension: 0.4,
          fill: true,
        }
      ],
    };
  };

  // Status distribution chart data
  const statusChartData = {
    labels: ['Under Review', 'Published', 'Accepted', 'Rejected'],
    datasets: [
      {
        data: [stats.underReview, stats.published, stats.accepted, stats.rejected],
        backgroundColor: [
          'rgba(245, 158, 11, 0.8)',
          'rgba(34, 197, 94, 0.8)',
          'rgba(59, 130, 246, 0.8)',
          'rgba(239, 68, 68, 0.8)'
        ],
        borderColor: [
          'rgba(245, 158, 11, 1)',
          'rgba(34, 197, 94, 1)',
          'rgba(59, 130, 246, 1)',
          'rgba(239, 68, 68, 1)'
        ],
        borderWidth: 2,
      },
    ],
  };

  const submissionChartData = generateSubmissionChartData(manuscripts);

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Submission Activity Over Time',
        font: {
          size: 16,
          weight: 'bold'
        }
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1,
        },
        title: {
          display: true,
          text: 'Number of Submissions'
        }
      },
      x: {
        title: {
          display: true,
          text: 'Month'
        }
      }
    },
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
      },
      title: {
        display: true,
        text: 'Submission Status Distribution',
        font: {
          size: 16,
          weight: 'bold'
        }
      },
    },
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <FaFileAlt className="mx-auto h-12 w-12 text-red-500" />
          <h2 className="mt-4 text-xl font-semibold text-gray-900">Error Loading Dashboard</h2>
          <p className="mt-2 text-sm text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  const dashboardStats = [
    {
      title: 'New Submission',
      description: 'Submit a new manuscript',
      icon: <FaFileUpload className="text-blue-500" />,
      action: () => navigate('/author/submit'),
      isAction: true
    },
    {
      title: 'Total Submissions',
      value: stats.total,
      color: 'text-indigo-600',
      description: 'All manuscripts',
      icon: <FaFileAlt className="text-indigo-500" />,
      trend: 'neutral'
    },
    {
      title: 'Under Review',
      value: stats.underReview,
      color: 'text-yellow-600',
      description: 'Currently being reviewed',
      icon: <FaClock className="text-yellow-500" />,
      trend: 'neutral'
    },
    {
      title: 'Published Papers',
      value: stats.published,
      color: 'text-green-600',
      description: `Success rate: ${stats.successRate}%`,
      icon: <FaCheckCircle className="text-green-500" />,
      trend: 'up'
    },
    {
      title: 'Average Review Time',
      value: `${stats.averageReviewTime}d`,
      color: 'text-purple-600',
      description: 'Days per review',
      icon: <FaChartLine className="text-purple-500" />,
      trend: 'down'
    }
  ];

  return (
    <>
      {/* 
        Developer: Nfon Andrew (andrew21-mch)
        GitHub: https://github.com/andrew21-mch
        Component: Author Dashboard
        Description: Main dashboard for authors showing manuscript statistics and recent submissions
      */}
      <div className="p-6">
        <motion.div 
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex flex-col md:flex-row md:items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">Welcome back, {user?.name}!</h1>
              <p className="text-gray-600 mt-2">Manage your submissions and track their progress through the review process.</p>
            </div>
            <div className="mt-4 md:mt-0 flex items-center space-x-4">
              <div className="flex items-center text-sm text-gray-600">
                <FaAward className="mr-2 text-yellow-500" />
                <span>Author Level: {stats.published > 10 ? 'Expert' : stats.published > 5 ? 'Advanced' : 'Emerging'}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <FaTrophy className="mr-2 text-gold-500" />
                <span>{stats.published} Published Papers</span>
              </div>
            </div>
          </div>
        </motion.div>
        
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8"
        >
          {dashboardStats.map((stat, index) => (
          <motion.div 
              key={index}
            whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)" }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
              className={`bg-white p-6 rounded-xl shadow-md border border-gray-100 hover:border-transparent transition-all ${
                stat.isAction ? 'cursor-pointer hover:bg-blue-50' : ''
              }`}
              onClick={stat.action}
            >
              {stat.isAction ? (
                <div className="text-center">
                  <div className="text-3xl mb-3">{stat.icon}</div>
                  <h3 className="text-lg font-medium text-gray-800 mb-2">{stat.title}</h3>
                  <p className="text-sm text-gray-600 mb-4">{stat.description}</p>
            <motion.button 
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.98 }}
                    className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all"
            >
                    <FaFileUpload className="mr-2" />
              Submit Paper
            </motion.button>
                </div>
              ) : (
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className={`text-2xl font-bold ${stat.color} mt-1`}>{stat.value}</p>
                    <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
                  </div>
                  <div className="text-2xl opacity-75">
                    {stat.icon}
                  </div>
                </div>
              )}
            </motion.div>
          ))}
          </motion.div>

        {/* Payment Alert Section */}
        {stats.paymentRequired > 0 && (
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-xl p-6 mb-8"
          >
            <div className="flex items-start">
              <FaCreditCard className="text-2xl text-orange-500 mr-4 mt-1" />
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-orange-800 mb-2">
                  Payment Required for {stats.paymentRequired} Manuscript{stats.paymentRequired > 1 ? 's' : ''}
                </h3>
                <p className="text-orange-700 mb-4">
                  Congratulations! Your manuscript{stats.paymentRequired > 1 ? 's have' : ' has'} been accepted for publication. 
                  Please complete the payment to proceed with publication.
                </p>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-bold text-orange-800">
                      {stats.totalPaymentsDue.toLocaleString()} CFA
                    </p>
                    <p className="text-sm text-orange-600">Total Amount Due</p>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => navigate('/author/submissions?filter=payment_pending')}
                    className="inline-flex items-center px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-all shadow-lg"
                  >
                    <FaCreditCard className="mr-2" />
                    Make Payment
                  </motion.button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
          
        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Submission Activity Chart */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white p-6 rounded-xl shadow-md border border-gray-100"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800">Submission Activity</h3>
              <div className="text-sm text-gray-500">
                Last 6 months
              </div>
            </div>
            
            {stats.total > 0 ? (
              <div className="h-64">
                <Line data={submissionChartData} options={chartOptions} />
              </div>
            ) : (
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <FaChartLine className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">No Submissions Yet</h4>
                  <p className="text-gray-500">
                    Submit your first manuscript to see your activity graph here.
                  </p>
                </div>
              </div>
            )}
          </motion.div>
          
          {/* Status Distribution Chart */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white p-6 rounded-xl shadow-md border border-gray-100"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800">Status Distribution</h3>
              <div className="text-sm text-gray-500">
                Current status
              </div>
            </div>
            
            {stats.total > 0 ? (
              <div className="h-64">
                <Doughnut data={statusChartData} options={doughnutOptions} />
              </div>
            ) : (
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <FaBook className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">No Data Available</h4>
                  <p className="text-gray-500">
                    Submit manuscripts to see status distribution.
                  </p>
                </div>
              </div>
            )}
          </motion.div>
        </div>
        
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden"
        >
          <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 flex flex-col md:flex-row justify-between items-start md:items-center">
            <h2 className="text-lg font-medium text-gray-800 mb-3 md:mb-0">Recent Submissions</h2>
            <motion.div 
              whileHover={{ scale: 1.02 }}
              className="relative w-full md:w-64"
            >
              <input
                type="text"
                placeholder="Search submissions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
              />
              <FaEye className="absolute left-3 top-3 text-gray-400" />
            </motion.div>
          </div>
          <div className="divide-y divide-gray-200">
            {filteredManuscripts.length > 0 ? (
              filteredManuscripts.slice(0, 10).map((manuscript, index) => (
              <motion.div 
                key={manuscript.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * index }}
                whileHover={{ backgroundColor: "rgba(249, 250, 251, 1)" }}
                  className="p-6 transition-colors duration-200 cursor-pointer"
                  onClick={() => navigate(`/author/submissions/${manuscript.id}`)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                        manuscript.status === 'under_review' ? 'bg-yellow-100' :
                        manuscript.status === 'published' ? 'bg-green-100' :
                        manuscript.status === 'accepted' ? 'bg-blue-100' : 'bg-red-100'
                      }`}>
                    {getStatusIcon(manuscript.status)}
                  </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <h3 className="text-lg font-medium text-gray-900 truncate">
                            {manuscript.title}
                          </h3>
                        </div>
                    <div className="flex flex-wrap items-center gap-3 mt-2 text-sm text-gray-600">
                      <span className="inline-flex items-center">
                        <span className="text-gray-500 mr-1">ID:</span> {manuscript.submission_id}
                      </span>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(manuscript.status)}`}>
                        {manuscript.status.replace('_', ' ').toUpperCase()}
                      </span>
                      <span className="inline-flex items-center">
                            <FaCalendarAlt className="mr-1" />
                            <span className="text-gray-500 mr-1">Submitted:</span> 
                            {new Date(manuscript.created_at).toLocaleDateString()}
                      </span>
                      {manuscript.issue && (
                        <span className="inline-flex items-center">
                          <span className="text-gray-500 mr-1">Target Issue:</span> {manuscript.issue.type} {manuscript.issue.number}
                        </span>
                          )}
                        </div>
                        {manuscript.author && (
                          <div className="flex items-center mt-1 text-sm text-gray-500">
                            <FaUser className="mr-1" />
                            <span>by {manuscript.author.name}</span>
                            {manuscript.author.affiliation && (
                              <span className="ml-2">({manuscript.author.affiliation})</span>
                            )}
                          </div>
                      )}
                    </div>
                  </div>
                  <motion.button 
                    whileHover={{ x: 3 }}
                      whileTap={{ scale: 0.98 }}
                      className="flex items-center px-4 py-2 rounded-lg text-blue-600 hover:bg-blue-50 transition-colors"
                  >
                    View Details
                    <FaChevronRight className="ml-1" />
                  </motion.button>
                </div>
              </motion.div>
              ))
            ) : (
              <div className="p-8 text-center text-gray-500">
                <FaFileAlt className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No submissions found</h3>
                <p>
                  {searchQuery 
                    ? "No submissions match your search criteria."
                    : "You haven't submitted any manuscripts yet."
                  }
                </p>
                {!searchQuery && (
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => navigate('/author/submit')}
                    className="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <FaFileUpload className="mr-2" />
                    Submit Your First Paper
                  </motion.button>
                )}
              </div>
            )}
          </div>
        </motion.div>

        {/* Quick Actions */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mt-8 bg-white p-6 rounded-xl shadow-md border border-gray-100"
        >
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => navigate('/author/submit')}
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all"
            >
              <FaFileUpload className="text-blue-500 mr-3" />
              <div className="text-left">
                <p className="font-medium text-gray-900">Submit New Paper</p>
                <p className="text-sm text-gray-500">Start a new submission</p>
              </div>
            </motion.button>
            
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => navigate('/author/manuscripts')}
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-all"
            >
              <FaFileAlt className="text-green-500 mr-3" />
              <div className="text-left">
                <p className="font-medium text-gray-900">View All Manuscripts</p>
                <p className="text-sm text-gray-500">Manage submissions</p>
              </div>
            </motion.button>
            
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => navigate('/author/profile')}
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-all"
            >
              <FaUser className="text-purple-500 mr-3" />
              <div className="text-left">
                <p className="font-medium text-gray-900">Update Profile</p>
                <p className="text-sm text-gray-500">Manage your information</p>
              </div>
            </motion.button>
          </div>
        </motion.div>
      </div>
    </>
  );
};

export default AuthorDashboard;