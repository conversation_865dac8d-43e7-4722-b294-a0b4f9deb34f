/**
 * <AUTHOR> (andrew21-mch)
 * @github https://github.com/andrew21-mch
 * @description Author Profile Component - Displays author information and manuscript statistics
 */

import { useState, useEffect } from 'react';
import { FaFileAlt, FaUser, FaUniversity, FaGlobe, FaBook, FaPhone, FaBriefcase, FaGraduationCap, FaUserEdit, FaSave, FaEdit } from 'react-icons/fa';
import { manuscriptService } from '../../services/manuscriptService';
import userService from '../../services/userService';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { toast } from 'react-hot-toast';
import { motion as Motion } from 'framer-motion'; 

export default function Profile() {
  const { user, setUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    affiliation: '',
    country: '',
    position: '',
    education: '',
    bio: '',
    research_interests: '',
    orcid: '',
    website: ''
  });
  const [stats, setStats] = useState({
    total: 0,
    underReview: 0,
    published: 0,
    rejected: 0,
    successRate: 0
  });

  useEffect(() => {
    fetchManuscripts();
    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        phone: user.phone || '',
        affiliation: user.affiliation || '',
        country: user.country || '',
        position: user.position || '',
        education: user.education || '',
        bio: user.bio || '',
        research_interests: user.research_interests || '',
        orcid: user.orcid || '',
        website: user.website || ''
      });
    }
  }, [user]);

  const fetchManuscripts = async () => {
    try {
      setLoading(true);
      const response = await manuscriptService.getAuthorManuscripts();
      const manuscripts = response.data || [];

      // Calculate statistics
      const underReview = manuscripts.filter(m => m.status === 'under_review').length;
      const published = manuscripts.filter(m => m.status === 'published').length;
      const rejected = manuscripts.filter(m => m.status === 'rejected').length;

      setStats({
        total: manuscripts.length,
        underReview,
        published,
        rejected,
        successRate: manuscripts.length > 0 ? ((published / manuscripts.length) * 100).toFixed(1) : 0
      });
    } catch (error) {
      console.error('Error fetching manuscripts:', error);
      setError('Failed to fetch manuscripts. Please try again later.');
      toast.error('Failed to fetch manuscripts');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);

    try {
      console.log('Submitting profile data:', formData);
      const response = await userService.updateProfile(formData);
      console.log('Profile update response:', response);
      
      // Update the user context with the new data
      if (setUser && response.data) {
        setUser(response.data);
        // Also update localStorage
        localStorage.setItem('user', JSON.stringify(response.data));
      }
      
      toast.success('Profile updated successfully!');
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error(error.response?.data?.message || 'Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <FaUser className="mx-auto h-12 w-12 text-red-500" />
          <h2 className="mt-4 text-xl font-semibold text-gray-900">Error Loading Profile</h2>
          <p className="mt-2 text-sm text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* 
        Developer: Nfon Andrew (andrew21-mch)
        GitHub: https://github.com/andrew21-mch
        Component: Author Profile
        Description: Displays author information and manuscript statistics
      */}
      <div className="max-w-9xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Author Profile</h1>
              <p className="text-gray-600 mt-2">Manage your author information and manuscript statistics</p>
            </div>
            <div className="flex items-center space-x-4">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800 capitalize">
              {user?.role || 'author'}
            </span>
              <Motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setIsEditing(!isEditing)}
                className={`inline-flex items-center px-4 py-2 rounded-lg transition-colors ${
                  isEditing 
                    ? 'bg-gray-600 text-white hover:bg-gray-700' 
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {isEditing ? <FaSave className="mr-2" /> : <FaEdit className="mr-2" />}
                {isEditing ? 'Cancel' : 'Edit Profile'}
              </Motion.button>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Submissions Card */}
            <Motion.div 
              whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)" }}
              className="bg-white p-6 rounded-xl shadow-md border border-gray-100 hover:border-transparent transition-all"
            >
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium text-gray-600">Total Submissions</h3>
                  <p className="text-2xl font-bold text-indigo-600 mt-1">{stats.total}</p>
                  <p className="text-xs text-gray-500 mt-1">All manuscripts</p>
                </div>
                <FaFileAlt className="text-2xl text-indigo-500 opacity-75" />
              </div>
            </Motion.div>
            
            {/* Active Submissions Card */}
            <Motion.div 
              whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)" }}
              className="bg-white p-6 rounded-xl shadow-md border border-gray-100 hover:border-transparent transition-all"
            >
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium text-gray-600">Under Review</h3>
                  <p className="text-2xl font-bold text-yellow-600 mt-1">{stats.underReview}</p>
                  <p className="text-xs text-gray-500 mt-1">Currently reviewing</p>
                </div>
                <FaFileAlt className="text-2xl text-yellow-500 opacity-75" />
              </div>
            </Motion.div>
            
            {/* Published Papers Card */}
            <Motion.div 
              whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)" }}
              className="bg-white p-6 rounded-xl shadow-md border border-gray-100 hover:border-transparent transition-all"
            >
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium text-gray-600">Published</h3>
                  <p className="text-2xl font-bold text-green-600 mt-1">{stats.published}</p>
                  <p className="text-xs text-gray-500 mt-1">Success rate: {stats.successRate}%</p>
                </div>
                <FaBook className="text-2xl text-green-500 opacity-75" />
              </div>
            </Motion.div>

            {/* Rejected Papers Card */}
            <Motion.div 
              whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)" }}
              className="bg-white p-6 rounded-xl shadow-md border border-gray-100 hover:border-transparent transition-all"
            >
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium text-gray-600">Rejected</h3>
                  <p className="text-2xl font-bold text-red-600 mt-1">{stats.rejected || 0}</p>
                  <p className="text-xs text-gray-500 mt-1">For improvement</p>
                </div>
                <FaFileAlt className="text-2xl text-red-500 opacity-75" />
              </div>
            </Motion.div>
          </div>

          {/* Profile Form */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-5 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center">
                <FaUserEdit className="text-gray-600 mr-3" />
              <h3 className="text-lg font-medium text-gray-900">Profile Information</h3>
              </div>
            </div>
            <form onSubmit={handleSubmit} className="divide-y divide-gray-200">
              <div className="px-6 py-5 space-y-6">
                {/* Basic Information */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-4 flex items-center">
                    <FaUser className="mr-2 text-blue-500" />
                    Basic Information
                  </h4>
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                        Full Name *
                    </label>
                    <input
                      type="text"
                      name="name"
                      id="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        required
                        className={`mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ${
                          !isEditing ? 'bg-gray-50' : ''
                        }`}
                    />
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                        Email Address *
                    </label>
                    <input
                      type="email"
                      name="email"
                      id="email"
                        value={formData.email}
                        onChange={handleInputChange}
                      disabled
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 bg-gray-50 sm:text-sm"
                    />
                      <p className="mt-1 text-xs text-gray-500">Email cannot be changed</p>
                  </div>

                  <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                        <FaPhone className="inline mr-1" />
                        Phone Number
                    </label>
                    <input
                        type="tel"
                        name="phone"
                        id="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className={`mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ${
                          !isEditing ? 'bg-gray-50' : ''
                        }`}
                    />
                  </div>

                  <div>
                    <label htmlFor="country" className="block text-sm font-medium text-gray-700">
                        <FaGlobe className="inline mr-1" />
                      Country
                    </label>
                    <input
                      type="text"
                      name="country"
                      id="country"
                        value={formData.country}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className={`mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ${
                          !isEditing ? 'bg-gray-50' : ''
                        }`}
                      />
                    </div>
                  </div>
                </div>

                {/* Professional Information */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-4 flex items-center">
                    <FaBriefcase className="mr-2 text-green-500" />
                    Professional Information
                  </h4>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label htmlFor="affiliation" className="block text-sm font-medium text-gray-700">
                        <FaUniversity className="inline mr-1" />
                        Institution/Affiliation
                      </label>
                      <input
                        type="text"
                        name="affiliation"
                        id="affiliation"
                        value={formData.affiliation}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className={`mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ${
                          !isEditing ? 'bg-gray-50' : ''
                        }`}
                      />
                    </div>

                    <div>
                      <label htmlFor="position" className="block text-sm font-medium text-gray-700">
                        Position/Title
                      </label>
                      <input
                        type="text"
                        name="position"
                        id="position"
                        value={formData.position}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        placeholder="e.g., Professor, Research Fellow, PhD Student"
                        className={`mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ${
                          !isEditing ? 'bg-gray-50' : ''
                        }`}
                      />
                    </div>

                    <div>
                      <label htmlFor="education" className="block text-sm font-medium text-gray-700">
                        <FaGraduationCap className="inline mr-1" />
                        Highest Education
                      </label>
                      <input
                        type="text"
                        name="education"
                        id="education"
                        value={formData.education}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        placeholder="e.g., PhD in Computer Science"
                        className={`mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ${
                          !isEditing ? 'bg-gray-50' : ''
                        }`}
                      />
                    </div>

                    <div>
                      <label htmlFor="orcid" className="block text-sm font-medium text-gray-700">
                        ORCID ID
                      </label>
                      <input
                        type="text"
                        name="orcid"
                        id="orcid"
                        value={formData.orcid}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        placeholder="0000-0000-0000-0000"
                        className={`mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ${
                          !isEditing ? 'bg-gray-50' : ''
                        }`}
                      />
                    </div>

                    <div className="sm:col-span-2">
                      <label htmlFor="website" className="block text-sm font-medium text-gray-700">
                        Website/Portfolio
                      </label>
                      <input
                        type="url"
                        name="website"
                        id="website"
                        value={formData.website}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        placeholder="https://example.com"
                        className={`mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ${
                          !isEditing ? 'bg-gray-50' : ''
                        }`}
                      />
                    </div>
                  </div>
                </div>

                {/* Research Information */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-4 flex items-center">
                    <FaBook className="mr-2 text-purple-500" />
                    Research Information
                  </h4>
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="research_interests" className="block text-sm font-medium text-gray-700">
                        Research Interests
                      </label>
                      <textarea
                        name="research_interests"
                        id="research_interests"
                        rows={3}
                        value={formData.research_interests}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        placeholder="Describe your research interests and areas of expertise..."
                        className={`mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ${
                          !isEditing ? 'bg-gray-50' : ''
                        }`}
                      />
                    </div>

                    <div>
                      <label htmlFor="bio" className="block text-sm font-medium text-gray-700">
                        Biography
                      </label>
                      <textarea
                        name="bio"
                        id="bio"
                        rows={4}
                        value={formData.bio}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        placeholder="Tell us about yourself, your background, and achievements..."
                        className={`mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ${
                          !isEditing ? 'bg-gray-50' : ''
                        }`}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {isEditing && (
                <div className="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
                  <Motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    type="button"
                    onClick={() => setIsEditing(false)}
                    className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Cancel
                  </Motion.button>
                  <Motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  type="submit"
                    disabled={saving}
                    className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                  >
                    {saving ? (
                      <>
                        <LoadingSpinner size="sm" />
                        <span className="ml-2">Saving...</span>
                      </>
                    ) : (
                      <>
                        <FaSave className="mr-2" />
                        Save Changes
                      </>
                    )}
                  </Motion.button>
              </div>
              )}
            </form>
          </div>
        </div>
      </div>
    </>
  );
}