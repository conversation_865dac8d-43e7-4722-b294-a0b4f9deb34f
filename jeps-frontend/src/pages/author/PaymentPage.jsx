import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { FaCreditCard, FaMoneyBillWave, FaCheckCircle, FaSpinner, FaArrowLeft } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { toast } from 'react-toastify';
import { manuscriptService } from '../../services/manuscriptService';
import LoadingSpinner from '../../components/common/LoadingSpinner';

const PaymentPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [manuscript, setManuscript] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState('mobile_money');
  const [paymentData, setPaymentData] = useState({
    phone_number: '',
    provider: 'mtn',
    reference: ''
  });

  useEffect(() => {
    fetchManuscript();
  }, [id]);

  const fetchManuscript = async () => {
    try {
      setLoading(true);
      const response = await manuscriptService.getManuscriptById(id);
      setManuscript(response.data);
    } catch (error) {
      toast.error('Failed to load manuscript details');
      navigate('/author');
    } finally {
      setLoading(false);
    }
  };

  const handlePayment = async (e) => {
    e.preventDefault();
    setProcessing(true);

    try {
      // In a real implementation, this would integrate with a payment gateway
      const response = await fetch(`/api/v1/manuscripts/${id}/payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          payment_method: paymentMethod,
          ...paymentData
        })
      });

      if (response.ok) {
        toast.success('Payment processed successfully!');
        navigate('/author/dashboard');
      } else {
        throw new Error('Payment failed');
      }
    } catch (error) {
      toast.error('Payment processing failed. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const handleInputChange = (e) => {
    setPaymentData({
      ...paymentData,
      [e.target.name]: e.target.value
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!manuscript || manuscript.status !== 'payment_pending') {
    return (
      <div className="max-w-2xl mx-auto py-8 px-4">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
          <h2 className="text-xl font-semibold text-yellow-800 mb-2">Payment Not Required</h2>
          <p className="text-yellow-700">This manuscript does not require payment at this time.</p>
          <button
            onClick={() => navigate('/author')}
            className="mt-4 inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700"
          >
            <FaArrowLeft className="mr-2" />
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-9xl mx-auto py-8 px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-blue-50 rounded-xl shadow-lg overflow-hidden border border-blue-200"
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Publication Fee Payment</h1>
              <p className="text-blue-100 mt-1">Complete your payment to proceed with publication</p>
            </div>
            <FaMoneyBillWave className="text-4xl text-blue-200" />
          </div>
        </div>

        <div className="p-6 bg-blue-50">
          {/* Manuscript Details */}
          <div className="bg-white rounded-lg p-6 mb-6 border border-blue-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Manuscript Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Title</p>
                <p className="font-medium text-gray-900">{manuscript.title}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Submission ID</p>
                <p className="font-medium text-gray-900">{manuscript.submission_id}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Status</p>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                  Accepted - Payment Required
                </span>
              </div>
              <div>
                <p className="text-sm text-gray-600">Publication Fee</p>
                <p className="font-bold text-2xl text-blue-600">75,000 CFA</p>
              </div>
            </div>
          </div>

          {/* Payment Form */}
          <form onSubmit={handlePayment} className="space-y-6">
            {/* Payment Method Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">Payment Method</label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                    paymentMethod === 'mobile_money' 
                      ? 'border-blue-500 bg-blue-100' 
                      : 'border-gray-200 hover:border-gray-300 bg-white'
                  }`}
                  onClick={() => setPaymentMethod('mobile_money')}
                >
                  <div className="flex items-center">
                    <input
                      type="radio"
                      name="payment_method"
                      value="mobile_money"
                      checked={paymentMethod === 'mobile_money'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                      className="h-4 w-4 text-blue-600"
                    />
                    <div className="ml-3">
                      <p className="font-medium text-gray-900">Mobile Money</p>
                      <p className="text-sm text-gray-500">MTN, Orange, Moov</p>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                    paymentMethod === 'bank_transfer' 
                      ? 'border-blue-500 bg-blue-100' 
                      : 'border-gray-200 hover:border-gray-300 bg-white'
                  }`}
                  onClick={() => setPaymentMethod('bank_transfer')}
                >
                  <div className="flex items-center">
                    <input
                      type="radio"
                      name="payment_method"
                      value="bank_transfer"
                      checked={paymentMethod === 'bank_transfer'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                      className="h-4 w-4 text-blue-600"
                    />
                    <div className="ml-3">
                      <p className="font-medium text-gray-900">Bank Transfer</p>
                      <p className="text-sm text-gray-500">Direct bank transfer</p>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>

            {/* Mobile Money Details */}
            {paymentMethod === 'mobile_money' && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="space-y-4"
              >
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Provider</label>
                  <select
                    name="provider"
                    value={paymentData.provider}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-white"
                    required
                  >
                    <option value="mtn">MTN Mobile Money</option>
                    <option value="orange">Orange Money</option>
                    <option value="moov">Moov Money</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                  <input
                    type="tel"
                    name="phone_number"
                    value={paymentData.phone_number}
                    onChange={handleInputChange}
                    placeholder="e.g., +237 6XX XXX XXX"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-white"
                    required
                  />
                </div>
              </motion.div>
            )}

            {/* Bank Transfer Details */}
            {paymentMethod === 'bank_transfer' && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="bg-blue-50 border border-blue-200 rounded-lg p-4"
              >
                <h4 className="font-medium text-blue-900 mb-2">Bank Transfer Details</h4>
                <div className="text-sm text-blue-800 space-y-1">
                  <p><strong>Bank:</strong> Afriland First Bank</p>
                  <p><strong>Account Name:</strong> JEPS Journal</p>
                  <p><strong>Account Number:</strong> 10033 00001 *********** 23</p>
                  <p><strong>Amount:</strong> 75,000 CFA</p>
                  <p><strong>Reference:</strong> {manuscript.submission_id}</p>
                </div>
                <div className="mt-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Transfer Reference</label>
                  <input
                    type="text"
                    name="reference"
                    value={paymentData.reference}
                    onChange={handleInputChange}
                    placeholder="Enter transfer reference number"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-white"
                    required
                  />
                </div>
              </motion.div>
            )}

            {/* Submit Button */}
            <div className="flex items-center justify-between pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={() => navigate('/author')}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                <FaArrowLeft className="mr-2" />
                Back to Dashboard
              </button>
              
              <motion.button
                type="submit"
                disabled={processing}
                whileHover={{ scale: processing ? 1 : 1.02 }}
                whileTap={{ scale: processing ? 1 : 0.98 }}
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {processing ? (
                  <>
                    <FaSpinner className="mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <FaCreditCard className="mr-2" />
                    Complete Payment
                  </>
                )}
              </motion.button>
            </div>
          </form>
        </div>
      </motion.div>
    </div>
  );
};

export default PaymentPage; 