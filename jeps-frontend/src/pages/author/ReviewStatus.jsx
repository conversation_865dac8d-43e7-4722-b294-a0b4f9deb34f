import { useState, useEffect } from 'react';
import { FaCheckCircle, FaClock, FaUserEdit, FaRegCommentDots, FaSpinner, FaEye, FaCalendarAlt, FaUser, FaGraduationCap, FaFileAlt, FaChartLine, FaExclamationTriangle, FaInfoCircle, FaSearch, FaFilter, FaTh, FaList } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import { manuscriptService } from '../../services/manuscriptService';
import LoadingSpinner from '../../components/common/LoadingSpinner';

const ReviewStatus = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [manuscripts, setManuscripts] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [viewMode, setViewMode] = useState('grid'); // grid or list

  useEffect(() => {
    fetchManuscripts();
  }, []);

  const fetchManuscripts = async () => {
    try {
      setLoading(true);
      const data = await manuscriptService.getAuthorManuscripts();
      setManuscripts(data.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching manuscripts:', err);
      setError(err.message || 'Failed to load manuscripts');
      toast.error('Failed to load manuscripts');
    } finally {
      setLoading(false);
    }
  };

  // Group reviews by manuscript
  const groupedReviews = manuscripts.reduce((acc, manuscript) => {
    if (manuscript.reviews && manuscript.reviews.length > 0) {
      acc[manuscript.id] = {
        manuscript: {
          id: manuscript.id,
          title: manuscript.title,
          status: manuscript.status,
          submission_id: manuscript.submission_id,
          created_at: manuscript.created_at,
          target_issue: manuscript.issue
        },
        reviews: manuscript.reviews
      };
    }
    return acc;
  }, {});

  // Calculate review statistics
  const getReviewStats = (reviews) => {
    const completed = reviews.filter(r => r.submitted_at).length;
    const pending = reviews.filter(r => !r.submitted_at).length;
    return {
      completed,
      pending,
      total: reviews.length,
      progress: reviews.length > 0 ? (completed / reviews.length) * 100 : 0
    };
  };

  const getRecommendationColor = (recommendation) => {
    switch (recommendation?.toLowerCase()) {
      case 'accept':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'minor revision':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'major revision':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'reject':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusBadgeColor = (status) => {
    switch (status) {
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'accepted':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'published':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Filter and search functionality
  const manuscriptList = Object.values(groupedReviews);
  const filteredManuscripts = manuscriptList.filter(({ manuscript }) => {
    const matchesStatus = statusFilter === 'all' || manuscript.status === statusFilter;
    const matchesSearch = searchQuery === '' || 
      manuscript.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      manuscript.submission_id?.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <FaExclamationTriangle className="mx-auto h-12 w-12 text-red-500" />
          <h2 className="mt-4 text-xl font-semibold text-gray-900">Error Loading Reviews</h2>
          <p className="mt-2 text-sm text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 py-8">
      <div className="max-w-9xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
      <motion.div 
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Review Status</h1>
              <p className="text-gray-600 mt-2">Track the progress of your manuscript reviews</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="bg-white px-4 py-2 rounded-lg shadow-sm border">
                <div className="flex items-center text-sm text-gray-600">
                  <FaChartLine className="mr-2 text-blue-500" />
                  <span className="font-medium">{filteredManuscripts.length}</span>
                  <span className="ml-1">Manuscripts Under Review</span>
                </div>
              </div>
            </div>
          </div>
      </motion.div>

        {/* Filters and Search */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaSearch className="inline mr-2" />
                Search
              </label>
              <input
                type="text"
                placeholder="Search by title or ID..."
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaFilter className="inline mr-2" />
                Status Filter
              </label>
              <select
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="all">All Statuses</option>
                <option value="under_review">Under Review</option>
                <option value="accepted">Accepted</option>
                <option value="published">Published</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                View Mode
              </label>
              <div className="flex rounded-lg border border-gray-300 overflow-hidden">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`flex-1 px-3 py-2 text-sm flex items-center justify-center transition-all ${
                    viewMode === 'grid' 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <FaTh className="mr-1" />
                  Grid
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`flex-1 px-3 py-2 text-sm flex items-center justify-center transition-all ${
                    viewMode === 'list' 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <FaList className="mr-1" />
                  List
                </button>
              </div>
            </div>
          </div>
        </motion.div>

        {filteredManuscripts.length > 0 ? (
          viewMode === 'grid' ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {filteredManuscripts.map(({ manuscript, reviews }, index) => {
              const stats = getReviewStats(reviews);
              return (
                <motion.div
                  key={manuscript.id}
                    initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 * index }}
                    className="bg-white rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 overflow-hidden"
                  >
                    {/* Card Header */}
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 border-b">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                            {manuscript.title}
                          </h3>
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <span className="font-medium">ID:</span>
                            <span className="font-mono">{manuscript.submission_id}</span>
                          </div>
                        </div>
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getStatusBadgeColor(manuscript.status)}`}>
                          {manuscript.status.replace('_', ' ').toUpperCase()}
                        </span>
                      </div>

                      {/* Progress Bar */}
                      <div className="mb-4">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium text-gray-700">Review Progress</span>
                          <span className="text-sm text-gray-600">{stats.completed}/{stats.total} completed</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <motion.div 
                            className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full"
                            initial={{ width: 0 }}
                            animate={{ width: `${stats.progress}%` }}
                            transition={{ duration: 1, delay: 0.5 }}
                          />
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {stats.pending > 0 ? `${stats.pending} reviews pending` : 'All reviews completed'}
                        </div>
                    </div>

                      {/* Submission Info */}
                      <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center">
                          <FaCalendarAlt className="mr-1" />
                          <span>Submitted: {new Date(manuscript.created_at).toLocaleDateString()}</span>
                        </div>
                        {manuscript.target_issue && (
                          <span className="bg-white px-2 py-1 rounded text-gray-700">
                            {manuscript.target_issue.type} {manuscript.target_issue.number}
                          </span>
                        )}
                    </div>
                  </div>

                    {/* Reviews List */}
                    <div className="p-6">
                      <div className="space-y-4">
                        {reviews.map((review, reviewIndex) => (
                          <motion.div 
                            key={review.id}
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.2 + (reviewIndex * 0.1) }}
                            className={`p-4 rounded-lg border-l-4 transition-all ${
                              review.submitted_at 
                                ? 'bg-green-50 border-green-400' 
                                : 'bg-yellow-50 border-yellow-400'
                            }`}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex items-start space-x-3">
                                <div className={`p-2 rounded-full ${
                                  review.submitted_at ? 'bg-green-100' : 'bg-yellow-100'
                                }`}>
                          {review.submitted_at ? (
                                    <FaCheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                                    <FaClock className="h-4 w-4 text-yellow-600" />
                          )}
                        </div>
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2">
                                    <FaUser className="h-3 w-3 text-gray-400" />
                                    <span className="text-sm font-medium text-gray-900">
                            {review.reviewer?.name || 'Anonymous Reviewer'}
                                    </span>
                                  </div>
                                  {review.reviewer?.affiliation && (
                                    <div className="flex items-center space-x-2 mt-1">
                                      <FaGraduationCap className="h-3 w-3 text-gray-400" />
                                      <span className="text-xs text-gray-600">
                                        {review.reviewer.affiliation}
                            </span>
                                    </div>
                                  )}
                                  
                            {review.submitted_at ? (
                                    <div className="mt-2 space-y-2">
                                      <div className="flex items-center space-x-2 text-xs text-gray-600">
                                        <FaCalendarAlt className="h-3 w-3" />
                                <span>Completed: {new Date(review.submitted_at).toLocaleDateString()}</span>
                                      </div>
                                {review.recommendation && (
                                        <span className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium border ${getRecommendationColor(review.recommendation)}`}>
                                          <FaInfoCircle className="mr-1 h-3 w-3" />
                                    {review.recommendation}
                                  </span>
                                )}
                                    </div>
                                  ) : (
                                    <div className="mt-2">
                                      <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
                                        <FaClock className="mr-1 h-3 w-3" />
                                        Due in {review.days_remaining || 'X'} days
                                      </span>
                                    </div>
                            )}
                          </div>
                              </div>
                              
                              {review.feedback && (
                                <motion.button 
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.95 }}
                                  className="ml-2 flex items-center px-3 py-1 text-xs text-blue-600 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors"
                                >
                                  <FaEye className="mr-1 h-3 w-3" />
                                  View
                                </motion.button>
                              )}
                            </div>
                          </motion.div>
                        ))}
                      </div>

                      {/* Action Buttons */}
                      <div className="mt-6 pt-4 border-t border-gray-100">
                        <motion.button
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => navigate(`/author/submissions/${manuscript.id}`)}
                          className="w-full flex items-center justify-center px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all"
                        >
                          <FaFileAlt className="mr-2 h-4 w-4" />
                          View Full Details
                        </motion.button>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          ) : (
            // List View
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <div className="divide-y divide-gray-200">
                {filteredManuscripts.map(({ manuscript, reviews }, index) => {
                  const stats = getReviewStats(reviews);
                  return (
                    <motion.div
                      key={manuscript.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 * index }}
                      className="p-6 hover:bg-gray-50 transition-colors duration-200"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex-1">
                              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                {manuscript.title}
                              </h3>
                              <div className="flex flex-wrap items-center gap-3 text-sm text-gray-500 mb-3">
                                <span>ID: {manuscript.submission_id}</span>
                                <span>•</span>
                                <span>Submitted: {new Date(manuscript.created_at).toLocaleDateString()}</span>
                                {manuscript.target_issue && (
                                  <>
                                    <span>•</span>
                                    <span>Target: {manuscript.target_issue.type} {manuscript.target_issue.number}</span>
                                  </>
                                )}
                              </div>
                              
                              {/* Progress Bar */}
                              <div className="mb-4">
                                <div className="flex justify-between items-center mb-2">
                                  <span className="text-sm font-medium text-gray-700">Review Progress</span>
                                  <span className="text-sm text-gray-600">{stats.completed}/{stats.total} completed</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <motion.div 
                                    className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full"
                                    initial={{ width: 0 }}
                                    animate={{ width: `${stats.progress}%` }}
                                    transition={{ duration: 1, delay: 0.5 }}
                                  />
                                </div>
                              </div>
                              
                              {/* Reviews Summary */}
                              <div className="space-y-2">
                                {reviews.map((review) => (
                                  <div key={review.id} className="flex items-center justify-between text-sm">
                                    <div className="flex items-center space-x-2">
                                      {review.submitted_at ? (
                                        <FaCheckCircle className="h-4 w-4 text-green-600" />
                                      ) : (
                                        <FaClock className="h-4 w-4 text-yellow-600" />
                                      )}
                                      <span className="text-gray-900">{review.reviewer?.name || 'Anonymous Reviewer'}</span>
                                      {review.recommendation && (
                                        <span className={`px-2 py-1 rounded text-xs ${getRecommendationColor(review.recommendation)}`}>
                                          {review.recommendation}
                                        </span>
                                      )}
                        </div>
                        {review.feedback && (
                                      <button className="text-blue-600 hover:text-blue-800 text-xs">
                                        <FaEye className="h-3 w-3" />
                          </button>
                        )}
                      </div>
                    ))}
                              </div>
                            </div>
                            <div className="flex items-center space-x-4 ml-6">
                              <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getStatusBadgeColor(manuscript.status)}`}>
                                {manuscript.status.replace('_', ' ').toUpperCase()}
                              </span>
                              <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => navigate(`/author/submissions/${manuscript.id}`)}
                                className="inline-flex items-center px-4 py-2 text-blue-600 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors"
                              >
                                <FaFileAlt className="mr-2 h-4 w-4" />
                                View Full Details
                              </motion.button>
                            </div>
                          </div>
                        </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
            </div>
          )
      ) : (
        <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-xl shadow-lg p-12 text-center max-w-md mx-auto"
          >
            <div className="bg-blue-50 rounded-full p-6 w-24 h-24 mx-auto mb-6 flex items-center justify-center">
              <FaFileAlt className="h-12 w-12 text-blue-500" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-3">
              {searchQuery || statusFilter !== 'all' ? 'No Reviews Found' : 'No Reviews Available'}
            </h3>
            <p className="text-gray-600 mb-6">
              {searchQuery || statusFilter !== 'all' 
                ? 'Try adjusting your search or filter criteria to find what you\'re looking for.' 
                : 'You currently don\'t have any manuscripts under review. Once you submit a manuscript and it\'s assigned to reviewers, you\'ll be able to track the review progress here.'}
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => window.location.href = '/author/submit'}
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all"
            >
              <FaFileAlt className="mr-2" />
              {searchQuery || statusFilter !== 'all' ? 'Submit New Manuscript' : 'Submit Your First Manuscript'}
            </motion.button>
        </motion.div>
      )}
      </div>
    </div>
  );
};

export default ReviewStatus;