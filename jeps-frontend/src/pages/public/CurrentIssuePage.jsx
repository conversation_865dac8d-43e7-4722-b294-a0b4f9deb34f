import React, { useState, useEffect } from 'react';
import {
  FaUserGraduate,
  FaDownload,
  FaQuoteLeft,
  FaArrowRight,
  FaCalendar,
  FaBook,
  FaEye,
  FaExternalLinkAlt,
  FaGraduationCap,
  FaBookOpen,
  FaFlask,
  FaBrain,
  FaChartLine,
  FaExclamationTriangle,
} from "react-icons/fa";
import { FiExternalLink } from "react-icons/fi";
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { issueService } from '../../services/issueService';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { showNotification } from '../../utils/notification';

const CurrentIssuePage = () => {
  const { t } = useTranslation();
  const [currentIssue, setCurrentIssue] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const truncateText = (text, maxLength) => {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength) + '...';
  };

  // Background icons with their positions and animations
  const backgroundIcons = [
    { icon: FaGraduationCap, position: "top-1/4 left-1/4", delay: 0 },
    { icon: FaBookOpen, position: "top-1/3 right-1/4", delay: 0.2 },
    { icon: FaFlask, position: "bottom-1/4 left-1/3", delay: 0.4 },
    { icon: FaBrain, position: "bottom-1/3 right-1/3", delay: 0.6 },
    { icon: FaChartLine, position: "top-1/2 left-1/2", delay: 0.8 }
  ];

  // Helper function to format authors safely
  const formatAuthors = (authors) => {
    if (typeof authors === 'string') {
      return authors;
    } else if (Array.isArray(authors)) {
      return authors.map(author => 
        typeof author === 'object' ? author.name : author
      ).join(', ');
    } else if (typeof authors === 'object' && authors?.name) {
      return authors.name;
    } else {
      return 'Unknown Author';
    }
  };

  useEffect(() => {
    const abortController = new AbortController();
    
    const fetchCurrentIssue = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await issueService.getCurrentIssue(abortController.signal);
        setCurrentIssue(data);
      } catch (error) {
        console.error('Error fetching current issue:', error);
        if (!abortController.signal.aborted) {
          const errorMessage = error.message || 'Failed to load current issue';
          setError(errorMessage);
          showNotification('error', errorMessage);
        }
      } finally {
        if (!abortController.signal.aborted) {
          setLoading(false);
        }
      }
    };

    fetchCurrentIssue();
    
    // Cleanup function to cancel request if component unmounts
    return () => {
      abortController.abort();
    };
  }, []);

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error || !currentIssue) {
  return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-yellow-50">
        {/* Hero Section with Image */}
        <div className="relative bg-gradient-to-br from-blue-900 via-blue-700 to-blue-600 overflow-hidden">
          {/* Background Image with Gradient */}
          <div className="absolute inset-0">
            <img
              className="w-full h-full object-cover opacity-30"
              src="/assets/uba.jpeg"
              alt="The University of Bamenda"
            />
            <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-blue-700 to-green-600 mix-blend-multiply opacity-80"></div>
          </div>

          {/* Decorative wave pattern */}
          <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-r from-green-500 via-yellow-400 to-orange-400 opacity-70"></div>

          {/* Animated Background Icons */}
          {backgroundIcons.map(({ icon: Icon, position, delay }, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 0.1, scale: 1 }}
              transition={{ duration: 1, delay }}
              className={`absolute ${position} transform -translate-x-1/2 -translate-y-1/2`}
            >
              <Icon className="w-24 h-24 text-white" />
            </motion.div>
          ))}

          {/* Content */}
          <div className="relative max-w-7xl mx-auto py-24 px-4 sm:py-32 sm:px-6 lg:px-8">
      <motion.div
              initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-center"
      >
              <h1 className="text-4xl font-extrabold text-white sm:text-5xl sm:tracking-tight lg:text-6xl">
          {t('currentIssue.title')}
              </h1>
              <div className="mt-6 max-w-2xl mx-auto">
                <p className="text-xl text-blue-100 mb-4">
                  {error || 'No current issue available'}
                </p>
                
                {/* Retry Button */}
                <button
                  onClick={() => window.location.reload()}
                  className="inline-flex items-center px-6 py-3 border border-white text-base font-medium rounded-md text-white bg-transparent hover:bg-white hover:text-blue-600 transition-colors"
                >
                  <FaExclamationTriangle className="mr-2" />
                  Try Again
                </button>
              </div>
            </motion.div>
          </div>
        </div>
        
        {/* Additional error information */}
        <div className="max-w-4xl mx-auto px-4 py-12">
          <div className="bg-white rounded-lg shadow-lg p-6 text-center border border-orange-200">
            <FaExclamationTriangle className="mx-auto h-12 w-12 text-orange-500 mb-4" />
            <h3 className="text-lg font-semibold text-blue-900 mb-2">
              Unable to Load Current Issue
            </h3>
            <p className="text-gray-700 mb-4">
              {error ? error : 'We couldn\'t find a current issue to display. This might be a temporary problem.'}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => window.location.reload()}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-green-600 transition-colors"
              >
                <FaExclamationTriangle className="mr-2" />
                Retry Loading
              </button>
              <Link
                to="/browse"
                className="inline-flex items-center px-4 py-2 bg-orange-100 text-orange-700 font-medium rounded-lg hover:bg-orange-200 transition-colors"
              >
                <FaBook className="mr-2" />
                Browse All Articles
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Get featured article (first article or null)
  const featuredArticle = currentIssue.manuscripts && currentIssue.manuscripts.length > 0 
    ? currentIssue.manuscripts[0] 
    : null;

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-yellow-50">
      {/* Hero Section with Image */}
      <div className="relative bg-gradient-to-br from-blue-900 via-blue-700 to-blue-600 overflow-hidden">
        {/* Background Image with Gradient */}
        <div className="absolute inset-0">
          <img
            className="w-full h-full object-cover opacity-30"
            src="/assets/uba.jpeg"
            alt="The University of Bamenda"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 mix-blend-multiply"></div>
        </div>

        {/* Animated Background Icons */}
        {backgroundIcons.map(({ icon: Icon, position, delay }, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 0.1, scale: 1 }}
            transition={{ duration: 1, delay }}
            className={`absolute ${position} transform -translate-x-1/2 -translate-y-1/2`}
          >
            <Icon className="w-24 h-24 text-white" />
          </motion.div>
        ))}

        {/* Content */}
        <div className="relative max-w-7xl mx-auto py-24 px-4 sm:py-32 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center"
          >
            <h1 className="text-4xl font-extrabold text-white sm:text-5xl sm:tracking-tight lg:text-6xl">
              {t('currentIssue.title')}
        </h1>
            <p className="mt-6 max-w-2xl mx-auto text-xl text-blue-100">
              Volume {currentIssue.volume?.number || 'N/A'}, Issue {currentIssue.number} • {currentIssue.type} {currentIssue.volume?.year || new Date().getFullYear()}
        </p>
      </motion.div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-9xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Debug Section - Remove this after fixing */}
        {/* <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">Debug Info (Remove after fixing)</h3>
          <div className="text-sm text-yellow-700">
            <p><strong>Current Issue:</strong> {JSON.stringify(currentIssue, null, 2)}</p>
          </div>
        </div> */}

        {/* Featured Article Section */}
        {featuredArticle && (
      <motion.div
            className="bg-white rounded-xl shadow-md overflow-hidden mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="flex flex-col lg:flex-row">
              {/* Image Section */}
              <div className="lg:w-1/3 p-6">
                <img
                  src="/assets/JepsFullPage.png"
                  alt={`JEPS Issue ${currentIssue.number} Cover`}
                  className="w-full h-64 object-contain rounded-2xl shadow-md"
                />
                <div className="mt-4">
                <div className="flex items-center text-sm text-gray-500 mb-2">
                  <span>{t('currentIssue.featuredArticle')}</span>
                </div>
                <button className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                  <FaDownload className="mr-2" />
                  {t('currentIssue.downloadArticle')}
                </button>
            </div>
          </div>

              {/* Content Section */}
              <div className="lg:w-2/3 p-6">
              <div className="flex items-start mb-6">
                  <FaQuoteLeft className="text-blue-200 text-3xl mr-4 mt-1 flex-shrink-0" />
                <h2 className="text-2xl font-bold text-gray-800">
                    {featuredArticle.title}
                </h2>
              </div>

                <div className="flex items-center text-sm text-gray-600 mb-4">
                  <FaUserGraduate className="mr-2" />
                  <span>{formatAuthors(featuredArticle.authors)}</span>
                </div>

              <div className="prose prose-lg text-gray-600 mb-6">
                  <p>{featuredArticle.abstract}</p>
              </div>

              <div className="border-t border-gray-200 pt-6">
                <h3 className="text-sm font-semibold text-gray-500 uppercase mb-2">
                  {t('currentIssue.articleDetails')}
                </h3>
                  <div className="grid grid-cols-1 gap-4 text-sm">
                  <div>
                      <p className="font-medium text-gray-700">{t('currentIssue.published')}:</p>
                      <p>{new Date(featuredArticle.published_at).toLocaleDateString()}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
        )}

      {/* Table of Contents */}
      <motion.div
        className="bg-white rounded-2xl shadow-md border border-gray-200 overflow-hidden mb-12"
          initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
      >
        <div className="px-8 py-6 border-b border-gray-200 bg-gray-50">
          <h2 className="text-2xl font-bold text-gray-800">
            {t('currentIssue.tableOfContents')}
          </h2>
        </div>

        <div className="divide-y divide-gray-200">
            {currentIssue.manuscripts && currentIssue.manuscripts.length > 0 ? (
              currentIssue.manuscripts.map((article, index) => (
                <motion.div
              key={article.id}
              className="p-8 hover:bg-gray-50 transition-colors group"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <div className="flex flex-col md:flex-row gap-6">
                <div className="md:w-3/4">
                  <h3 className="text-xl font-semibold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors">
                    {article.title}
                  </h3>
                  <div className="flex items-center text-sm text-gray-500 mb-3">
                    <FaUserGraduate className="mr-2 flex-shrink-0" />
                        <span>{formatAuthors(article.authors)}</span>
                  </div>
                  <p className="text-gray-600 mb-4">{ truncateText(article.abstract, 200)}</p>
                  {article.keywords && (
                    <div className="flex flex-wrap gap-2 mb-4">
                          {(Array.isArray(article.keywords) 
                            ? article.keywords 
                            : typeof article.keywords === 'string' 
                              ? article.keywords.split(',') 
                              : []
                          ).map((keyword, index) => (
                        <span
                          key={index}
                          className="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-xs"
                        >
                              {typeof keyword === 'string' ? keyword.trim() : keyword}
                        </span>
                      ))}
                    </div>
                  )}
                </div>

                <div className="md:w-1/4 flex flex-col justify-between">
                  <div className="text-sm text-gray-500 space-y-2 mb-4 md:mb-0">
                    <div>
                          <span className="font-medium text-gray-700">{t('currentIssue.published')}:</span>{" "}
                          {new Date(article.published_at).toLocaleDateString()}
                        </div>
                    </div>
                      <Link 
                        to={`/article/${article.id}`}
                        className="flex items-center text-blue-600 hover:text-blue-800 font-medium self-start md:self-end"
                      >
                        {t('currentIssue.readFullArticle')} <FaArrowRight className="ml-2" />
                      </Link>
                    </div>
                  </div>
                </motion.div>
              ))
            ) : (
              <div className="p-8 text-center text-gray-500">
                <FaExclamationTriangle className="text-yellow-500 text-3xl mb-4" />
                {t('currentIssue.noArticles')}
              </div>
            )}
        </div>
      </motion.div>
      </div>
    </div>
  );
};

export default CurrentIssuePage;
