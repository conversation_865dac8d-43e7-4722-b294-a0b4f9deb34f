import { useState, useEffect } from "react";
import { FaSearch, FaCalendarAlt, FaUserGraduate, FaBookOpen, FaFilter, FaDownload, FaGraduationCap, FaFlask, FaBrain, FaChartLine, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { FiExternalLink } from "react-icons/fi";
import { useTranslation } from 'react-i18next';
import { manuscriptService } from '../../services/manuscriptService';
import { motion } from 'framer-motion';
import { Link } from "react-router-dom";

const ArticlePage = () => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedYear, setSelectedYear] = useState("");
  const [selectedIssue, setSelectedIssue] = useState("");
  const [articles, setArticles] = useState([]);
  const [issues, setIssues] = useState([]);
  const [loading, setLoading] = useState(true);
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const itemsPerPage = 12;

  // Background icons with their positions and animations
  const backgroundIcons = [
    { icon: FaGraduationCap, position: "top-1/4 left-1/4", delay: 0 },
    { icon: FaBookOpen, position: "top-1/3 right-1/4", delay: 0.2 },
    { icon: FaFlask, position: "bottom-1/4 left-1/3", delay: 0.4 },
    { icon: FaBrain, position: "bottom-1/3 right-1/3", delay: 0.6 },
    { icon: FaChartLine, position: "top-1/2 left-1/2", delay: 0.8 }
  ];

  // Dummy articles as fallback
  const dummyArticles = [
    {
      id: 1,
      title: "Educational Reforms in Cameroon: A Decade of Progress",
      authors: ["Agborbechem P. Tambi", "Emmanuel Mbebeb"],
      abstract: "This study examines the impact of educational reforms implemented in Cameroon over the past decade, analyzing policy effectiveness through longitudinal data from 2015-2025.",
      date: "2025-01-15",
      volume: 10,
      issue: 1,
      pages: "1-22",
      doi: "10.1234/jeps.2025.001",
      category: "Education Policy",
      keywords: ["education reform", "policy analysis", "Cameroon"],
      downloads: 124,
      citations: 18
    },
    {
      id: 2,
      title: "Psychological Wellbeing of University Students in Post-Pandemic Era",
      authors: ["Mireille Ndje", "Guy-Bertrand Mbarga"],
      abstract: "An investigation into the mental health challenges faced by university students following the COVID-19 pandemic, with comparative data from five Cameroonian universities.",
      date: "2024-06-01",
      volume: 9,
      issue: 2,
      pages: "23-45",
      doi: "10.1234/jeps.2024.002",
      category: "Psychology",
      keywords: ["mental health", "higher education", "post-pandemic"],
      downloads: 89,
      citations: 12
    },
    {
      id: 3,
      title: "Innovative Approaches to STEM Education in Rural Schools",
      authors: ["Lily Chongwain", "Diedonne Nkempaah"],
      abstract: "Case studies of successful STEM education interventions in under-resourced rural schools, focusing on low-cost, high-impact teaching methodologies.",
      date: "2024-01-15",
      volume: 9,
      issue: 1,
      pages: "46-68",
      doi: "10.1234/jeps.2024.003",
      category: "STEM Education",
      keywords: ["STEM", "rural education", "Africa"],
      downloads: 156,
      citations: 24
    },
    {
      id: 4,
      title: "Cognitive Development in Multilingual Classrooms",
      authors: ["Valentine Ngalim", "Joseph Lah Looh"],
      abstract: "Exploring the cognitive benefits and challenges of multilingual education in primary schools across Cameroon's linguistic regions.",
      date: "2023-06-01",
      volume: 8,
      issue: 2,
      pages: "69-92",
      doi: "10.1234/jeps.2023.004",
      category: "Language Education",
      keywords: ["multilingualism", "cognitive development", "primary education"],
      downloads: 78,
      citations: 9
    },
    {
      id: 5,
      title: "Workplace Motivation in African Educational Institutions",
      authors: ["Andrew A. Mogaji", "Nyock Illouga"],
      abstract: "A comparative study of motivational factors for educators in public and private institutions across three African countries.",
      date: "2023-01-15",
      volume: 8,
      issue: 1,
      pages: "93-115",
      doi: "10.1234/jeps.2023.005",
      category: "Organizational Psychology",
      keywords: ["workplace motivation", "education", "Africa"],
      downloads: 67,
      citations: 7
    },
    {
      id: 6,
      title: "Assessment of Inclusive Education Practices in Central Africa",
      authors: ["Patrick Fonyuy Shey", "Nsagha Sarah"],
      abstract: "Evaluation of inclusive education implementation in primary schools, identifying best practices and systemic barriers.",
      date: "2022-06-01",
      volume: 7,
      issue: 2,
      pages: "116-140",
      doi: "10.1234/jeps.2022.006",
      category: "Special Education",
      keywords: ["inclusive education", "disability", "primary schools"],
      downloads: 112,
      citations: 15
    },
    {
      id: 7,
      title: "Digital Learning in Post-COVID African Classrooms",
      authors: ["Sarah Nkwenti", "John Mbah"],
      abstract: "Analysis of digital learning adoption and effectiveness in African educational institutions following the COVID-19 pandemic.",
      date: "2022-01-15",
      volume: 7,
      issue: 1,
      pages: "141-165",
      doi: "10.1234/jeps.2022.007",
      category: "Educational Technology",
      keywords: ["digital learning", "COVID-19", "Africa"],
      downloads: 145,
      citations: 22
    },
    {
      id: 8,
      title: "Early Childhood Education in Rural Communities",
      authors: ["Marie Tchokouani", "Peter Ndifor"],
      abstract: "A comprehensive study of early childhood education programs in rural African communities.",
      date: "2021-06-01",
      volume: 6,
      issue: 2,
      pages: "166-190",
      doi: "10.1234/jeps.2021.008",
      category: "Early Childhood Education",
      keywords: ["early childhood", "rural education", "Africa"],
      downloads: 98,
      citations: 14
    }
  ];

  const truncateText = (text, maxLength = 200) => {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength) + '...';
  };

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch articles with filters
  useEffect(() => {
    const fetchArticles = async () => {
      try {
        setLoading(true);
        const params = new URLSearchParams({
          page: currentPage.toString(),
          per_page: itemsPerPage.toString(),
        });
        
        if (debouncedSearchTerm) {
          params.append('search', debouncedSearchTerm);
        }
        if (selectedCategory) {
          params.append('category', selectedCategory);
        }
        if (selectedYear) {
          params.append('year', selectedYear);
        }
        if (selectedIssue) {
          params.append('issue_id', selectedIssue);
        }

        const response = await manuscriptService.getPublishedManuscripts(params);
        if (response.data && response.data.length > 0) {
          setArticles(response.data);
          setTotalItems(response.total || response.data.length);
          setTotalPages(response.last_page || 1);
        } else {
          // Use dummy articles as fallback, but with pagination
          const startIndex = (currentPage - 1) * itemsPerPage;
          const endIndex = startIndex + itemsPerPage;
          const paginatedDummyArticles = dummyArticles.slice(startIndex, endIndex);
          setArticles(paginatedDummyArticles);
          setTotalItems(dummyArticles.length);
          setTotalPages(Math.ceil(dummyArticles.length / itemsPerPage));
        }
      } catch (err) {
        console.error('Error fetching articles:', err);
        // Use dummy articles as fallback with pagination
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const paginatedDummyArticles = dummyArticles.slice(startIndex, endIndex);
        setArticles(paginatedDummyArticles);
        setTotalItems(dummyArticles.length);
        setTotalPages(Math.ceil(dummyArticles.length / itemsPerPage));
      } finally {
        setLoading(false);
      }
    };

    fetchArticles();
  }, [debouncedSearchTerm, selectedCategory, selectedYear, selectedIssue, currentPage]);

  // Reset to first page when filters change
  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [debouncedSearchTerm, selectedCategory, selectedYear, selectedIssue]);

  // Fetch issues
  useEffect(() => {
    const fetchIssues = async () => {
      try {
        const response = await manuscriptService.getIssues();
        if (response.data) {
          const transformedIssues = response.data.map(issue => ({
            id: issue.id,
            number: issue.number,
            type: issue.type,
            year: issue.volume && typeof issue.volume.year === 'number' ? issue.volume.year : (issue.volume ? issue.volume.year : new Date().getFullYear())
          }));
          setIssues(transformedIssues);
        }
      } catch (err) {
        console.error('Error fetching issues:', err);
      }
    };

    fetchIssues();
  }, []);

  // Extract unique years from articles
  const years = [...new Set(articles.map(article => 
    new Date(article.date || article.published_at).getFullYear()
  ))].sort((a, b) => b - a);

  const categories = [...new Set(articles.map(article => article.category))];

  // Remove client-side filtering since we're using server-side filtering
  const filteredArticles = articles;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50">
      {/* Hero Section with Image */}
      <div className="relative bg-white overflow-hidden">
        {/* Background Image with Gradient */}
        <div className="absolute inset-0">
          <img
            className="w-full h-full object-cover opacity-20"
            src="/assets/uba.jpeg"
            alt="The University of Bamenda"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 mix-blend-multiply"></div>
        </div>

        {/* Animated Background Icons */}
        {backgroundIcons.map(({ icon: Icon, position, delay }, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 0.1, scale: 1 }}
            transition={{ duration: 1, delay }}
            className={`absolute ${position} transform -translate-x-1/2 -translate-y-1/2`}
          >
            <Icon className="w-24 h-24 text-white" />
          </motion.div>
        ))}

        {/* Content */}
        <div className="relative max-w-7xl mx-auto py-24 px-4 sm:py-32 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center"
          >
            <h1 className="text-4xl font-extrabold text-white sm:text-5xl sm:tracking-tight lg:text-6xl">
              {t('browse.title')}
            </h1>
            <p className="mt-6 max-w-2xl mx-auto text-xl text-blue-100">
              {t('browse.subtitle')}
            </p>
          </motion.div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 py-12">
        {/* Search and Filters */}
        <motion.div
          className="max-w-7xl mx-auto mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className="bg-white rounded-xl shadow-md p-6">
            <div className="relative mb-6">
              <input
                type="text"
                placeholder={t('browse.searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-3 pl-12 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('browse.filters.category')}
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">{t('browse.filters.allCategories')}</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('browse.filters.year')}
                </label>
                <select
                  value={selectedYear}
                  onChange={(e) => setSelectedYear(e.target.value)}
                  className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">{t('browse.filters.allYears')}</option>
                  {years.map((year) => (
                    <option key={year} value={year.toString()}>
                      {year}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('browse.filters.issue')}
                </label>
                <select
                  value={selectedIssue}
                  onChange={(e) => setSelectedIssue(e.target.value)}
                  className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">{t('browse.filters.allIssues')}</option>
                  {issues.map((issue) => (
                    <option key={issue.id} value={issue.id.toString()}>
                      Issue {issue.number} ({issue.type} {issue.year})
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Articles Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          {loading ? (
            // Loading skeleton
            Array(6).fill(0).map((_, index) => (
              <div key={index} className="bg-white rounded-xl shadow-md p-6 animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 mb-3"></div>
                <div className="h-3 bg-gray-200 rounded w-full mb-3"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            ))
          ) : (
            filteredArticles.map((article, index) => (
              <motion.div
                key={article.id}
                className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">
                    {article.title}
                  </h3>
                  <div className="flex items-center text-sm text-gray-500 mb-3">
                    <FaUserGraduate className="mr-2" />
                    <span>
                      {t('browse.article.authors')}:{" "}
                      {article.authors.map(author => 
                        typeof author === 'object' ? author.name : author
                      ).join(", ")}
                    </span>
                  </div>
                  <p className="text-gray-600 mb-4">
                    <span className="font-medium">{t('browse.article.abstract')}:</span>{" "}
                    {truncateText(article.abstract, 200)}
                  </p>
                  <div className="flex items-center text-sm text-gray-500 mb-4">
                    <FaCalendarAlt className="mr-2" />
                    <span>
                      {t('browse.article.published')}:{" "}
                      {new Date(article.date || article.published_at).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    {article.doi && (
                      <a
                        href={`https://doi.org/${article.doi}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline flex items-center"
                      >
                        {t('browse.article.doi')}: {article.doi.split("/").pop()}{" "}
                        <FiExternalLink className="ml-1" />
                      </a>
                    )}
                    <Link to={`/article/${article.id}`} className="flex items-center text-blue-600 hover:text-blue-800 font-medium">
                      {t('browse.article.read')} <FaDownload className="ml-2" />
                    </Link>
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </motion.div>

        {/* No Results Message */}
        {!loading && filteredArticles.length === 0 && (
          <motion.div
            className="text-center py-12"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <p className="text-gray-600 text-lg">
              {t('browse.noResults')}
            </p>
          </motion.div>
        )}

        {/* Results Summary */}
        {!loading && articles.length > 0 && (
          <div className="mt-8 mb-6">
            <p className="text-sm text-gray-600 text-center">
              Showing {Math.min((currentPage - 1) * itemsPerPage + 1, totalItems)} to {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} articles
            </p>
          </div>
        )}

        {/* Pagination Controls */}
        {!loading && totalPages > 1 && (
          <div className="mt-8 mb-12 flex justify-center">
            <div className="bg-white rounded-lg shadow-md p-4 flex items-center space-x-4">
              <button
                onClick={() => {
                  setCurrentPage(Math.max(1, currentPage - 1));
                  window.scrollTo({ top: 0, behavior: 'smooth' });
                }}
                disabled={currentPage === 1}
                className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FaChevronLeft className="mr-2" />
                Previous
              </button>
              
              <div className="flex items-center space-x-2">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }
                  
                  return (
                    <button
                      key={pageNum}
                      onClick={() => {
                        setCurrentPage(pageNum);
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                      }}
                      className={`px-3 py-2 text-sm font-medium rounded-lg ${
                        currentPage === pageNum
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>
              
              <button
                onClick={() => {
                  setCurrentPage(Math.min(totalPages, currentPage + 1));
                  window.scrollTo({ top: 0, behavior: 'smooth' });
                }}
                disabled={currentPage === totalPages}
                className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
                <FaChevronRight className="ml-2" />
              </button>
            </div>
          </div>
        )}

        {/* Archive Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="mt-16 bg-white rounded-xl shadow-md p-8 text-center"
        >
          <h3 className="text-xl font-semibold text-gray-800 mb-4">{t('browse.archive.title')}</h3>
          <p className="text-gray-600 mb-6 max-w-3xl mx-auto">
            {t('browse.archive.description')}
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <div className="bg-blue-50 p-4 rounded-lg shadow-sm border border-blue-200 min-w-[200px]">
              <div className="text-3xl font-bold text-blue-600 mb-2">{articles.length}+</div>
              <div className="text-gray-700">{t('browse.archive.stats.articles')}</div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg shadow-sm border border-blue-200 min-w-[200px]">
              <div className="text-3xl font-bold text-blue-600 mb-2">12+</div>
              <div className="text-gray-700">{t('browse.archive.stats.specialIssues')}</div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg shadow-sm border border-blue-200 min-w-[200px]">
              <div className="text-3xl font-bold text-blue-600 mb-2">50+</div>
              <div className="text-gray-700">{t('browse.archive.stats.countries')}</div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

const ArticleCard = ({ article }) => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow h-full flex flex-col">
      <div className="p-6 flex-1">
        <div className="flex items-center text-sm text-gray-500 mb-3">
          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
            {article.category}
          </span>
          <span className="mx-2">•</span>
          <FaCalendarAlt className="mr-1" />
          <span>{new Date(article.date).toLocaleDateString('en-US', { year: 'numeric', month: 'short' })}</span>
        </div>
        
        <h2 className="text-xl font-semibold text-gray-800 mb-3 hover:text-blue-600 transition-colors">
          {article.title}
        </h2>
        
        <div className="flex items-center text-sm text-gray-600 mb-4">
          <FaUserGraduate className="mr-2 flex-shrink-0" />
          <span className="line-clamp-1">{article.authors.join(", ")}</span>
        </div>
        
        <p className="text-gray-600 mb-4 line-clamp-3">
          {article.abstract}
        </p>
        
        <div className="flex flex-wrap gap-2 mb-4">
          {article.keywords.slice(0, 3).map((keyword, index) => (
            <span key={index} className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">
              {keyword}
            </span>
          ))}
        </div>
      </div>
      
      <div className="border-t border-gray-200 p-4 bg-gray-50">
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-500">
            Vol. {article.volume}, Iss. {article.issue} • pp. {article.pages}
          </div>
          <div className="flex space-x-3">
            <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
              <FaBookOpen className="inline mr-1" /> Read
            </button>
            <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
              <FaDownload className="inline mr-1" /> PDF
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ArticlePage;