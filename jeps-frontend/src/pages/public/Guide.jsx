const GuidelinesPage = () => {
    return (
      <motion.div
        className="max-w-4xl mx-auto px-4 py-12"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          className="text-center mb-12"
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-3xl font-bold text-gray-800 mb-4">
            Submission Guidelines
          </h1>
          <p className="text-lg text-gray-600">
            Essential information for authors preparing to submit to JEPS Journal
          </p>
        </motion.div>
  
        <motion.div
          className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-12"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.7 }}
        >
          <div className="p-6 md:p-8">
            {/* Manuscript Preparation */}
            <motion.div
              className="mb-10"
              initial={{ x: -100, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
            >
              <div className="flex items-center mb-6">
                <div className="bg-blue-100 p-3 rounded-full mr-4">
                  <FaFileWord className="text-blue-600 text-xl" />
                </div>
                <h2 className="text-2xl font-semibold text-gray-800">
                  Manuscript Preparation
                </h2>
              </div>
  
              <ul className="space-y-4 pl-2">
                <li className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 text-blue-600 mr-3 mt-0.5">
                    •
                  </div>
                  <div>
                    <span className="font-medium text-gray-800">
                      File Format:
                    </span>{" "}
                    Submissions must be in{" "}
                    <span className="font-semibold">MS Word (.doc, .docx)</span>{" "}
                    format
                  </div>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 text-blue-600 mr-3 mt-0.5">
                    •
                  </div>
                  <div>
                    <span className="font-medium text-gray-800">Abstract:</span>{" "}
                    Include an abstract of no more than{" "}
                    <span className="font-semibold">250 words</span>
                  </div>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 text-blue-600 mr-3 mt-0.5">
                    •
                  </div>
                  <div>
                    <span className="font-medium text-gray-800">Keywords:</span>{" "}
                    Provide <span className="font-semibold">3-5 keywords</span>{" "}
                    separated by commas
                  </div>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 text-blue-600 mr-3 mt-0.5">
                    •
                  </div>
                  <div>
                    <span className="font-medium text-gray-800">Formatting:</span>{" "}
                    Follow{" "}
                    <span className="font-semibold">APA (7th edition)</span> style
                    <a
                      href="https://apastyle.apa.org"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="ml-2 inline-flex items-center text-blue-600 hover:underline text-sm"
                    >
                      APA Style Guide <FiExternalLink className="ml-1" />
                    </a>
                  </div>
                </li>
              </ul>
            </motion.div>
  
            {/* Submission Process */}
            <motion.div
              className="mb-10"
              initial={{ x: 100, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
            >
              <div className="flex items-center mb-6">
                <div className="bg-blue-100 p-3 rounded-full mr-4">
                  <FaUserEdit className="text-blue-600 text-xl" />
                </div>
                <h2 className="text-2xl font-semibold text-gray-800">
                  Submission Process
                </h2>
              </div>
  
              <ol className="space-y-6 pl-2">
                <li className="flex">
                  <div className="flex-shrink-0 bg-blue-600 text-white font-bold rounded-full h-8 w-8 flex items-center justify-center mr-4 mt-0.5">
                    1
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-800 mb-1">
                      Author Registration
                    </h3>
                    <p className="text-gray-600">
                      Create an author account on our submission platform
                    </p>
                  </div>
                </li>
                <li className="flex">
                  <div className="flex-shrink-0 bg-blue-600 text-white font-bold rounded-full h-8 w-8 flex items-center justify-center mr-4 mt-0.5">
                    2
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-800 mb-1">
                      Submit Manuscript
                    </h3>
                    <div className="text-gray-600">
                      <p>Upload your manuscript by the deadline:</p>
                      <div className="flex flex-wrap gap-4 mt-2">
                        <div className="flex items-center bg-blue-50 px-3 py-1 rounded-full">
                          <FaCalendarAlt className="text-blue-600 mr-2" />
                          <span>April 15 for June issue</span>
                        </div>
                        <div className="flex items-center bg-blue-50 px-3 py-1 rounded-full">
                          <FaCalendarAlt className="text-blue-600 mr-2" />
                          <span>November 15 for January issue</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
                <li className="flex">
                  <div className="flex-shrink-0 bg-blue-600 text-white font-bold rounded-full h-8 w-8 flex items-center justify-center mr-4 mt-0.5">
                    3
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-800 mb-1">
                      Publication Fee
                    </h3>
                    <div className="text-gray-600 flex items-center">
                      <FaMoneyBillWave className="mr-2 text-green-600" />
                      <span>
                        75,000 CFA (Central African Franc) payable upon acceptance
                      </span>
                    </div>
                  </div>
                </li>
              </ol>
            </motion.div>
  
            {/* Review Process */}
            <motion.div
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
            >
              <div className="flex items-center mb-6">
                <div className="bg-blue-100 p-3 rounded-full mr-4">
                  <FaSearch className="text-blue-600 text-xl" />
                </div>
                <h2 className="text-2xl font-semibold text-gray-800">
                  Review Process
                </h2>
              </div>
  
              <div className="bg-blue-50 rounded-lg p-6">
                <div className="flex items-start">
                  <div className="flex-shrink-0 mt-1">
                    <FaClock className="text-blue-600 text-xl mr-4" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-800 mb-2">
                      Double-Blind Peer Review
                    </h3>
                    <p className="text-gray-600 mb-4">
                      All submissions undergo rigorous peer review where both
                      reviewer and author identities are concealed. The process
                      typically takes 4-6 weeks from submission date.
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-white p-4 rounded-lg border border-gray-200">
                        <h4 className="font-medium text-gray-800 mb-1">
                          Initial Screening
                        </h4>
                        <p className="text-sm text-gray-600">
                          Editorial review for basic requirements
                        </p>
                      </div>
                      <div className="bg-white p-4 rounded-lg border border-gray-200">
                        <h4 className="font-medium text-gray-800 mb-1">
                          Peer Review
                        </h4>
                        <p className="text-sm text-gray-600">
                          Evaluation by 2-3 experts in the field
                        </p>
                      </div>
                      <div className="bg-white p-4 rounded-lg border border-gray-200">
                        <h4 className="font-medium text-gray-800 mb-1">
                          Final Decision
                        </h4>
                        <p className="text-sm text-gray-600">
                          Editor makes publication decision
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
  
        <motion.div
          className="bg-blue-600 rounded-xl p-6 text-white"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-xl font-semibold mb-4">Ready to Submit?</h2>
          <p className="mb-6">
            Ensure your manuscript meets all requirements before submission
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <a
              href="/submit-paper"
              className="px-6 py-3 bg-white text-blue-600 font-medium rounded-lg hover:bg-gray-100 text-center transition-colors"
            >
              Begin Submission
            </a>
            <a
              href="/contact"
              className="px-6 py-3 border border-white text-white font-medium rounded-lg hover:bg-blue-700 text-center transition-colors"
            >
              Contact Editorial Team
            </a>
          </div>
        </motion.div>
      </motion.div>
    );
  };
  