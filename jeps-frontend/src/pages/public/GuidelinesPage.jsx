import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import {
  FaFileWord,
  FaCalendarAlt,
  FaMoneyBillWave,
  FaUserEdit,
  FaSearch,
  FaClock,
  FaArrowRight,
  FaCheck,
  FaFileUpload,
  FaUserCheck,
  FaComments,
  FaFileAlt,
  FaFileSignature,
  FaGraduationCap,
  FaBookOpen,
  FaFlask,
  FaBrain,
  FaChartLine,
} from "react-icons/fa";
import { FiExternalLink } from "react-icons/fi";

const GuidelinesPage = () => {
  const { t } = useTranslation();

  // Background icons with their positions and animations
  const backgroundIcons = [
    { icon: FaGraduationCap, position: "top-1/4 left-1/4", delay: 0 },
    { icon: FaBookOpen, position: "top-1/3 right-1/4", delay: 0.2 },
    { icon: FaFlask, position: "bottom-1/4 left-1/3", delay: 0.4 },
    { icon: FaBrain, position: "bottom-1/3 right-1/3", delay: 0.6 },
    { icon: FaChartLine, position: "top-1/2 left-1/2", delay: 0.8 }
  ];

  const steps = [
    {
      title: t("guidelines.processOverview.steps.onboarding.title"),
      icon: <FaUserCheck className="text-blue-600 text-xl" />,
      description: t("guidelines.processOverview.steps.onboarding.description"),
    },
    {
      title: t("guidelines.processOverview.steps.prepare.title"),
      icon: <FaFileAlt className="text-blue-600 text-xl" />,
      description: t("guidelines.processOverview.steps.prepare.description"),
    },
    {
      title: t("guidelines.processOverview.steps.submit.title"),
      icon: <FaFileUpload className="text-blue-600 text-xl" />,
      description: t("guidelines.processOverview.steps.submit.description"),
    },
    {
      title: t("guidelines.processOverview.steps.initialReview.title"),
      icon: <FaFileSignature className="text-blue-600 text-xl" />,
      description: t("guidelines.processOverview.steps.initialReview.description"),
    },
    {
      title: t("guidelines.processOverview.steps.peerReview.title"),
      icon: <FaComments className="text-blue-600 text-xl" />,
      description: t("guidelines.processOverview.steps.peerReview.description"),
    },
    {
      title: t("guidelines.processOverview.steps.revisions.title"),
      icon: <FaFileWord className="text-blue-600 text-xl" />,
      description: t("guidelines.processOverview.steps.revisions.description"),
    },
    {
      title: t("guidelines.processOverview.steps.decision.title"),
      icon: <FaCheck className="text-blue-600 text-xl" />,
      description: t("guidelines.processOverview.steps.decision.description"),
    },
    {
      title: t("guidelines.processOverview.steps.publication.title"),
      icon: <FaArrowRight className="text-blue-600 text-xl" />,
      description: t("guidelines.processOverview.steps.publication.description"),
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50">
      {/* Hero Section with Image */}
      <div className="relative bg-white overflow-hidden">
        {/* Background Image with Gradient */}
        <div className="absolute inset-0">
          <img
            className="w-full h-full object-cover opacity-20"
            src="/assets/uba.jpeg"
            alt="The University of Bamenda"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 mix-blend-multiply"></div>
        </div>

        {/* Animated Background Icons */}
        {backgroundIcons.map(({ icon: Icon, position, delay }, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 0.1, scale: 1 }}
            transition={{ duration: 1, delay }}
            className={`absolute ${position} transform -translate-x-1/2 -translate-y-1/2`}
          >
            <Icon className="w-24 h-24 text-white" />
          </motion.div>
        ))}

        {/* Content */}
        <div className="relative max-w-7xl mx-auto py-24 px-4 sm:py-32 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center"
          >
            <h1 className="text-4xl font-extrabold text-white sm:text-5xl sm:tracking-tight lg:text-6xl">
              {t("guidelines.title")}
            </h1>
            <p className="mt-6 max-w-2xl mx-auto text-xl text-blue-100">
              {t("guidelines.subtitle")}
            </p>
          </motion.div>
        </div>
      </div>

      {/* Main Content */}
      <motion.div
        className="max-w-7xl mx-auto px-4 py-12"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <motion.div
          className="mb-12"
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.7, delay: 0.3 }}
        >
          <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">
            {t("guidelines.processOverview.title")}
          </h2>
          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
              {steps.map((step, index) => (
                <motion.div
                  key={index}
                  className="relative border border-gray-200 rounded-lg p-6 flex flex-col items-center text-center hover:shadow-lg transition-all group overflow-hidden bg-white"
                  whileHover={{ y: -5 }}
                >
                  {/* Top-left background with diagonal rounded */}
                  <div
                    className="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-blue-500 to-white opacity-40 group-hover:opacity-60 transition-opacity duration-300"
                    style={{
                      clipPath: "circle(60% at 0 0)",
                    }}
                  ></div>

                  {/* Icon inside the background */}
                  <div className="absolute top-4 left-4 z-10 text-blue-800 text-3xl">
                    {step.icon}
                  </div>

                  {/* Text Content */}
                  <div className="mt-24 z-10">
                    <h3 className="text-sm font-semibold text-gray-800 mb-1">
                      {step.title}
                    </h3>
                    <p className="text-xs text-gray-500">{step.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
        <motion.div
          className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-12"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.7, delay: 0.4 }}
        >
          <div className="p-6 md:p-8">
            {/* Manuscript Preparation */}
            <motion.div
              className="mb-10"
              initial={{ x: -100, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            >
              <div className="flex items-center mb-6">
                <div className="bg-blue-100 p-3 rounded-full mr-4">
                  <FaFileWord className="text-blue-600 text-xl" />
                </div>
                <h2 className="text-2xl font-semibold text-gray-800">
                  {t("guidelines.manuscriptPreparation.title")}
                </h2>
              </div>

              <ul className="space-y-4 pl-2">
                <li className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 text-blue-600 mr-3 mt-0.5">
                    •
                  </div>
                  <div>
                    <span className="font-medium text-gray-800">
                      {t("guidelines.manuscriptPreparation.requirements.fileFormat.label")}
                    </span>{" "}
                    {t("guidelines.manuscriptPreparation.requirements.fileFormat.description")}
                  </div>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 text-blue-600 mr-3 mt-0.5">
                    •
                  </div>
                  <div>
                    <span className="font-medium text-gray-800">
                      {t("guidelines.manuscriptPreparation.requirements.abstract.label")}
                    </span>{" "}
                    {t("guidelines.manuscriptPreparation.requirements.abstract.description")}
                  </div>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 text-blue-600 mr-3 mt-0.5">
                    •
                  </div>
                  <div>
                    <span className="font-medium text-gray-800">
                      {t("guidelines.manuscriptPreparation.requirements.keywords.label")}
                    </span>{" "}
                    {t("guidelines.manuscriptPreparation.requirements.keywords.description")}
                  </div>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 text-blue-600 mr-3 mt-0.5">
                    •
                  </div>
                  <div>
                    <span className="font-medium text-gray-800">
                      {t("guidelines.manuscriptPreparation.requirements.formatting.label")}
                    </span>{" "}
                    {t("guidelines.manuscriptPreparation.requirements.formatting.description")}
                    <a
                      href="https://apastyle.apa.org"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="ml-2 inline-flex items-center text-blue-600 hover:underline text-sm"
                    >
                      <FiExternalLink className="w-3 h-3" />
                    </a>
                  </div>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 text-blue-600 mr-3 mt-0.5">
                    •
                  </div>
                  <div>
                    <span className="font-medium text-gray-800">
                      {t("guidelines.manuscriptPreparation.requirements.authorInfo.label")}
                    </span>{" "}
                    {t("guidelines.manuscriptPreparation.requirements.authorInfo.description")}
                  </div>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 text-blue-600 mr-3 mt-0.5">
                    •
                  </div>
                  <div>
                    <span className="font-medium text-gray-800">
                      {t("guidelines.manuscriptPreparation.requirements.length.label")}
                    </span>{" "}
                    {t("guidelines.manuscriptPreparation.requirements.length.description")}
                  </div>
                </li>
              </ul>
            </motion.div>

            {/* Submission Requirements */}
            <motion.div
              className="mb-10"
              initial={{ x: 100, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <div className="flex items-center mb-6">
                <div className="bg-green-100 p-3 rounded-full mr-4">
                  <FaFileUpload className="text-green-600 text-xl" />
                </div>
                <h2 className="text-2xl font-semibold text-gray-800">
                  {t("guidelines.submissionRequirements.title")}
                </h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-gray-800 mb-3">
                    {t("guidelines.submissionRequirements.requiredFiles.title")}
                  </h4>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                      {t("guidelines.submissionRequirements.requiredFiles.manuscript")}
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                      {t("guidelines.submissionRequirements.requiredFiles.titlePage")}
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                      {t("guidelines.submissionRequirements.requiredFiles.coverLetter")}
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                      {t("guidelines.submissionRequirements.requiredFiles.figures")}
                    </li>
                  </ul>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-gray-800 mb-3">
                    {t("guidelines.submissionRequirements.technicalSpecs.title")}
                  </h4>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-green-600 rounded-full mr-3"></div>
                      {t("guidelines.submissionRequirements.technicalSpecs.fileSize")}
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-green-600 rounded-full mr-3"></div>
                      {t("guidelines.submissionRequirements.technicalSpecs.font")}
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-green-600 rounded-full mr-3"></div>
                      {t("guidelines.submissionRequirements.technicalSpecs.spacing")}
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-green-600 rounded-full mr-3"></div>
                      {t("guidelines.submissionRequirements.technicalSpecs.margins")}
                    </li>
                  </ul>
                </div>
              </div>
            </motion.div>

            {/* Review Process */}
            <motion.div
              className="mb-10"
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.7 }}
            >
              <div className="flex items-center mb-6">
                <div className="bg-purple-100 p-3 rounded-full mr-4">
                  <FaSearch className="text-purple-600 text-xl" />
                </div>
                <h2 className="text-2xl font-semibold text-gray-800">
                  {t("guidelines.reviewProcess.title")}
                </h2>
              </div>

              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-600 text-white text-sm font-semibold rounded-full mr-4 mt-1">
                    1
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">
                      {t("guidelines.reviewProcess.steps.initialScreening.title")}
                    </h4>
                    <p className="text-gray-600 text-sm">
                      {t("guidelines.reviewProcess.steps.initialScreening.description")}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-600 text-white text-sm font-semibold rounded-full mr-4 mt-1">
                    2
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">
                      {t("guidelines.reviewProcess.steps.peerReview.title")}
                    </h4>
                    <p className="text-gray-600 text-sm">
                      {t("guidelines.reviewProcess.steps.peerReview.description")}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-600 text-white text-sm font-semibold rounded-full mr-4 mt-1">
                    3
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">
                      {t("guidelines.reviewProcess.steps.editorialDecision.title")}
                    </h4>
                    <p className="text-gray-600 text-sm">
                      {t("guidelines.reviewProcess.steps.editorialDecision.description")}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-600 text-white text-sm font-semibold rounded-full mr-4 mt-1">
                    4
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">
                      {t("guidelines.reviewProcess.steps.authorRevisions.title")}
                    </h4>
                    <p className="text-gray-600 text-sm">
                      {t("guidelines.reviewProcess.steps.authorRevisions.description")}
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Timeline & Fees */}
            <motion.div
              className="grid grid-cols-1 md:grid-cols-2 gap-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <div>
                <div className="flex items-center mb-6">
                  <div className="bg-orange-100 p-3 rounded-full mr-4">
                    <FaClock className="text-orange-600 text-xl" />
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800">
                    {t("guidelines.timeline.title")}
                  </h2>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium text-gray-700">
                      {t("guidelines.timeline.initialReview")}
                    </span>
                    <span className="text-blue-600 font-semibold">{t("guidelines.timeline.days2to3")}</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium text-gray-700">
                      {t("guidelines.timeline.peerReview")}
                    </span>
                    <span className="text-blue-600 font-semibold">{t("guidelines.timeline.weeks6to8")}</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium text-gray-700">
                      {t("guidelines.timeline.finalDecision")}
                    </span>
                    <span className="text-blue-600 font-semibold">{t("guidelines.timeline.weeks1to2")}</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium text-gray-700">
                      {t("guidelines.timeline.publication")}
                    </span>
                    <span className="text-blue-600 font-semibold">{t("guidelines.timeline.weeks2to4")}</span>
                  </div>
                </div>
              </div>

              <div>
                <div className="flex items-center mb-6">
                  <div className="bg-green-100 p-3 rounded-full mr-4">
                    <FaMoneyBillWave className="text-green-600 text-xl" />
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800">
                    {t("guidelines.fees.title")}
                  </h2>
                </div>

                <div className="space-y-3">
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-semibold text-gray-800">
                        {t("guidelines.fees.reviewFee.title")}
                      </span>
                      <span className="text-2xl font-bold text-blue-600">
                        {t("guidelines.fees.reviewFee.amount")}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600">
                      {t("guidelines.fees.reviewFee.description")}
                    </p>
                  </div>

                  <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                    <p className="text-sm text-amber-800">
                      <strong>Note:</strong> {t("guidelines.fees.note")}
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Contact Information */}
        <motion.div
          className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl shadow-md text-white p-8 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.9 }}
        >
          <h3 className="text-2xl font-semibold mb-4">
            {t("guidelines.contact.title")}
          </h3>
          <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
            {t("guidelines.contact.description")}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center px-6 py-3 bg-white text-blue-600 font-medium rounded-lg hover:bg-blue-50 transition-colors"
            >
              <FaUserEdit className="mr-2" />
              {t("guidelines.contact.contactEditorial")}
            </a>
            <a
              href="/contact"
              className="inline-flex items-center px-6 py-3 bg-blue-700 text-white font-medium rounded-lg hover:bg-blue-800 transition-colors border border-blue-500"
            >
              <FaCalendarAlt className="mr-2" />
              {t("guidelines.contact.getSupport")}
            </a>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default GuidelinesPage;
