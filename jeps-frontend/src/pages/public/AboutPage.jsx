/**
 * <AUTHOR> (andrew21-mch)
 * @github https://github.com/andrew21-mch
 * @description About Us Page - Displays information about JEPS Journal in English and French
 */

import { FaBook, FaUniversity, FaGlobe, FaGraduationCap, FaBookOpen, FaFlask, FaBrain, FaChartLine } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';

const AboutPage = () => {
  const { t } = useTranslation();

  // Background icons with their positions and animations
  const backgroundIcons = [
    { icon: FaGraduationCap, position: "top-1/4 left-1/4", delay: 0 },
    { icon: FaBookOpen, position: "top-1/3 right-1/4", delay: 0.2 },
    { icon: FaFlask, position: "bottom-1/4 left-1/3", delay: 0.4 },
    { icon: FaBrain, position: "bottom-1/3 right-1/3", delay: 0.6 },
    { icon: FaChart<PERSON><PERSON>, position: "top-1/2 left-1/2", delay: 0.8 }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50">
      {/* Hero Section with Image */}
      <div className="relative bg-white overflow-hidden">
        {/* Background Image with Gradient */}
        <div className="absolute inset-0">
          <img
            className="w-full h-full object-cover opacity-20"
            src="/assets/uba.jpeg"
            alt="The University of Bamenda"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 mix-blend-multiply"></div>
        </div>

        {/* Animated Background Icons */}
        {backgroundIcons.map(({ icon: Icon, position, delay }, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 0.1, scale: 1 }}
            transition={{ duration: 1, delay }}
            className={`absolute ${position} transform -translate-x-1/2 -translate-y-1/2`}
          >
            <Icon className="w-24 h-24 text-white" />
          </motion.div>
        ))}

        {/* Content */}
        <div className="relative max-w-7xl mx-auto py-24 px-4 sm:py-32 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center"
          >
            <h1 className="text-4xl font-extrabold text-white sm:text-5xl sm:tracking-tight lg:text-6xl">
              {t('about.title')}
            </h1>
            <p className="mt-6 max-w-2xl mx-auto text-xl text-blue-100">
              {t('about.subtitle')}
            </p>
          </motion.div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Overview and Mission */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-white rounded-xl shadow-md overflow-hidden"
          >
            <div className="px-6 py-8">
              <div className="flex items-center space-x-3 mb-6">
                <FaUniversity className="text-3xl text-blue-600" />
                <h2 className="text-2xl font-bold text-gray-900">
                  {t('about.sections.overview')}
                </h2>
              </div>
              <p className="text-gray-600 leading-relaxed mb-6">
                {t('about.overview')}
              </p>
              <p className="text-gray-600 leading-relaxed">
                {t('about.mission')}
              </p>
            </div>
          </motion.div>

          {/* Scope */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-white rounded-xl shadow-md overflow-hidden"
          >
            <div className="px-6 py-8">
              <div className="flex items-center space-x-3 mb-6">
                <FaBook className="text-3xl text-blue-600" />
                <h2 className="text-2xl font-bold text-gray-900">
                  {t('about.sections.scope')}
                </h2>
              </div>
              <p className="text-gray-600 leading-relaxed">
                {t('about.scope')}
              </p>
            </div>
          </motion.div>

          {/* Publication Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="lg:col-span-2 bg-white rounded-xl shadow-md overflow-hidden"
          >
            <div className="px-6 py-8">
              <div className="flex items-center space-x-3 mb-6">
                <FaGlobe className="text-3xl text-blue-600" />
                <h2 className="text-2xl font-bold text-gray-900">
                  {t('about.sections.publication')}
                </h2>
              </div>
              <p className="text-gray-600 leading-relaxed">
                {t('about.publication')}
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default AboutPage;