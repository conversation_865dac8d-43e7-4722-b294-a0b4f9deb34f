/**
 * <AUTHOR> (andrew21-mch)
 * @github https://github.com/andrew21-mch
 * @description About Us Page - Displays information about JEMS Journal in English and French
 */

import { FaBook, FaUniversity, FaGlobe, FaGraduationCap, FaBookOpen, FaFlask, FaBrain, FaChartLine } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';

const AboutPage = () => {
  const { t } = useTranslation();

  // Background icons with their positions and animations
  const backgroundIcons = [
    { icon: FaGraduationCap, position: "top-1/4 left-1/4", delay: 0 },
    { icon: FaBookOpen, position: "top-1/3 right-1/4", delay: 0.2 },
    { icon: FaFlask, position: "bottom-1/4 left-1/3", delay: 0.4 },
    { icon: FaBrain, position: "bottom-1/3 right-1/3", delay: 0.6 },
    { icon: FaChart<PERSON><PERSON>, position: "top-1/2 left-1/2", delay: 0.8 }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-yellow-50">
      {/* Hero Section with Image */}
      <div className="relative bg-gradient-to-br from-blue-900 via-blue-700 to-blue-600 overflow-hidden">
        {/* Background Image with Gradient */}
        <div className="absolute inset-0">
          <img
            className="w-full h-full object-cover opacity-30"
            src="/assets/uba.jpeg"
            alt="The University of Bamenda"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-blue-700 to-green-600 mix-blend-multiply opacity-80"></div>
        </div>

        {/* Decorative wave pattern */}
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-r from-green-500 via-yellow-400 to-orange-400 opacity-70"></div>

        {/* Animated Background Icons */}
        {backgroundIcons.map(({ icon: Icon, position, delay }, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 0.1, scale: 1 }}
            transition={{ duration: 1, delay }}
            className={`absolute ${position} transform -translate-x-1/2 -translate-y-1/2`}
          >
            <Icon className="w-24 h-24 text-white" />
          </motion.div>
        ))}

        {/* Content */}
        <div className="relative max-w-7xl mx-auto py-24 px-4 sm:py-32 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center"
          >
            <h1 className="text-4xl font-extrabold text-white sm:text-5xl sm:tracking-tight lg:text-6xl">
              {t('about.title')}
            </h1>
            <p className="mt-6 max-w-2xl mx-auto text-xl text-yellow-100 drop-shadow-md">
              {t('about.subtitle')}
            </p>
          </motion.div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Overview and Mission */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden border border-orange-200"
          >
            <div className="px-6 py-8">
              <div className="flex items-center space-x-3 mb-6">
                <FaUniversity className="text-3xl text-blue-900" />
                <h2 className="text-2xl font-bold text-blue-900">
                  {t('about.sections.overview')}
                </h2>
              </div>
              <p className="text-gray-700 leading-relaxed mb-6">
                {t('about.overview')}
              </p>
              <p className="text-gray-700 leading-relaxed">
                {t('about.mission')}
              </p>
            </div>
          </motion.div>

          {/* Scope */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden border border-green-200"
          >
            <div className="px-6 py-8">
              <div className="flex items-center space-x-3 mb-6">
                <FaBook className="text-3xl text-green-600" />
                <h2 className="text-2xl font-bold text-blue-900">
                  {t('about.sections.scope')}
                </h2>
              </div>
              <p className="text-gray-700 leading-relaxed">
                {t('about.scope')}
              </p>
            </div>
          </motion.div>

          {/* Publication Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="lg:col-span-2 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden border border-yellow-200"
          >
            <div className="px-6 py-8">
              <div className="flex items-center space-x-3 mb-6">
                <FaGlobe className="text-3xl text-orange-500" />
                <h2 className="text-2xl font-bold text-blue-900">
                  {t('about.sections.publication')}
                </h2>
              </div>
              <p className="text-gray-700 leading-relaxed">
                {t('about.publication')}
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default AboutPage;