import { FaPaperPlane, FaMapMarkerAlt, FaPhone, FaEnvelope, FaClock } from 'react-icons/fa';
import { motion } from 'framer-motion';

const ContactPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-yellow-50">
      <div className="max-w-6xl mx-auto px-4 py-12">
        {/* Header Section */}
        <div className="text-center mb-12">
          <motion.h1
            className="text-4xl font-bold text-blue-900 mb-4"
            initial={{ y: -20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            Contact JEMS Editorial Team
          </motion.h1>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto">
            Have questions about submissions, reviews, or the journal? Reach out to us.
          </p>
        </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Contact Information */}
        <motion.div
          className="bg-white rounded-xl shadow-lg border border-orange-200 p-8 hover:shadow-xl transition-shadow duration-300"
          initial={{ x: -20, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.6 }}
        >
          <h2 className="text-2xl font-semibold text-blue-900 mb-6">Our Information</h2>

          <div className="space-y-6">
            <div className="flex items-start">
              <div className="bg-orange-100 p-3 rounded-full mr-4">
                <FaMapMarkerAlt className="text-orange-600 text-xl" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-blue-900 mb-1">Address</h3>
                <p className="text-gray-700">
                  Faculty of Economics and Management Sciences<br />
                  The University of Bamenda<br />
                  P.O. Box 39, Bambili<br />
                  North West Region, Cameroon
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="bg-blue-100 p-3 rounded-full mr-4">
                <FaPhone className="text-blue-600 text-xl" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-1">Phone</h3>
                <p className="text-gray-600">
                  +237 677 381 419<br />
                  +237 694 909 800<br />
                  +237 676 517 417
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="bg-blue-100 p-3 rounded-full mr-4">
                <FaEnvelope className="text-blue-600 text-xl" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-1">Email</h3>
                <p className="text-gray-600">
                  <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                    <EMAIL>
                  </a><br />
                  <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                    <EMAIL>
                  </a>
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="bg-blue-100 p-3 rounded-full mr-4">
                <FaClock className="text-blue-600 text-xl" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-1">Office Hours</h3>
                <p className="text-gray-600">
                  Monday - Friday: 8:00 AM - 4:00 PM<br />
                  (GMT+1 Time Zone)
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Contact Form */}
        <motion.div
          className="bg-white rounded-xl shadow-sm border border-gray-200 p-8"
          initial={{ x: 20, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.6 }}
        >
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">Send Us a Message</h2>
          
          <form className="space-y-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Full Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Your full name"
                required
              />
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email Address <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                id="email"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div>
              <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
                Subject <span className="text-red-500">*</span>
              </label>
              <select
                id="subject"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="">Select a subject</option>
                <option value="submission">Manuscript Submission</option>
                <option value="review">Review Process</option>
                <option value="payment">Publication Fees</option>
                <option value="technical">Technical Support</option>
                <option value="other">Other Inquiry</option>
              </select>
            </div>

            <div>
              <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                Message <span className="text-red-500">*</span>
              </label>
              <textarea
                id="message"
                rows="5"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Your message..."
                required
              ></textarea>
            </div>

            <motion.button
              type="submit"
              className="w-full flex items-center justify-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <FaPaperPlane className="mr-2" />
              Send Message
            </motion.button>
          </form>
        </motion.div>
      </div>

      {/* Additional Information */}
      <motion.div 
        className="mt-16 bg-blue-50 rounded-xl p-8 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.6 }}
      >
        <h3 className="text-xl font-semibold text-gray-800 mb-4">Editorial Office</h3>
        <p className="text-gray-600 mb-4">
          For manuscript submissions, please use our online submission system rather than emailing directly.
        </p>
        <a
          href="/submit-paper"
          className="inline-flex items-center px-6 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
        >
          Go to Submission Portal
        </a>
      </motion.div>
    </div>
  );
};

export default ContactPage;