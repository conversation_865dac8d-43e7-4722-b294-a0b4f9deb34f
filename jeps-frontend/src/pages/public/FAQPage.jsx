import { useState } from 'react';
import { FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { motion } from 'framer-motion';

const FAQPage = () => {
    const [activeIndex, setActiveIndex] = useState(null);

    const toggleFAQ = (index) => {
        setActiveIndex(activeIndex === index ? null : index);
    };

    const faqs = [
        {
            question: "What is the publication frequency of JEMS?",
            answer: "JEMS is published biannually, with issues released in January and June each year."
        },
        {
            question: "What are the submission deadlines?",
            answer: "Submissions for the January issue must be received by November 15, and submissions for the June issue by April 15."
        },
        {
            question: "What is the publication fee?",
            answer: "The publication fee is 75,000 CFA (Central African Franc). Payment details will be provided upon manuscript acceptance."
        },
        {
            question: "How long does the review process take?",
            answer: "The peer-review process typically takes 4-6 weeks from submission. Authors will receive notification about the editorial decision within one month of submission."
        },
        {
            question: "Can I submit my paper in French?",
            answer: "Yes, JEMS accepts manuscripts in both English and French. Abstracts should be provided in both languages."
        },
        {
            question: "What referencing style should I use?",
            answer: "All manuscripts must follow the latest edition of the APA (American Psychological Association) style for references and citations."
        },
        {
            question: "How can I become a reviewer for JEMS?",
            answer: "Qualified academics can express their interest by sending their CV and list of <NAME_EMAIL>, indicating their areas of expertise."
        }
    ];

    return (
        <div className="max-w-4xl mx-auto px-4 py-12">
            <div className="text-center mb-12">
                <motion.h1
                    className="text-3xl font-bold text-gray-800 mb-4"
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                >
                    Frequently Asked Questions
                </motion.h1>
                <motion.p
                    className="text-lg text-gray-600"
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                >
                    Find answers to common questions about submissions, reviews, and publication in JEMS.
                </motion.p>
            </div>

            <div className="space-y-4">
                {faqs.map((faq, index) => (
                    <motion.div
                        key={index}
                        className="border border-gray-200 rounded-lg overflow-hidden"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                    >
                        <button
                            className="w-full flex justify-between items-center p-6 text-left bg-gray-50 hover:bg-gray-100 transition-colors"
                            onClick={() => toggleFAQ(index)}
                        >
                            <h3 className="text-lg font-medium text-gray-800">{faq.question}</h3>
                            {activeIndex === index ? (
                                <FaChevronUp className="text-blue-600" />
                            ) : (
                                <FaChevronDown className="text-blue-600" />
                            )}
                        </button>
                        {activeIndex === index && (
                            <motion.div
                                className="p-6 bg-white"
                                initial={{ height: 0, opacity: 0 }}
                                animate={{ height: "auto", opacity: 1 }}
                                exit={{ height: 0, opacity: 0 }}
                                transition={{ duration: 0.3 }}
                            >
                                <p className="text-gray-600">{faq.answer}</p>
                            </motion.div>
                        )}
                    </motion.div>
                ))}
            </div>

            <motion.div
                className="mt-12 text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
            >
                <p className="text-gray-600 mb-4">Didn't find what you're looking for?</p>
                <a
                    href="/contact"
                    className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
                >
                    Contact Our Editorial Team
                </a>
            </motion.div>
        </div>
    );
};

export default FAQPage;