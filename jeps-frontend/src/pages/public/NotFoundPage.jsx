import { Link } from "react-router-dom";
import { FaExclamation<PERSON>riangle, <PERSON>aHome, FaSearch, FaBook } from "react-icons/fa";
import { motion } from "framer-motion";
import Header from "../../components/Header";

const NotFoundPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50">
      <Header />
      <div className="flex items-center justify-center p-4 pt-24">
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-2xl w-full bg-white rounded-xl shadow-2xl overflow-hidden transform -rotate-1 hover:rotate-0 transition-transform duration-300"
        >
          {/* Paper texture overlay */}
          <div className="absolute inset-0 bg-[url('/assets/paper-texture.png')] opacity-5 pointer-events-none"></div>
          
          {/* Header with gradient */}
          <div className="bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-800 py-8 px-10 relative">
            <div className="absolute top-0 left-0 w-full h-full bg-[url('/assets/uba.jpeg')] opacity-10"></div>
            <div className="flex items-center space-x-4 relative z-10">
              <div className="bg-white/20 p-3 rounded-full">
                <FaExclamationTriangle className="text-white text-2xl" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Page Not Found</h2>
                <p className="text-blue-100 text-sm mt-1">Error 404</p>
              </div>
            </div>
          </div>
          
          <div className="p-10 text-center relative">
            {/* Decorative elements */}
            <div className="absolute top-0 left-0 w-32 h-32 bg-blue-50 rounded-br-full -z-10"></div>
            <div className="absolute bottom-0 right-0 w-32 h-32 bg-indigo-50 rounded-tl-full -z-10"></div>
            
            <motion.div 
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
              className="text-blue-600 text-8xl font-bold mb-6"
            >
              404
            </motion.div>
            
            <h3 className="text-2xl font-semibold text-gray-800 mb-4">Oops! Page not found</h3>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              The page you're looking for doesn't exist or has been moved. Let's get you back on track.
            </p>
            
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link
                  to="/"
                  className="flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors shadow-md"
                >
                  <FaHome className="mr-2" />
                  Return Home
                </Link>
              </motion.div>
              
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link
                  to="/browse"
                  className="flex items-center justify-center border-2 border-blue-600 text-blue-600 hover:bg-blue-50 font-medium py-3 px-6 rounded-lg transition-colors"
                >
                  <FaBook className="mr-2" />
                  Browse Articles
                </Link>
              </motion.div>
            </div>
          </div>
          
          {/* Footer note */}
          <div className="bg-gray-50 border-t border-gray-100 py-4 px-10 text-center text-sm text-gray-500">
            Need help? Contact our support team at <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default NotFoundPage;