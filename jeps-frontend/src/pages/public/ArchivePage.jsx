import { Link } from 'react-router-dom';
import { FaBook<PERSON><PERSON>, FaSearch, FaCalendarAlt, FaUser, FaEye } from 'react-icons/fa';
import { useState, useEffect } from 'react';
import { manuscriptService } from '../../services/manuscriptService';

const ArchivePage = () => {
  const [manuscripts, setManuscripts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedYear, setSelectedYear] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const itemsPerPage = 12;

  // Get unique years from manuscripts for filter dropdown
  const [availableYears, setAvailableYears] = useState([]);

  useEffect(() => {
    loadManuscripts();
  }, [currentPage, searchQuery, selectedYear, selectedCategory]);

  const loadManuscripts = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        per_page: itemsPerPage.toString(),
      });

      if (searchQuery.trim()) {
        params.append('search', searchQuery.trim());
      }
      if (selectedYear) {
        params.append('year', selectedYear);
      }
      if (selectedCategory) {
        params.append('category', selectedCategory);
      }

      const response = await manuscriptService.getPublishedManuscripts(params);
      
      setManuscripts(response.data || []);
      setTotalItems(response.total || 0);
      setTotalPages(response.last_page || 1);
      
      // Extract unique years for filter dropdown
      if (response.data && response.data.length > 0) {
        const years = [...new Set(response.data.map(manuscript => 
          new Date(manuscript.published_at).getFullYear()
        ))].sort((a, b) => b - a);
        setAvailableYears(years);
      }
    } catch (error) {
      console.error('Error loading manuscripts:', error);
      setManuscripts([]);
      setTotalItems(0);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    loadManuscripts();
  };

  const handleYearChange = (e) => {
    setSelectedYear(e.target.value);
    setCurrentPage(1);
  };

  const handleCategoryChange = (e) => {
    setSelectedCategory(e.target.value);
    setCurrentPage(1);
  };

  const truncateText = (text, wordLimit = 20) => {
    if (!text) return '';
    const words = text.split(' ');
    if (words.length <= wordLimit) return text;
    return words.slice(0, wordLimit).join(' ') + '...';
  };

  return (
    <div className="max-w-6xl mx-auto px-4 py-12">
      <div className="text-center mb-12">
        <h1 className="text-3xl font-bold text-gray-800 mb-4">Published Articles Archive</h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Browse published articles from the Journal of Education and Psychological Sciences
        </p>
      </div>

      {/* Search and Filter Controls */}
      <div className="mb-8 flex flex-col sm:flex-row justify-between items-center gap-4">
        <form onSubmit={handleSearch} className="relative w-full sm:w-96">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search articles by title, abstract, keywords, or author..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </form>
        
        <div className="flex gap-4 w-full sm:w-auto">
          <select 
            value={selectedYear} 
            onChange={handleYearChange}
            className="w-full sm:w-32 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Years</option>
            {availableYears.map(year => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>
          
          <select 
            value={selectedCategory} 
            onChange={handleCategoryChange}
            className="w-full sm:w-40 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Categories</option>
            <option value="Education">Education</option>
            <option value="Psychology">Psychology</option>
            <option value="Research">Research</option>
            <option value="Social Sciences">Social Sciences</option>
          </select>
        </div>
      </div>

      {/* Results Summary */}
      <div className="mb-6">
        <p className="text-sm text-gray-600">
          {loading ? 'Loading...' : `Showing ${Math.min((currentPage - 1) * itemsPerPage + 1, totalItems || 0)} to ${Math.min(currentPage * itemsPerPage, totalItems || 0)} of ${totalItems || 0} published articles`}
        </p>
      </div>

      {/* Articles Grid */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Loading articles...</span>
          </div>
        ) : manuscripts.length === 0 ? (
          <div className="text-center py-12">
            <FaBookOpen className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-gray-500">No published articles found matching your criteria.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
            {manuscripts.map((manuscript) => (
              <div
                key={manuscript.id}
                className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start mb-4">
                  <div className="p-3 bg-blue-50 rounded-full mr-4 flex-shrink-0">
                    <FaBookOpen className="text-blue-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-medium text-gray-800 mb-2 line-clamp-2">
                      {manuscript.title}
                    </h3>
                    <div className="flex items-center text-sm text-gray-600 mb-2">
                      <FaUser className="mr-1" />
                      <span>{manuscript.author?.name || 'Unknown Author'}</span>
                    </div>
                  </div>
                </div>
                
                {manuscript.abstract && (
                  <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                    {truncateText(manuscript.abstract, 15)}
                  </p>
                )}
                
                <div className="space-y-2 mb-4">
                  {manuscript.category && (
                    <span className="inline-block px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                      {manuscript.category}
                    </span>
                  )}
                  {manuscript.issue && (
                    <div className="text-xs text-gray-500">
                      {manuscript.issue.volume && `Vol. ${manuscript.issue.volume.number}, `}
                      Issue {manuscript.issue.number}
                    </div>
                  )}
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center text-gray-500">
                    <FaCalendarAlt className="mr-2" />
                    <span>Published: {new Date(manuscript.published_at).toLocaleDateString()}</span>
                  </div>
                  <Link
                    to={`/article/${manuscript.id}`}
                    className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
                  >
                    <FaEye className="mr-1" />
                    View
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {!loading && totalPages > 1 && (
          <div className="border-t border-gray-200 px-6 py-4 flex justify-between items-center">
            <span className="text-sm text-gray-600">
              Page {currentPage} of {totalPages}
            </span>
            <div className="flex space-x-2">
              <button
                onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 border border-gray-300 rounded text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              
              {/* Page numbers */}
              <div className="flex space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }
                  
                  return (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`px-3 py-1 border rounded text-sm font-medium ${
                        currentPage === pageNum
                          ? 'border-blue-500 bg-blue-600 text-white'
                          : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>
              
              <button
                onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 border border-gray-300 rounded text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ArchivePage;