import { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { manuscriptService } from '../../services/manuscriptService';
import { motion } from 'framer-motion';
import { FaArrowLeft, FaCalendarAlt, FaBook, FaUser, FaUniversity, FaDownload, FaExpand, FaCompress, FaFilePdf } from 'react-icons/fa';

const ArticleViewPage = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const [article, setArticle] = useState(null);
  const [relatedArticles, setRelatedArticles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Add state for PDF viewer
  const [showPdfViewer, setShowPdfViewer] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [pdfUrl, setPdfUrl] = useState(null);
  const [downloadUrl, setDownloadUrl] = useState(null);
  const [pdfLoading, setPdfLoading] = useState(false);
  const [pdfError, setPdfError] = useState(false);

  const truncateText = (text, maxLength = 100) => {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength) + '...';
  };

  useEffect(() => {
    const fetchArticle = async () => {
      try {
        setLoading(true);
        const response = await manuscriptService.getManuscriptById(id);
        setArticle(response.data);
        
        // Set PDF URL for viewer
        if (response.data) {
          console.log('Article data:', response.data);
          console.log('File path:', response.data.file_path);
          console.log('Links:', response.data.links);
          
          // Only set PDF URL if file_path exists
          if (response.data.file_path) {
            const API_URL = import.meta.env.VITE_API_URL;
            const downloadUrlPath = `${API_URL}/api/v1/manuscripts/${id}/download`;
            console.log('Generated PDF URL:', downloadUrlPath);
            
            // For preview, we need to access the file directly from storage
            // Assuming files are served from storage/app/public/manuscripts
            const previewUrl = `${API_URL}/storage/${response.data.file_path}`;
            console.log('Generated Preview URL:', previewUrl);
            
            setPdfUrl(previewUrl);
            setDownloadUrl(downloadUrlPath);
          } else {
            console.log('No file_path found for this article');
            setPdfUrl(null);
            setDownloadUrl(null);
          }
        }
        
        // Fetch related articles from the same volume/issue
        if (response.data.issue?.id) {
          const relatedResponse = await manuscriptService.getManuscriptsByIssue(response.data.issue.id);
          // Filter out the current article from related articles
          const filteredRelated = relatedResponse.data.filter(art => art.id !== id);
          setRelatedArticles(filteredRelated);
        }
      } catch (err) {
        console.error('Error fetching article:', err);
        setError('Failed to load article');
      } finally {
        setLoading(false);
      }
    };

    fetchArticle();
  }, [id]);

  const handleDownload = () => {
    if (downloadUrl) {
      window.open(downloadUrl, '_blank');
    }
  };

  const togglePdfViewer = () => {
    if (!showPdfViewer) {
      setPdfLoading(true);
      setPdfError(false);
    }
    setShowPdfViewer(!showPdfViewer);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handlePdfLoad = () => {
    setPdfLoading(false);
  };

  const handlePdfError = () => {
    setPdfLoading(false);
    setPdfError(true);
  };

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
          <div className="h-4 bg-gray-200 rounded w-full mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-full mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        </div>
      </div>
    );
  }

  if (error || !article) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">{error || 'Article not found'}</h2>
          <Link to="/" className="text-blue-600 hover:text-blue-500">
            Return to Home
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Back button */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="mb-8"
        >
          <Link
            to="/"
            className="inline-flex items-center text-blue-600 hover:text-blue-500"
          >
            <FaArrowLeft className="mr-2" />
            {t('common.back')}
          </Link>
        </motion.div>

        {/* Article Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-12"
        >
          <h1 className="text-4xl font-bold text-gray-900 mb-4">{article.title}</h1>
          
          <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-6">
            <div className="flex items-center">
              <FaCalendarAlt className="mr-2" />
              Vol. {article.issue?.volume?.number} - Issue {article.issue?.type} - {article.issue?.volume?.year}
            </div>
            <div className="flex items-center">
              <FaBook className="mr-2" />
              {article.category}
            </div>
          </div>

          {/* Document Actions */}
          {pdfUrl && (
            <div className="flex flex-wrap gap-4 mb-8">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={togglePdfViewer}
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200"
              >
                <FaFilePdf className="mr-2" />
                {showPdfViewer ? 'Hide Document' : 'View Full Document'}
              </motion.button>
            </div>
          )}

          {/* Authors */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('browse.article.authors')}</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {article.authors?.map((author, index) => (
                <div key={index} className="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg">
                  <FaUser className="mt-1 text-blue-600" />
                  <div>
                    <p className="font-medium text-gray-900">{typeof author === 'object' ? author.name : author}</p>
                    {typeof author === 'object' && author.affiliation && (
                      <p className="text-sm text-gray-600 flex items-center">
                        <FaUniversity className="mr-1" />
                        {author.affiliation}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* PDF Viewer */}
        {showPdfViewer && pdfUrl && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className={`mb-12 ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : ''}`}
          >
            <div className={`${isFullscreen ? 'h-full flex flex-col' : ''}`}>
              {/* Enhanced Header */}
              <div className="flex justify-between items-center mb-4 bg-gradient-to-r from-gray-50 to-gray-100 p-6 rounded-t-xl border border-gray-200 shadow-sm">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <FaFilePdf className="text-blue-600 text-lg" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Document Viewer</h3>
                    <p className="text-sm text-gray-600">
                      {article.title?.length > 50 ? `${article.title.substring(0, 50)}...` : article.title}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  {/* Status Indicator */}
                  <div className="flex items-center space-x-2 mr-4">
                    {pdfLoading && (
                      <div className="flex items-center space-x-2 text-blue-600">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                        <span className="text-sm">Loading...</span>
                      </div>
                    )}
                    {!pdfLoading && !pdfError && showPdfViewer && (
                      <div className="flex items-center space-x-2 text-green-600">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm">Ready</span>
                      </div>
                    )}
                    {pdfError && (
                      <div className="flex items-center space-x-2 text-red-600">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        <span className="text-sm">Error</span>
                      </div>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleDownload}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 shadow-sm"
                    title="Download PDF"
                  >
                    <FaDownload className="mr-2" />
                    Download
                  </motion.button>
                  
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={toggleFullscreen}
                    className="p-3 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded-lg transition-all duration-200"
                    title={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
                  >
                    {isFullscreen ? <FaCompress className="text-lg" /> : <FaExpand className="text-lg" />}
                  </motion.button>
                  
                  {isFullscreen && (
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={togglePdfViewer}
                      className="p-3 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200"
                      title="Close Viewer"
                    >
                      <span className="text-xl font-bold">×</span>
                    </motion.button>
                  )}
                </div>
              </div>
              
              {/* Enhanced PDF Container */}
              <div className={`relative border-x border-b border-gray-200 rounded-b-xl overflow-hidden shadow-lg ${isFullscreen ? 'flex-1' : 'h-96 md:h-[600px]'}`}>
                {/* Loading Overlay */}
                {pdfLoading && (
                  <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                      <p className="text-gray-600 font-medium">Loading document...</p>
                      <p className="text-sm text-gray-500 mt-2">Please wait while we prepare your document</p>
                    </div>
                  </div>
                )}

                {/* Error State */}
                {pdfError && (
                  <div className="absolute inset-0 bg-gray-50 flex items-center justify-center">
                    <div className="text-center p-8">
                      <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <FaFilePdf className="text-red-600 text-2xl" />
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">Unable to preview document</h4>
                      <p className="text-gray-600 mb-6">The document couldn't be loaded for preview, but you can still download it.</p>
                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={handleDownload}
                        className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 shadow-sm"
                      >
                        <FaDownload className="mr-2" />
                        Download to View
                      </motion.button>
                    </div>
                  </div>
                )}

                {/* PDF Iframe */}
                <iframe
                  src={`${pdfUrl}#toolbar=1&navpanes=1&scrollbar=1`}
                  className="w-full h-full"
                  title="Document Viewer"
                  onLoad={handlePdfLoad}
                  onError={handlePdfError}
                  style={{ 
                    border: 'none',
                    background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
                  }}
                />

                {/* Corner Decoration */}
                <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-blue-500/10 to-transparent pointer-events-none"></div>
                <div className="absolute bottom-0 left-0 w-20 h-20 bg-gradient-to-tr from-blue-500/10 to-transparent pointer-events-none"></div>
              </div>

              {/* Footer Info */}
              {!isFullscreen && (
                <div className="mt-4 px-4 py-3 bg-gray-50 rounded-lg border border-gray-200">
                  <div className="flex justify-between items-center text-sm text-gray-600">
                    <div className="flex items-center space-x-4">
                      <span>📄 PDF Document</span>
                      <span>•</span>
                      <span>Use browser zoom for better readability</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                        Interactive
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}

        {/* Article Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="prose prose-lg max-w-none mb-12"
        >
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">{t('browse.article.abstract')}</h2>
          <p className="text-gray-700 mb-8">
            {article.abstract}
          </p>

          {article.keywords && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('browse.article.keywords')}</h3>
              <div className="flex flex-wrap gap-2">
                {article.keywords.map((keyword, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                  >
                    {keyword}
                  </span>
                ))}
              </div>
            </div>
          )}

          {article.content && (
            <div className="mt-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">{t('article.content')}</h2>
              <div className="text-gray-700">{article.content}</div>
            </div>
          )}
        </motion.div>

        {/* Related Articles */}
        {relatedArticles.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mt-16"
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-8">{t('article.relatedArticles')}</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {relatedArticles.map((relatedArticle) => (
                <motion.div
                  key={relatedArticle.id}
                  whileHover={{ y: -5 }}
                  className="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300"
                >
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      <Link
                        to={`/article/${relatedArticle.id}`}
                        className="hover:text-blue-600 transition-colors duration-200"
                      >
                        {relatedArticle.title}
                      </Link>
                    </h3>
                    <p className="text-sm text-gray-500 mb-4">
                      Vol. {relatedArticle.issue?.volume?.number} - Issue {relatedArticle.issue?.type} - {relatedArticle.issue?.volume?.year}
                    </p>
                    <p className="text-gray-600 line-clamp-3 mb-4">
                      {truncateText(relatedArticle.abstract, 200)}
                      {relatedArticle.abstract.length > 200 && (
                        <span className="inline-block ml-1">
                          <Link to={`/article/${relatedArticle.id}`} className="text-blue-600 hover:text-blue-500">
                            {t('home.publications.readMore')}
                          </Link>
                        </span>
                      )}
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {relatedArticle.authors?.map((author, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {typeof author === 'object' ? author.name : author}
                        </span>
                      ))}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default ArticleViewPage; 