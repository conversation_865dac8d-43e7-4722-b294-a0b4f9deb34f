import { useState, useEffect } from 'react';
import { FaBullhorn, FaCalendarAlt, FaStar } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import announcementService from '../../services/announcementService';
import LoadingSpinner from '../../components/common/LoadingSpinner';

const AnnouncementsPage = () => {
  const { t, i18n } = useTranslation();
  const [announcements, setAnnouncements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [email, setEmail] = useState('');
  const [subscribing, setSubscribing] = useState(false);

  useEffect(() => {
    fetchAnnouncements();
  }, []);

  const fetchAnnouncements = async () => {
    try {
      setLoading(true);
      const response = await announcementService.getAnnouncements({
        status: 'published',
        language: i18n.language
      });
      setAnnouncements(response.data || []);
    } catch (error) {
      console.error('Error fetching announcements:', error);
      setError('Failed to load announcements');
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = async (e) => {
    e.preventDefault();
    if (!email) return;

    setSubscribing(true);
    try {
      // Here you would typically call an API to subscribe the user
      // For now, we'll just show a success message
      setTimeout(() => {
        alert('Thank you for subscribing to our announcements!');
        setEmail('');
        setSubscribing(false);
      }, 1000);
    } catch (error) {
      console.error('Error subscribing:', error);
      alert('Failed to subscribe. Please try again.');
      setSubscribing(false);
    }
  };

  const getAnnouncementTitle = (announcement) => {
    const currentLang = i18n.language;
    if (announcement.title && typeof announcement.title === 'object') {
      return announcement.title[currentLang] || announcement.title.en || announcement.title.fr || 'Untitled';
    }
    return announcement.title || 'Untitled';
  };

  const getAnnouncementContent = (announcement) => {
    const currentLang = i18n.language;
    if (announcement.content && typeof announcement.content === 'object') {
      return announcement.content[currentLang] || announcement.content.en || announcement.content.fr || 'No content available';
    }
    return announcement.content || 'No content available';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString(i18n.language === 'fr' ? 'fr-FR' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-12">
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-12">
        <div className="text-center">
          <FaBullhorn className="mx-auto text-4xl text-gray-400 mb-4" />
          <p className="text-gray-500 mb-4">{error}</p>
          <button
            onClick={fetchAnnouncements}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center bg-blue-100 p-3 rounded-full mb-4">
            <FaBullhorn className="text-blue-600 text-2xl" />
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mb-4">
            {t('announcements.title', 'Announcements')}
          </h1>
          <p className="text-lg text-gray-600">
            {t('announcements.description', 'Latest news and updates from the JEMS editorial team')}
          </p>
        </div>

        {announcements.length === 0 ? (
          <div className="text-center py-12">
            <FaBullhorn className="mx-auto text-4xl text-gray-400 mb-4" />
            <p className="text-gray-500">
              {t('announcements.noAnnouncements', 'No announcements available at this time.')}
            </p>
          </div>
        ) : (
          <div className="space-y-8">
            {announcements.map((announcement) => (
              <motion.div
                key={announcement.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-50 rounded-full mr-4">
                      <FaCalendarAlt className="text-blue-600" />
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">
                        {formatDate(announcement.created_at)}
                      </span>
                      {announcement.featured && (
                        <div className="flex items-center mt-1">
                          <FaStar className="text-yellow-500 text-xs mr-1" />
                          <span className="text-xs text-yellow-600 font-medium">
                            {t('announcements.featured', 'Featured')}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                <h3 className="text-xl font-semibold text-gray-800 mb-3">
                  {getAnnouncementTitle(announcement)}
                </h3>
                
                <div className="text-gray-600 whitespace-pre-wrap">
                  {getAnnouncementContent(announcement)}
                </div>
              </motion.div>
            ))}
          </div>
        )}

        {/* Newsletter Subscription */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="mt-12 text-center bg-blue-50 rounded-xl p-8"
        >
          <h3 className="text-xl font-semibold text-gray-800 mb-4">
            {t('announcements.stayUpdated', 'Stay updated with JEMS announcements')}
          </h3>
          <p className="text-gray-600 mb-6">
            {t('announcements.subscribeDescription', 'Get notified about new announcements, calls for papers, and important updates.')}
          </p>
          
          <form onSubmit={handleSubscribe} className="flex flex-col sm:flex-row justify-center gap-3 max-w-md mx-auto">
            <input
              type="email"
              placeholder={t('announcements.emailPlaceholder', 'Your email address')}
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <button
              type="submit"
              disabled={subscribing}
              className="px-6 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {subscribing ? (
                <LoadingSpinner size="sm" className="text-white" />
              ) : (
                t('announcements.subscribe', 'Subscribe')
              )}
            </button>
          </form>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default AnnouncementsPage;