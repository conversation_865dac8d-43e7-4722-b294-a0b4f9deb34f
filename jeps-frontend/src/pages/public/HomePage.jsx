import { Link } from "react-router-dom";
import { FaBook, FaUniversity, FaUsers, FaCalendarAlt, FaUserGraduate,FaGraduationCap, FaMicroscope, FaBrain, FaChartLine, FaSearch } from "react-icons/fa";
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { manuscriptService } from '../../services/manuscriptService';
import { motion } from 'framer-motion';

const HomePage = () => {
  const { t } = useTranslation();
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [publishedManuscripts, setPublishedManuscripts] = useState([]);
  const [loading, setLoading] = useState(true);

  // Function to truncate text to specified number of words
  const truncateText = (text, wordLimit = 50) => {
    if (!text) return '';
    const words = text.split(' ');
    if (words.length <= wordLimit) return text;
    return words.slice(0, wordLimit).join(' ') + '...';
  };


  useEffect(() => {
    const fetchPublishedManuscripts = async () => {
      try {
        const response = await manuscriptService.getPublishedManuscripts();
        setPublishedManuscripts(response.data);
      } catch (error) {
        console.error('Error fetching published manuscripts:', error);
        setPublishedManuscripts([]); // Set empty array on error
      } finally {
        setLoading(false);
      }
    };

    fetchPublishedManuscripts();
  }, []);

  const features = [
    {
      icon: <FaBook className="text-orange-500" size={28} />,
      title: t('home.features.items.multidisciplinary.title'),
      description: t('home.features.items.multidisciplinary.description')
    },
    {
      icon: <FaUniversity className="text-blue-900" size={28} />,
      title: t('home.features.items.excellence.title'),
      description: t('home.features.items.excellence.description')
    },
    {
      icon: <FaUsers className="text-green-600" size={28} />,
      title: t('home.features.items.collaboration.title'),
      description: t('home.features.items.collaboration.description')
    },
    {
      icon: <FaCalendarAlt className="text-yellow-500" size={28} />,
      title: t('home.features.items.issues.title'),
      description: t('home.features.items.issues.description')
    }
  ];

  const stats = [
    { value: "12+", label: t('home.stats.years') },
    { value: "200+", label: t('home.stats.papers') },
    { value: "50+", label: t('home.stats.countries') },
    { value: "300+", label: t('home.stats.reviewers') }
  ];

  // Floating academic icons for background
  const floatingIcons = [
    { icon: <FaBook className="text-white opacity-20" size={28} />, size: 28 },
    { icon: <FaGraduationCap className="text-white opacity-20" size={32} />, size: 32 },
    { icon: <FaMicroscope className="text-white opacity-20" size={24} />, size: 24 },
    { icon: <FaBrain className="text-white opacity-20" size={30} />, size: 30 },
    { icon: <FaChartLine className="text-white opacity-20" size={26} />, size: 26 },
    { icon: <FaUniversity className="text-white opacity-20" size={28} />, size: 28 }
  ];

  const SubmitPaperButton = () => {
    const handleClick = (e) => {
      e.preventDefault();
      if (!isAuthenticated) {
        navigate('/login');
      } else {
        navigate('/author/submit');
      }
    };

    return (
      <button
        onClick={handleClick}
        className="inline-flex items-center justify-center px-8 py-4 border-2 border-orange-500 text-orange-600 text-lg font-bold rounded-lg bg-white bg-opacity-95 hover:bg-green-500 hover:text-white hover:border-green-500 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
      >
        <FaUserGraduate className="mr-2" />
        {t('home.authorLogin')}
      </button>
    );
  };

  return (
    <div className="bg-jems-cream">
      {/* Enhanced Hero Section with JEMS Colors */}
      <div className="relative bg-gradient-to-br from-blue-900 via-blue-700 to-blue-600 overflow-hidden">
        {/* Background Image with JEMS Gradient */}
        <div className="absolute inset-0">
          <img
            className="w-full h-full object-cover opacity-40"
            src="/assets/uba.jpeg"
            alt="The University of Bamenda"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-blue-700 to-green-600 mix-blend-multiply opacity-80"></div>
        </div>

        {/* Decorative wave pattern */}
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-r from-green-500 via-yellow-400 to-orange-400 opacity-70"></div>

        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.8, duration: 0.5 }}
          whileHover={{ scale: 1.05 }}
          className="absolute top-6 right-6 z-10"
        >
          <div className="bg-white bg-opacity-90 backdrop-blur-sm rounded-lg shadow-lg px-4 py-2 flex items-center">
            <span className="text-xs font-semibold text-gray-600 mr-1">{t('home.isbn')}:</span>
            {/* <span className="text-sm font-bold text-blue-800">{t('home.isbnNumber')}</span> */}
            <motion.div 
              animate={{ rotate: [0, 5, -5, 0] }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                repeatDelay: 5
              }}
              className="ml-2"
            >
              <FaBook className="text-blue-600 text-opacity-80" size={14} />
            </motion.div>
          </div>
        </motion.div>
        
        {/* Animated floating icons */}
        {floatingIcons.map((item, index) => (
          <motion.div
            key={index}
            initial={{ 
              opacity: 0,
              y: Math.random() * 100 - 50,
              x: Math.random() * 100 - 50
            }}
            animate={{
              opacity: [0, 0.2, 0],
              y: [Math.random() * 100 - 50, Math.random() * 200 - 100],
              x: [Math.random() * 100 - 50, Math.random() * 200 - 100]
            }}
            transition={{
              duration: Math.random() * 20 + 10,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "linear"
            }}
            className="absolute"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
            }}
          >
            {item.icon}
          </motion.div>
        ))}

        {/* Subtle grid animation */}
        <div className="absolute inset-0 opacity-5">
          <svg
            className="absolute inset-0 w-full h-full"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 100 100"
            preserveAspectRatio="none"
          >
            <pattern
              id="grid-pattern"
              x="0"
              y="0"
              width="10"
              height="10"
              patternUnits="userSpaceOnUse"
            >
              <path
                d="M 10 0 L 0 0 0 10"
                fill="none"
                stroke="white"
                strokeWidth="0.5"
              />
            </pattern>
            <rect width="100" height="100" fill="url(#grid-pattern)" />
          </svg>
        </div>
        
        <div className="relative max-w-7xl mx-auto py-24 px-4 sm:py-32 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row items-center justify-center gap-12">
            {/* Hero Text */}
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center lg:text-left lg:w-3/4"
            >
              <motion.h1
                className="text-4xl font-extrabold tracking-tight text-white sm:text-5xl lg:text-6xl drop-shadow-lg"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                <span className="bg-gradient-to-r from-white to-jems-gold bg-clip-text text-transparent">
                  {t('home.title')}
                </span>
              </motion.h1>
              <motion.p
                className="mt-6 text-xl text-jems-cream max-w-3xl mx-auto lg:mx-0 drop-shadow-md"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
              >
                {t('home.subtitle')}
              </motion.p>
              <motion.div 
                className="mt-10 flex justify-center lg:justify-start space-x-4"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
              >
                <motion.div 
                  whileHover={{ scale: 1.05 }} 
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                </motion.div>
                {isAuthenticated ? (
                  <SubmitPaperButton />
                ) : (
                  <SubmitPaperButton />
                )}
              </motion.div>
            </motion.div>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.8, delay: 0.3 }}
                    className="mt-12 lg:mt-0 lg:w-1/2 flex justify-center"
                  >
                    <img
                    src="/assets/JemsFront.png"
                    alt="JEMS Journal Cover"
                    className="w-[700px] h-auto rounded-xl shadow-2xl transform -rotate-12 transition-transform duration-300"
                    style={{ maxWidth: '100%', height: 'auto' }}
                    />
                  </motion.div>
                  </div>
                </div>  

                {/* Animated scroll indicator */}
        <motion.div
          initial={{ opacity: 0, y: 0 }}
          animate={{ opacity: [0, 1, 0], y: [0, 10, 20] }}
          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <svg
            className="h-8 w-8 text-white"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 14l-7 7m0 0l-7-7m7 7V3"
            />
          </svg>
        </motion.div>
      </div>

      {/* Features Section with JEMS Colors */}
      <div className="py-20 bg-gradient-to-b from-orange-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="lg:text-center"
          >
            <h2 className="text-base text-orange-500 font-semibold tracking-wide uppercase">{t('home.features.title')}</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-blue-900 sm:text-4xl">
              {t('home.features.subtitle')}
            </p>
            <div className="mt-4 max-w-2xl mx-auto text-lg text-gray-700">
              {t('home.features.description')}
            </div>
          </motion.div>

          <div className="mt-16">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ y: -8, scale: 1.02 }}
                  className="bg-white p-8 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-orange-200 hover:border-orange-400"
                >
                  <motion.div
                    whileHover={{ rotate: 10, scale: 1.2 }}
                    className="flex items-center justify-center h-16 w-16 rounded-xl bg-gradient-to-br from-orange-100 to-white shadow-md mb-6 mx-auto border border-orange-300"
                  >
                    {feature.icon}
                  </motion.div>
                  <h3 className="text-lg font-bold text-center text-blue-900 mb-3">{feature.title}</h3>
                  <p className="text-base text-gray-700 text-center leading-relaxed">{feature.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Latest Publications with JEMS Colors */}
      <div className="py-20 bg-gradient-to-b from-white to-green-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="lg:text-center mb-16"
          >
            <h2 className="text-base text-green-600 font-semibold tracking-wide uppercase">{t('home.publications.title')}</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-blue-900 sm:text-4xl">
              {t('home.publications.subtitle')}
            </p>
            <div className="mt-4 max-w-2xl mx-auto text-lg text-gray-700">
              {t('home.publications.description')}
            </div>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {loading ? (
              <div className="col-span-3">
                <div className="flex justify-center items-center h-full">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                </div>
              </div>
            ) : (
              publishedManuscripts.slice(0, 6).map((manuscript) => (
                <div
                key={manuscript.id}
                className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
              >
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2 line-clamp-2">
                    {manuscript.title}
                  </h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {truncateText(manuscript.abstract, 50)}
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>{manuscript.authors?.map((author) => author.name).join(', ')}</span>
                    <span>{new Date(manuscript.published_at).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
              ))
            )}
          </div>

          <motion.div 
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4 }}
            className="mt-12 text-center"
          >
            <Link
              to="/browse"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200"
            >
              {t('home.publications.browseAll')}
              <FaSearch className="ml-3 -mr-1 w-5 h-5" />
            </Link>
          </motion.div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-gradient-to-r from-blue-900 via-blue-800 to-blue-700 relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute inset-0 bg-gradient-to-r from-green-500 via-yellow-400 to-orange-400 opacity-10"></div>
        <div className="max-w-7xl mx-auto py-20 px-4 sm:py-24 sm:px-6 lg:px-8 lg:py-28 relative z-10">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            className="max-w-4xl mx-auto text-center"
          >
            <h2 className="text-3xl font-extrabold text-white sm:text-4xl drop-shadow-lg">
              {t('home.stats.title')}
            </h2>
            <p className="mt-4 text-xl text-yellow-100 sm:mt-5 drop-shadow-md">
              {t('home.stats.subtitle')}
            </p>
          </motion.div>
          <div className="mt-16 text-center">
            <div className="grid grid-cols-2 gap-6 sm:grid-cols-4">
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ scale: 1.05, y: -5 }}
                  className="px-6 py-8 bg-white bg-opacity-15 backdrop-blur-md rounded-xl border border-white border-opacity-20 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <p className="text-4xl sm:text-5xl font-extrabold text-yellow-400 drop-shadow-lg">{stat.value}</p>
                  <p className="mt-3 text-lg font-medium text-yellow-100">{stat.label}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action with JEMS Colors */}
      <div className="bg-gradient-to-r from-orange-50 to-white">
        <div className="max-w-7xl mx-auto py-20 px-4 sm:px-6 lg:py-24 lg:px-8 lg:flex lg:items-center lg:justify-between">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="lg:w-2/3"
          >
            <h2 className="text-3xl font-extrabold tracking-tight text-blue-900 sm:text-4xl">
              <span className="block">{t('home.cta.title')}</span>
              <span className="block text-orange-500">{t('home.cta.subtitle')}</span>
            </h2>
            <p className="mt-4 text-lg text-gray-700">
              {t('home.cta.description')}
            </p>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mt-8 flex flex-col sm:flex-row gap-4 lg:mt-0 lg:flex-shrink-0"
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-flex rounded-lg shadow-lg"
            >
              <Link
                to="/author/submit"
                className="inline-flex items-center justify-center px-8 py-4 border border-transparent text-base font-bold rounded-lg text-white bg-blue-600 hover:bg-green-600 hover:text-white transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                {t('home.cta.submit')}
                <FaBook className="ml-3 -mr-1 h-5 w-5" />
              </Link>
            </motion.div>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-flex rounded-lg shadow-lg"
            >
              <Link
                to="/guidelines"
                className="inline-flex items-center justify-center px-8 py-4 border-2 border-blue-900 text-base font-bold rounded-lg text-blue-900 bg-white hover:bg-blue-900 hover:text-white transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                {t('home.cta.guidelines')}
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;