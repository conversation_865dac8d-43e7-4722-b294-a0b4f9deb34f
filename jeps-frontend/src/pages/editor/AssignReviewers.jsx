import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { submissionService } from '../../services/submissionService';
import { FaUserPlus, FaUserMinus, FaSearch, FaSpinner } from 'react-icons/fa';

export default function AssignReviewers() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [submission, setSubmission] = useState(null);
  const [reviewers, setReviewers] = useState([]);
  const [selectedReviewers, setSelectedReviewers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadData();
  }, [id]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [submissionData, reviewersData] = await Promise.all([
        submissionService.getSubmission(id),
        submissionService.getAvailableReviewers()
      ]);
      setSubmission(submissionData);
      setReviewers(reviewersData);
      // Initialize selected reviewers from existing assignments
      if (submissionData.reviewers) {
        setSelectedReviewers(submissionData.reviewers.map(r => r.id));
      }
      setError(null);
    } catch (err) {
      setError(err.message || 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleReviewerToggle = (reviewerId) => {
    setSelectedReviewers(prev => {
      if (prev.includes(reviewerId)) {
        return prev.filter(id => id !== reviewerId);
      } else {
        return [...prev, reviewerId];
      }
    });
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      await submissionService.assignReviewers(id, selectedReviewers);
      navigate('/editor/submissions');
    } catch (err) {
      setError(err.message || 'Failed to assign reviewers');
    } finally {
      setSaving(false);
    }
  };

  const filteredReviewers = reviewers.filter(reviewer => 
    reviewer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    reviewer.expertise?.some(exp => exp.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <FaSpinner className="animate-spin h-8 w-8 text-blue-500" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-4 rounded-md">
        <p className="text-red-700">{error}</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Assign Reviewers</h1>
          <p className="mt-2 text-gray-600">
            {submission?.submission_id}: {submission?.title}
          </p>
        </div>
        <button
          onClick={handleSave}
          disabled={saving}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
        >
          {saving ? (
            <span className="flex items-center">
              <FaSpinner className="animate-spin mr-2" />
              Saving...
            </span>
          ) : (
            'Save Assignments'
          )}
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="mb-6">
          <div className="relative">
            <input
              type="text"
              placeholder="Search reviewers by name or expertise..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <FaSearch className="absolute left-3 top-3 text-gray-400" />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredReviewers.map((reviewer) => (
            <div
              key={reviewer.id}
              className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                selectedReviewers.includes(reviewer.id)
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-blue-300'
              }`}
              onClick={() => handleReviewerToggle(reviewer.id)}
            >
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="font-medium text-gray-900">{reviewer.name}</h3>
                  <p className="text-sm text-gray-500">{reviewer.affiliation}</p>
                </div>
                {selectedReviewers.includes(reviewer.id) ? (
                  <FaUserMinus className="text-blue-500" />
                ) : (
                  <FaUserPlus className="text-gray-400" />
                )}
              </div>
              {reviewer.expertise && (
                <div className="mt-2">
                  <p className="text-xs text-gray-500">Areas of Expertise:</p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {reviewer.expertise.map((exp, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full"
                      >
                        {exp}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {filteredReviewers.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No reviewers found matching your search criteria
          </div>
        )}
      </div>
    </div>
  );
} 