import { useState, useEffect } from 'react';
import { FaBook, FaPlus, FaEdit, FaTrash, FaCalendarAlt, FaCheck, FaTimes, FaCloudUploadAlt } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { useDropzone } from 'react-dropzone';
import { volumeService } from '../../services/volumeService';

export default function Volumes() {
  const [volumes, setVolumes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newVolume, setNewVolume] = useState({
    number: '',
    year: new Date().getFullYear(),
    description: '',
    cover_image: null,
  });

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif']
    },
    maxSize: 5242880, // 5MB
    onDrop: acceptedFiles => {
      if (acceptedFiles.length > 0) {
        setNewVolume(prev => ({
          ...prev,
          cover_image: acceptedFiles[0]
        }));
      }
    }
  });

  useEffect(() => {
    fetchVolumes();
  }, []);

  const fetchVolumes = async () => {
    try {
      const data = await volumeService.getAllVolumes();
      setVolumes(data);
    } catch (error) {
      toast.error('Failed to fetch volumes');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateVolume = async (e) => {
    e.preventDefault();

    // Validation: check required fields
    if (!newVolume.number || !newVolume.year) {
      toast.error('Volume number and year are required.');
      return;
    }

    try {
      const formData = new FormData();
      formData.append('number', String(newVolume.number));
      formData.append('year', String(newVolume.year));
      formData.append('description', newVolume.description || '');
      if (newVolume.cover_image) {
        formData.append('cover_image', newVolume.cover_image);
      }

      // Debug: log all form data
      for (let [key, value] of formData.entries()) {
        console.log(key, value);
      }

      await volumeService.createVolume(formData);
      toast.success('Volume created successfully');
      setShowCreateModal(false);
      setNewVolume({
        number: '',
        year: new Date().getFullYear(),
        description: '',
        cover_image: null,
      });
      fetchVolumes();
    } catch (error) {
      toast.error(error.message || 'Failed to create volume');
    }
  };

  const handlePublishVolume = async (volumeId) => {
    try {
      await volumeService.publishVolume(volumeId);
      toast.success('Volume published successfully');
      fetchVolumes();
    } catch (error) {
      toast.error(error.message || 'Failed to publish volume');
    }
  };

  const handleDeleteVolume = async (volumeId) => {
    if (!window.confirm('Are you sure you want to delete this volume?')) return;
    
    try {
      await volumeService.deleteVolume(volumeId);
      toast.success('Volume deleted successfully');
      fetchVolumes();
    } catch (error) {
      toast.error(error.message || 'Failed to delete volume');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-8">
        <motion.h1 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-2xl font-bold text-gray-800"
        >
          Volume Management
        </motion.h1>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => setShowCreateModal(true)}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <FaPlus className="mr-2" />
          New Volume
        </motion.button>
      </div>

      {volumes.length === 0 ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-md border border-gray-100 p-8 text-center"
        >
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="p-4 rounded-full bg-blue-50 text-blue-600">
              <FaBook className="w-8 h-8" />
            </div>
            <h3 className="text-xl font-semibold text-gray-800">No Volumes Yet</h3>
            <p className="text-gray-600 max-w-md">
              Get started by creating your first volume. Volumes help organize your journal's content by year and number.
            </p>
            <button
              onClick={() => setShowCreateModal(true)}
              className="mt-4 flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <FaPlus className="mr-2" />
              Create First Volume
            </button>
          </div>
        </motion.div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {volumes.map((volume) => (
            <motion.div
              key={volume.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              whileHover={{ y: -5 }}
              className="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden"
            >
              {volume.cover_image && (
                <div className="h-48 overflow-hidden">
                  <img
                    src={volume.cover_image}
                    alt={`Volume ${volume.number} cover`}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-blue-50 text-blue-600 mr-3">
                      <FaBook />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800">
                        Volume {volume.number}
                      </h3>
                      <p className="text-sm text-gray-500">{volume.year}</p>
                    </div>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    volume.published 
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {volume.published ? 'Published' : 'Draft'}
                  </span>
                </div>

                {volume.description && (
                  <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                    {volume.description}
                  </p>
                )}

                <div className="space-y-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <FaCalendarAlt className="mr-2" />
                    <span>
                      {volume.publication_date 
                        ? new Date(volume.publication_date).toLocaleDateString()
                        : 'Not published yet'}
                    </span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <FaBook className="mr-2" />
                    <span>{volume.issues?.length || 0} Issues</span>
                  </div>
                </div>

                <div className="mt-6 flex justify-end space-x-3">
                  {!volume.published && (
                    <button
                      onClick={() => handlePublishVolume(volume.id)}
                      className="flex items-center px-3 py-1.5 text-sm text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                    >
                      <FaCheck className="mr-1" />
                      Publish
                    </button>
                  )}
                  <button
                    onClick={() => handleDeleteVolume(volume.id)}
                    className="flex items-center px-3 py-1.5 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                  >
                    <FaTrash className="mr-1" />
                    Delete
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Create Volume Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-xl shadow-lg p-6 w-full max-w-md"
          >
            <h2 className="text-xl font-semibold mb-4">Create New Volume</h2>
            <form onSubmit={handleCreateVolume}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Volume Number
                  </label>
                  <input
                    type="number"
                    value={newVolume.number}
                    onChange={(e) => setNewVolume({ ...newVolume, number: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Year
                  </label>
                  <input
                    type="number"
                    value={newVolume.year}
                    onChange={(e) => setNewVolume({ ...newVolume, year: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={newVolume.description}
                    onChange={(e) => setNewVolume({ ...newVolume, description: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                    rows="3"
                    placeholder="Enter a description for this volume..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Cover Image
                  </label>
                  <div
                    {...getRootProps()}
                    className={`mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer hover:border-blue-500 transition-colors ${
                      isDragActive ? 'border-blue-500 bg-blue-50' : ''
                    }`}
                  >
                    <div className="space-y-1 text-center">
                      <FaCloudUploadAlt className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="flex text-sm text-gray-600">
                        <label className="relative cursor-pointer rounded-md font-medium text-blue-600 hover:text-blue-500">
                          <span>Upload a file</span>
                          <input {...getInputProps()} className="sr-only" />
                        </label>
                        <p className="pl-1">or drag and drop</p>
                      </div>
                      <p className="text-xs text-gray-500">
                        PNG, JPG, GIF up to 5MB
                      </p>
                      {newVolume.cover_image && (
                        <div className="mt-2">
                          <p className="text-sm text-gray-600">
                            Selected: {newVolume.cover_image.name}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => {
                    setShowCreateModal(false);
                    setNewVolume({
                      number: '',
                      year: new Date().getFullYear(),
                      description: '',
                      cover_image: null,
                    });
                  }}
                  className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Create Volume
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </div>
  );
} 