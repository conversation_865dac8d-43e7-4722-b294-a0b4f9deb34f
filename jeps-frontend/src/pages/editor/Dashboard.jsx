import React, { useState, useEffect } from 'react';
import { FaFileAlt, FaUsers, FaComments, FaCalendarAlt, FaChartBar, FaChevronRight, FaEllipsisH, FaUpload, FaChartLine, FaClock, FaCheckCircle, FaTimesCircle } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { submissionService } from '../../services/submissionService';
import { manuscriptService } from '../../services/manuscriptService';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { toast } from 'react-hot-toast';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const EditorDashboard = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState([]);
  const [recentSubmissions, setRecentSubmissions] = useState([]);
  const [calendarData, setCalendarData] = useState([]);
  const [submissionsData, setSubmissionsData] = useState([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [submissionsResponse, issuesData] = await Promise.all([
        submissionService.getAllSubmissions(1, 100), // Get first 100 submissions for dashboard stats
        manuscriptService.getIssues()
      ]);

      const submissionsData = submissionsResponse.data || submissionsResponse;
      setSubmissionsData(submissionsData);

      // Process submissions for stats
      const newSubmissions = submissionsData.filter(s => s.status === 'submitted');
      const underReview = submissionsData.filter(s => s.status === 'under_review');
      const pendingDecisions = submissionsData.filter(s => s.status === 'review_complete');
      const publishedArticles = submissionsData.filter(s => s.status === 'published');
      const avgReviewTime = calculateAverageReviewTime(submissionsData);

      // Get upcoming issue
      const upcomingIssue = issuesData.data?.find(issue => 
        new Date(issue.submission_deadline) > new Date()
      );

      setStats([
    { 
      name: 'New Submissions', 
          value: newSubmissions.length, 
      icon: <FaFileAlt className="text-blue-500" />, 
          change: `+${newSubmissions.length} from last week`,
      color: 'bg-blue-100',
      trend: 'up'
    },
    { 
      name: 'Under Review', 
          value: underReview.length, 
      icon: <FaComments className="text-yellow-500" />, 
          change: `${underReview.filter(s => new Date(s.review_deadline) < new Date()).length} overdue`,
      color: 'bg-yellow-100',
      trend: 'neutral'
    },
    { 
      name: 'Pending Decisions', 
          value: pendingDecisions.length, 
          icon: <FaClock className="text-purple-500" />, 
      change: 'Need your action',
      color: 'bg-purple-100',
      trend: 'alert'
    },
        { 
          name: 'Published Articles', 
          value: publishedArticles.length, 
          icon: <FaCheckCircle className="text-green-500" />, 
          change: 'Total published',
          color: 'bg-green-100',
          trend: 'positive'
        },
        { 
          name: 'Avg Review Time', 
          value: `${avgReviewTime} days`, 
          icon: <FaChartLine className="text-indigo-500" />, 
          change: 'Average turnaround',
          color: 'bg-indigo-100',
          trend: 'neutral'
        },
    { 
      name: 'Upcoming Issue', 
          value: upcomingIssue ? `Vol ${upcomingIssue.volume.number}, Issue ${upcomingIssue.number}` : 'None', 
      icon: <FaCalendarAlt className="text-green-500" />, 
          change: upcomingIssue ? `${new Date(upcomingIssue.submission_deadline).toLocaleDateString()} deadline` : 'No upcoming issues',
      color: 'bg-green-100',
      trend: 'positive'
    }
      ]);

      // Process recent submissions
      const recentSubs = submissionsData
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, 4)
        .map(submission => ({
          id: submission.id,
          title: submission.title,
          author: submission.author?.name || 'Unknown Author',
          date: new Date(submission.created_at).toLocaleDateString(),
          status: submission.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
          statusColor: getStatusBadgeClass(submission.status)
        }));

      setRecentSubmissions(recentSubs);

      // Process calendar data
      const calendar = issuesData.data
        ?.filter(issue => new Date(issue.publication_date) >= new Date())
        .map(issue => ({
          issue: `Volume ${issue.volume.number}, Issue ${issue.number}`,
          submissionDeadline: new Date(issue.submission_deadline).toLocaleDateString(),
          reviewDeadline: new Date(issue.review_deadline).toLocaleDateString(),
          publicationDate: new Date(issue.publication_date).toLocaleDateString(),
          status: issue.status,
          statusColor: getStatusBadgeClass(issue.status),
          progress: calculateProgress(issue)
        }));

      setCalendarData(calendar || []);
    } catch (err) {
      console.error('Error loading dashboard data:', err);
      setError(err.message || 'Failed to load dashboard data');
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const calculateAverageReviewTime = (submissions) => {
    const completedReviews = submissions.filter(s => 
      s.status === 'accepted' || s.status === 'rejected' || s.status === 'published'
    );
    
    if (completedReviews.length === 0) return 0;
    
    const totalDays = completedReviews.reduce((sum, submission) => {
      const startDate = new Date(submission.created_at);
      const endDate = new Date(submission.updated_at);
      const daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
      return sum + daysDiff;
    }, 0);
    
    return Math.round(totalDays / completedReviews.length);
  };

  // Chart data generators
  const generateSubmissionTrendChart = () => {
    const last6Months = [];
    const currentDate = new Date();
    
    for (let i = 5; i >= 0; i--) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      last6Months.push({
        month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        submissions: submissionsData.filter(s => {
          const submissionDate = new Date(s.created_at);
          return submissionDate.getMonth() === date.getMonth() && 
                 submissionDate.getFullYear() === date.getFullYear();
        }).length
      });
    }

    return {
      labels: last6Months.map(m => m.month),
      datasets: [
        {
          label: 'Submissions',
          data: last6Months.map(m => m.submissions),
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4,
          fill: true,
        },
      ],
    };
  };

  const generateEditorialDecisionChart = () => {
    const decisions = {
      'Accepted': submissionsData.filter(s => s.status === 'accepted').length,
      'Rejected': submissionsData.filter(s => s.status === 'rejected').length,
      'Revisions Required': submissionsData.filter(s => s.status === 'revisions_required').length,
      'Under Review': submissionsData.filter(s => s.status === 'under_review').length,
      'Published': submissionsData.filter(s => s.status === 'published').length,
    };

    return {
      labels: Object.keys(decisions),
      datasets: [
        {
          data: Object.values(decisions),
          backgroundColor: [
            'rgba(34, 197, 94, 0.8)',
            'rgba(239, 68, 68, 0.8)',
            'rgba(251, 191, 36, 0.8)',
            'rgba(59, 130, 246, 0.8)',
            'rgba(168, 85, 247, 0.8)',
          ],
          borderColor: [
            'rgba(34, 197, 94, 1)',
            'rgba(239, 68, 68, 1)',
            'rgba(251, 191, 36, 1)',
            'rgba(59, 130, 246, 1)',
            'rgba(168, 85, 247, 1)',
          ],
          borderWidth: 2,
        },
      ],
    };
  };

  const generateReviewTimeChart = () => {
    const timeRanges = {
      '0-30 days': 0,
      '31-60 days': 0,
      '61-90 days': 0,
      '90+ days': 0
    };

    submissionsData.forEach(submission => {
      if (submission.status === 'accepted' || submission.status === 'rejected' || submission.status === 'published') {
        const startDate = new Date(submission.created_at);
        const endDate = new Date(submission.updated_at);
        const daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
        
        if (daysDiff <= 30) timeRanges['0-30 days']++;
        else if (daysDiff <= 60) timeRanges['31-60 days']++;
        else if (daysDiff <= 90) timeRanges['61-90 days']++;
        else timeRanges['90+ days']++;
      }
    });

    return {
      labels: Object.keys(timeRanges),
      datasets: [
        {
          label: 'Number of Manuscripts',
          data: Object.values(timeRanges),
          backgroundColor: 'rgba(99, 102, 241, 0.8)',
          borderColor: 'rgba(99, 102, 241, 1)',
          borderWidth: 1,
        },
      ],
    };
  };

  const getStatusBadgeClass = (status) => {
    const statusClasses = {
      'under_review': 'bg-yellow-100 text-yellow-800',
      'accepted': 'bg-green-100 text-green-800',
      'rejected': 'bg-red-100 text-red-800',
      'revisions_required': 'bg-blue-100 text-blue-800',
      'submitted': 'bg-gray-100 text-gray-800',
      'active': 'bg-green-100 text-green-800',
      'planning': 'bg-blue-100 text-blue-800',
      'review_complete': 'bg-purple-100 text-purple-800',
      'published': 'bg-green-100 text-green-800'
    };
    return statusClasses[status] || 'bg-gray-100 text-gray-800';
  };

  const calculateProgress = (issue) => {
    const now = new Date();
    const start = new Date(issue.submission_deadline);
    const end = new Date(issue.publication_date);
    const total = end - start;
    const current = now - start;
    return Math.min(Math.max(Math.round((current / total) * 100), 0), 100);
  };

  const handleManageSubmission = (id) => {
    navigate(`/editor/submissions/${id}/manage`);
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
      x: {
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
    },
  };

  const doughnutOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom',
      },
    },
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <motion.div 
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <h1 className="text-2xl font-bold text-gray-800">Editor Dashboard</h1>
        <p className="text-gray-600 mt-2">Manage the journal workflow and oversee the review process.</p>
      </motion.div>
      
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8"
      >
        {stats.map((stat, index) => (
          <motion.div 
            key={index}
            whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)" }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
            className="bg-white p-6 rounded-xl shadow-md border border-gray-100 hover:border-transparent transition-all"
          >
            <div className="flex items-center">
              <div className={`p-3 rounded-full ${stat.color} mr-4`}>
                {stat.icon}
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                <p className="text-2xl font-semibold text-gray-800 mt-1">{stat.value}</p>
                <div className="flex items-center mt-1">
                  <p className="text-xs text-gray-500 mr-2">{stat.change}</p>
                  {stat.trend === 'up' && (
                    <span className="text-xs text-green-500">↑</span>
                  )}
                  {stat.trend === 'alert' && (
                    <span className="text-xs text-red-500">!</span>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Submission Trends Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white p-6 rounded-xl shadow-md border border-gray-100"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <FaChartLine className="text-blue-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-800">Submission Trends</h3>
            </div>
          </div>
          {submissionsData.length > 0 ? (
            <Line data={generateSubmissionTrendChart()} options={chartOptions} />
          ) : (
            <div className="flex flex-col items-center justify-center py-8">
              <FaChartLine className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-gray-500">No submission data available</p>
            </div>
          )}
        </motion.div>

        {/* Editorial Decisions Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white p-6 rounded-xl shadow-md border border-gray-100"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <FaFileAlt className="text-green-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-800">Editorial Decisions</h3>
            </div>
          </div>
          {submissionsData.length > 0 ? (
            <Doughnut data={generateEditorialDecisionChart()} options={doughnutOptions} />
          ) : (
            <div className="flex flex-col items-center justify-center py-8">
              <FaFileAlt className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-gray-500">No decision data available</p>
            </div>
          )}
        </motion.div>

        {/* Review Time Distribution */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white p-6 rounded-xl shadow-md border border-gray-100"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <FaClock className="text-purple-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-800">Review Time Distribution</h3>
            </div>
          </div>
          {submissionsData.length > 0 ? (
            <Bar data={generateReviewTimeChart()} options={chartOptions} />
          ) : (
            <div className="flex flex-col items-center justify-center py-8">
              <FaClock className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-gray-500">No review time data available</p>
            </div>
          )}
        </motion.div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <motion.div 
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
          className="lg:col-span-2 bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden"
        >
          <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h2 className="text-lg font-medium text-gray-800">Recent Submissions</h2>
          </div>
          <div className="divide-y divide-gray-200">
            {recentSubmissions.map((submission, index) => (
              <motion.div 
                key={submission.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * index }}
                whileHover={{ backgroundColor: "rgba(249, 250, 251, 1)" }}
                className="p-6 transition-colors duration-200"
              >
                <div className="flex items-start">
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-gray-800 mb-1">{submission.title}</h3>
                    <div className="flex flex-wrap items-center gap-3 mt-2 text-sm text-gray-600">
                      <span className="inline-flex items-center">
                        <span className="text-gray-500 mr-1">ID:</span> {submission.id}
                      </span>
                      <span className="inline-flex items-center">
                        <span className="text-gray-500 mr-1">Author:</span> {submission.author}
                      </span>
                      <span className="inline-flex items-center">
                        <span className="text-gray-500 mr-1">Submitted:</span> {submission.date}
                      </span>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${submission.statusColor}`}>
                        {submission.status}
                      </span>
                    </div>
                  </div>
                  <motion.button 
                    whileHover={{ x: 3 }}
                    onClick={() => handleManageSubmission(submission.id)}
                    className="flex items-center text-blue-600 hover:text-blue-800 transition-colors"
                  >
                    Manage
                    <FaChevronRight className="ml-1" />
                  </motion.button>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
        
        <motion.div 
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden"
        >
          <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h2 className="text-lg font-medium text-gray-800">Quick Actions</h2>
          </div>
          <div className="p-6">
            <div className="space-y-3">
              <motion.button 
                whileHover={{ x: 5 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => navigate('/editor/submissions')}
                className="w-full flex items-center justify-between px-4 py-3 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-all"
              >
                <span className="font-medium">Assign Reviewers</span>
                <FaUsers />
              </motion.button>
              <motion.button 
                whileHover={{ x: 5 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => navigate('/editor/submissions')}
                className="w-full flex items-center justify-between px-4 py-3 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-all"
              >
                <span className="font-medium">Make Editorial Decisions</span>
                <FaFileAlt />
              </motion.button>
              <motion.button 
                whileHover={{ x: 5 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => navigate('/editor/issues')}
                className="w-full flex items-center justify-between px-4 py-3 bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-all"
              >
                <span className="font-medium">Plan Next Issue</span>
                <FaCalendarAlt />
              </motion.button>
              <motion.button 
                whileHover={{ x: 5 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => navigate('/editor/analytics')}
                className="w-full flex items-center justify-between px-4 py-3 bg-yellow-50 text-yellow-700 rounded-lg hover:bg-yellow-100 transition-all"
              >
                <span className="font-medium">View Journal Analytics</span>
                <FaChartBar />
              </motion.button>
              <motion.button 
                whileHover={{ x: 5 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => navigate('/editor/direct-publish')}
                className="w-full flex items-center justify-between px-4 py-3 bg-indigo-50 text-indigo-700 rounded-lg hover:bg-indigo-100 transition-all"
              >
                <span className="font-medium">Direct Publish Article</span>
                <FaUpload />
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
      
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
        className="bg-white p-6 rounded-xl shadow-md border border-gray-100"
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-800">Editorial Calendar</h3>
          <button className="text-gray-500 hover:text-gray-700">
            <FaEllipsisH />
          </button>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Progress</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submission Deadline</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Review Deadline</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Publication Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {calendarData.map((item, index) => (
                <motion.tr
                  key={index}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.1 * index }}
                  whileHover={{ backgroundColor: "rgba(249, 250, 251, 1)" }}
                  className="transition-colors duration-200"
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.issue}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          item.progress > 50 ? 'bg-green-500' : 
                          item.progress > 25 ? 'bg-yellow-500' : 'bg-blue-500'
                        }`} 
                        style={{ width: `${item.progress}%` }}
                      ></div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.submissionDeadline}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.reviewDeadline}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.publicationDate}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${item.statusColor}`}>
                      {item.status}
                    </span>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </motion.div>
    </div>
  );
};

export default EditorDashboard;