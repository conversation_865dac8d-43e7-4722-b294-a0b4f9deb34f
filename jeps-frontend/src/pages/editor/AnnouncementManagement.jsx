/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from 'react';
import {
  FaBullhorn,
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye,
  FaSearch,
  FaFilter,
  FaCalendarAlt,
  FaGlobe,
  FaStar,
  FaCheck,
  FaTimes,
  FaSpinner,
  FaLanguage,
  FaExclamationTriangle
} from 'react-icons/fa';
import { motion } from 'framer-motion';
import { toast } from 'react-toastify';
import announcementService from '../../services/announcementService';
import LoadingSpinner from '../../components/common/LoadingSpinner';

const AnnouncementManagement = () => {
  const [announcements, setAnnouncements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'view'
  const [selectedAnnouncement, setSelectedAnnouncement] = useState(null);
  const [selectedItems, setSelectedItems] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    status: '',
    language: '',
    featured: ''
  });
  const [stats, setStats] = useState({});
  const [formData, setFormData] = useState({
    title: { en: '', fr: '' },
    content: { en: '', fr: '' },
    language: 'en',
    status: 'published',
    featured: false,
    priority: 0
  });

  useEffect(() => {
    fetchAnnouncements();
    fetchStats();
  }, [searchTerm, filters]);

  const fetchAnnouncements = async () => {
    try {
      setLoading(true);
      const params = {
        type: 'announcement',
        search: searchTerm,
        ...filters
      };
      
      // Remove empty filters
      Object.keys(params).forEach(key => {
        if (!params[key]) delete params[key];
      });

      const response = await announcementService.getAllContent(params);
      setAnnouncements(response.data || []);
    } catch (error) {
      console.error('Error fetching announcements:', error);
      toast.error('Failed to fetch announcements');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await announcementService.getStats();
      setStats(response.data || {});
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const handleCreate = () => {
    setFormData({
      title: { en: '', fr: '' },
      content: { en: '', fr: '' },
      language: 'en',
      status: 'published',
      featured: false,
      priority: 0
    });
    setSelectedAnnouncement(null);
    setModalMode('create');
    setShowModal(true);
  };

  const handleEdit = (announcement) => {
    setFormData({
      title: announcement.title || { en: '', fr: '' },
      content: announcement.content || { en: '', fr: '' },
      language: announcement.language || 'en',
      status: announcement.status || 'published',
      featured: announcement.featured || false,
      priority: announcement.priority || 0
    });
    setSelectedAnnouncement(announcement);
    setModalMode('edit');
    setShowModal(true);
  };

  const handleView = (announcement) => {
    setSelectedAnnouncement(announcement);
    setModalMode('view');
    setShowModal(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this announcement?')) {
      try {
        await announcementService.deleteAnnouncement(id);
        toast.success('Announcement deleted successfully');
        fetchAnnouncements();
        fetchStats();
      } catch (error) {
        console.error('Error deleting announcement:', error);
        toast.error('Failed to delete announcement');
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (modalMode === 'create') {
        await announcementService.createAnnouncement(formData);
        toast.success('Announcement created successfully');
      } else {
        await announcementService.updateAnnouncement(selectedAnnouncement.id, formData);
        toast.success('Announcement updated successfully');
      }
      setShowModal(false);
      fetchAnnouncements();
      fetchStats();
    } catch (error) {
      console.error('Error saving announcement:', error);
      toast.error('Failed to save announcement');
    }
  };

  const handleBulkAction = async (action) => {
    if (selectedItems.length === 0) {
      toast.warning('Please select items first');
      return;
    }

    try {
      await announcementService.bulkAction(action, selectedItems);
      toast.success(`Bulk ${action} completed successfully`);
      setSelectedItems([]);
      fetchAnnouncements();
      fetchStats();
    } catch (error) {
      console.error('Error performing bulk action:', error);
      toast.error('Failed to perform bulk action');
    }
  };

  const toggleSelectAll = () => {
    if (selectedItems.length === announcements.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(announcements.map(item => item.id));
    }
  };

  const toggleSelectItem = (id) => {
    setSelectedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <FaBullhorn className="mr-3 text-blue-600" />
              Announcement Management
            </h1>
            <p className="text-gray-600 mt-1">
              Manage journal announcements and public notices
            </p>
          </div>
          <button
            onClick={handleCreate}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <FaPlus className="mr-2" />
            New Announcement
          </button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <FaBullhorn className="text-blue-500 text-2xl mr-3" />
              <div>
                <p className="text-sm text-gray-600">Total Announcements</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.by_type?.announcement || 0}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <FaCheck className="text-green-500 text-2xl mr-3" />
              <div>
                <p className="text-sm text-gray-600">Published</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.by_status?.published || 0}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <FaStar className="text-yellow-500 text-2xl mr-3" />
              <div>
                <p className="text-sm text-gray-600">Featured</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.featured_count || 0}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <FaCalendarAlt className="text-purple-500 text-2xl mr-3" />
              <div>
                <p className="text-sm text-gray-600">This Week</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.recent_count || 0}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white p-4 rounded-lg shadow-sm border mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search announcements..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select
              value={filters.status}
              onChange={(e) => setFilters({...filters, status: e.target.value})}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Status</option>
              <option value="published">Published</option>
              <option value="draft">Draft</option>
            </select>
            <select
              value={filters.language}
              onChange={(e) => setFilters({...filters, language: e.target.value})}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Languages</option>
              <option value="en">English</option>
              <option value="fr">French</option>
            </select>
            <select
              value={filters.featured}
              onChange={(e) => setFilters({...filters, featured: e.target.value})}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All</option>
              <option value="true">Featured</option>
              <option value="false">Not Featured</option>
            </select>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedItems.length > 0 && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm text-blue-700">
                {selectedItems.length} item(s) selected
              </span>
              <div className="flex gap-2">
                <button
                  onClick={() => handleBulkAction('publish')}
                  className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                >
                  Publish
                </button>
                <button
                  onClick={() => handleBulkAction('draft')}
                  className="px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700"
                >
                  Draft
                </button>
                <button
                  onClick={() => handleBulkAction('feature')}
                  className="px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700"
                >
                  Feature
                </button>
                <button
                  onClick={() => handleBulkAction('delete')}
                  className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Announcements Table */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="p-4 text-left">
                  <input
                    type="checkbox"
                    checked={selectedItems.length === announcements.length && announcements.length > 0}
                    onChange={toggleSelectAll}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th className="p-4 text-left text-sm font-medium text-gray-700">Title</th>
                <th className="p-4 text-left text-sm font-medium text-gray-700">Status</th>
                <th className="p-4 text-left text-sm font-medium text-gray-700">Language</th>
                <th className="p-4 text-left text-sm font-medium text-gray-700">Featured</th>
                <th className="p-4 text-left text-sm font-medium text-gray-700">Created</th>
                <th className="p-4 text-left text-sm font-medium text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {announcements.map((announcement) => (
                <tr key={announcement.id} className="hover:bg-gray-50">
                  <td className="p-4">
                    <input
                      type="checkbox"
                      checked={selectedItems.includes(announcement.id)}
                      onChange={() => toggleSelectItem(announcement.id)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </td>
                  <td className="p-4">
                    <div>
                      <p className="font-medium text-gray-900">
                        {announcement.title?.en || announcement.title?.fr || 'Untitled'}
                      </p>
                      <p className="text-sm text-gray-500 truncate max-w-xs">
                        {announcement.content?.en || announcement.content?.fr || 'No content'}
                      </p>
                    </div>
                  </td>
                  <td className="p-4">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(announcement.status)}`}>
                      {announcement.status}
                    </span>
                  </td>
                  <td className="p-4">
                    <div className="flex items-center">
                      <FaLanguage className="mr-2 text-gray-400" />
                      <span className="text-sm text-gray-700 uppercase">
                        {announcement.language}
                      </span>
                    </div>
                  </td>
                  <td className="p-4">
                    {announcement.featured ? (
                      <FaStar className="text-yellow-500" />
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </td>
                  <td className="p-4 text-sm text-gray-500">
                    {formatDate(announcement.created_at)}
                  </td>
                  <td className="p-4">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleView(announcement)}
                        className="text-blue-600 hover:text-blue-800"
                        title="View"
                      >
                        <FaEye />
                      </button>
                      <button
                        onClick={() => handleEdit(announcement)}
                        className="text-green-600 hover:text-green-800"
                        title="Edit"
                      >
                        <FaEdit />
                      </button>
                      <button
                        onClick={() => handleDelete(announcement.id)}
                        className="text-red-600 hover:text-red-800"
                        title="Delete"
                      >
                        <FaTrash />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {announcements.length === 0 && (
          <div className="p-8 text-center">
            <FaBullhorn className="mx-auto text-4xl text-gray-400 mb-4" />
            <p className="text-gray-500">No announcements found</p>
            <button
              onClick={handleCreate}
              className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
            >
              Create First Announcement
            </button>
          </div>
        )}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">
                  {modalMode === 'create' ? 'Create Announcement' : 
                   modalMode === 'edit' ? 'Edit Announcement' : 'View Announcement'}
                </h2>
                <button
                  onClick={() => setShowModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <FaTimes />
                </button>
              </div>

              {modalMode === 'view' ? (
                <div className="space-y-4">
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">Title (English)</h3>
                    <p className="text-gray-700">{selectedAnnouncement?.title?.en || 'Not provided'}</p>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">Title (French)</h3>
                    <p className="text-gray-700">{selectedAnnouncement?.title?.fr || 'Not provided'}</p>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">Content (English)</h3>
                    <p className="text-gray-700 whitespace-pre-wrap">{selectedAnnouncement?.content?.en || 'Not provided'}</p>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">Content (French)</h3>
                    <p className="text-gray-700 whitespace-pre-wrap">{selectedAnnouncement?.content?.fr || 'Not provided'}</p>
                  </div>
                  <div className="flex gap-4">
                    <div>
                      <h3 className="font-medium text-gray-900 mb-2">Status</h3>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(selectedAnnouncement?.status)}`}>
                        {selectedAnnouncement?.status}
                      </span>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 mb-2">Language</h3>
                      <p className="text-gray-700 uppercase">{selectedAnnouncement?.language}</p>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 mb-2">Featured</h3>
                      <p className="text-gray-700">{selectedAnnouncement?.featured ? 'Yes' : 'No'}</p>
                    </div>
                  </div>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Title (English) *
                      </label>
                      <input
                        type="text"
                        required
                        value={formData.title.en}
                        onChange={(e) => setFormData({
                          ...formData,
                          title: { ...formData.title, en: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter title in English"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Title (French)
                      </label>
                      <input
                        type="text"
                        value={formData.title.fr}
                        onChange={(e) => setFormData({
                          ...formData,
                          title: { ...formData.title, fr: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter title in French"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Content (English) *
                    </label>
                    <textarea
                      required
                      rows={6}
                      value={formData.content.en}
                      onChange={(e) => setFormData({
                        ...formData,
                        content: { ...formData.content, en: e.target.value }
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter content in English"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Content (French)
                    </label>
                    <textarea
                      rows={6}
                      value={formData.content.fr}
                      onChange={(e) => setFormData({
                        ...formData,
                        content: { ...formData.content, fr: e.target.value }
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter content in French"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Primary Language
                      </label>
                      <select
                        value={formData.language}
                        onChange={(e) => setFormData({...formData, language: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="en">English</option>
                        <option value="fr">French</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Status
                      </label>
                      <select
                        value={formData.status}
                        onChange={(e) => setFormData({...formData, status: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="published">Published</option>
                        <option value="draft">Draft</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Priority
                      </label>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        value={formData.priority}
                        onChange={(e) => setFormData({...formData, priority: parseInt(e.target.value)})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Featured
                      </label>
                      <div className="flex items-center mt-3">
                        <input
                          type="checkbox"
                          checked={formData.featured}
                          onChange={(e) => setFormData({...formData, featured: e.target.checked})}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-700">Feature this announcement</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-3 pt-4 border-t">
                    <button
                      type="button"
                      onClick={() => setShowModal(false)}
                      className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
                    >
                      {modalMode === 'create' ? 'Create' : 'Update'} Announcement
                    </button>
                  </div>
                </form>
              )}
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default AnnouncementManagement; 