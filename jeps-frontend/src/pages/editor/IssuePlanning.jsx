import React, { useState, useEffect } from 'react';
import { FaCalendarAlt, FaPlus, FaEdit, FaTrash, FaCheck, FaTimes, FaBook, FaStar } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { volumeService } from '../../services/volumeService';
import { issueService } from '../../services/issueService';

export default function IssuePlanning() {
  const [volumes, setVolumes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedVolume, setSelectedVolume] = useState(null);
  const [newIssue, setNewIssue] = useState({
    number: '',
    type: 'January',
    submission_deadline: '',
    review_deadline: '',
  });

  useEffect(() => {
    fetchVolumes();
  }, []);

  const fetchVolumes = async () => {
    try {
      const data = await volumeService.getAllVolumes();
      setVolumes(data);
    } catch {
      toast.error('Failed to fetch volumes and issues');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateIssue = async (e) => {
    e.preventDefault();
    try {
      await issueService.createIssue({
        ...newIssue,
        volume_id: selectedVolume.id
      });
      toast.success('Issue created successfully');
      setShowCreateModal(false);
      fetchVolumes();
    } catch (error) {
      toast.error(error.message || 'Failed to create issue');
    }
  };

  const handlePublishIssue = async (volumeId, issueId) => {
    try {
      await issueService.publishIssue(issueId);
      toast.success('Issue published successfully');
      fetchVolumes();
    } catch (error) {
      toast.error(error.message || 'Failed to publish issue');
    }
  };

  const handleDeleteIssue = async (volumeId, issueId) => {
    if (!window.confirm('Are you sure you want to delete this issue?')) return;
    
    try {
      await issueService.deleteIssue(issueId);
      toast.success('Issue deleted successfully');
      fetchVolumes();
    } catch (error) {
      toast.error(error.message || 'Failed to delete issue');
    }
  };

  const handleSetCurrent = async (issueId) => {
    try {
      await issueService.setCurrent(issueId);
      toast.success('Issue set as current successfully');
      fetchVolumes();
    } catch (error) {
      toast.error(error.message || 'Failed to set issue as current');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-8">
        <motion.h1 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-2xl font-bold text-gray-800"
        >
          Issue Planning
        </motion.h1>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => {
            if (volumes.length === 0) {
              toast.error('Please create a volume first');
              return;
            }
            setSelectedVolume(volumes[0]);
            setShowCreateModal(true);
          }}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <FaPlus className="mr-2" />
          New Issue
        </motion.button>
      </div>

      {volumes.length === 0 ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-md border border-gray-100 p-8 text-center"
        >
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="p-4 rounded-full bg-blue-50 text-blue-600">
              <FaBook className="w-8 h-8" />
            </div>
            <h3 className="text-xl font-semibold text-gray-800">No Volumes Yet</h3>
            <p className="text-gray-600 max-w-md">
              You need to create a volume before you can plan issues. Volumes help organize your journal's content by year and number.
            </p>
            <div className="flex space-x-4">
              <a
                href="/editor/volumes"
                className="mt-4 flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <FaBook className="mr-2" />
                Go to Volumes
              </a>
            </div>
          </div>
        </motion.div>
      ) : (
        <div className="space-y-8">
          {volumes.map((volume) => (
            <motion.div
              key={volume.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden"
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-blue-50 text-blue-600 mr-3">
                      <FaBook />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800">
                        Volume {volume.number} ({volume.year})
                      </h3>
                      <p className="text-sm text-gray-500">
                        {volume.issues?.length || 0} Issues
                      </p>
                    </div>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    volume.published 
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {volume.published ? 'Published' : 'Draft'}
                  </span>
                </div>

                {volume.issues?.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500 mb-4">No issues planned for this volume yet.</p>
                    <button
                      onClick={() => {
                        setSelectedVolume(volume);
                        setShowCreateModal(true);
                      }}
                      className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mx-auto"
                    >
                      <FaPlus className="mr-2" />
                      Add First Issue
                    </button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {volume.issues?.map((issue) => (
                      <motion.div
                        key={issue.id}
                        whileHover={{ y: -2 }}
                        className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="p-2 rounded-full bg-blue-50 text-blue-600">
                            <FaCalendarAlt />
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-800">
                              Issue {issue.number} - {issue.type}
                            </h4>
                            <div className="flex items-center space-x-4 text-sm text-gray-500">
                              <span>
                                Submission Deadline: {new Date(issue.submission_deadline).toLocaleDateString()}
                              </span>
                              {issue.review_deadline && (
                                <span>
                                  Review Deadline: {new Date(issue.review_deadline).toLocaleDateString()}
                                </span>
                              )}
                              {issue.publication_date && (
                                <span>
                                  Publication: {new Date(issue.publication_date).toLocaleDateString()}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                            issue.published 
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {issue.published ? 'Published' : 'Draft'}
                          </span>
                          {issue.is_current && (
                            <span className="px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 flex items-center">
                              <FaStar className="mr-1" />
                              Current
                            </span>
                          )}
                          {issue.published && !issue.is_current && (
                            <button
                              onClick={() => handleSetCurrent(issue.id)}
                              className="flex items-center px-3 py-1.5 text-sm text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                            >
                              <FaStar className="mr-1" />
                              Set as Current
                            </button>
                          )}
                          {!issue.published && (
                            <button
                              onClick={() => handlePublishIssue(volume.id, issue.id)}
                              className="flex items-center px-3 py-1.5 text-sm text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                            >
                              <FaCheck className="mr-1" />
                              Publish
                            </button>
                          )}
                          <button
                            onClick={() => handleDeleteIssue(volume.id, issue.id)}
                            className="flex items-center px-3 py-1.5 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                          >
                            <FaTrash className="mr-1" />
                            Delete
                          </button>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Create Issue Modal */}
      {showCreateModal && selectedVolume && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-xl shadow-lg p-6 w-full max-w-md"
          >
            <h2 className="text-xl font-semibold mb-4">Create New Issue</h2>
            <form onSubmit={handleCreateIssue}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Volume
                  </label>
                  <select
                    value={selectedVolume.id}
                    onChange={(e) => setSelectedVolume(volumes.find(v => v.id === parseInt(e.target.value)))}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  >
                    {volumes.map((volume) => (
                      <option key={volume.id} value={volume.id}>
                        Volume {volume.number} ({volume.year})
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Issue Number
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={newIssue.number}
                    onChange={(e) => setNewIssue({ ...newIssue, number: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter issue number"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Issue Type
                  </label>
                  <select
                    value={newIssue.type}
                    onChange={(e) => setNewIssue({ ...newIssue, type: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="January">January</option>
                    <option value="June">June</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Submission Deadline
                  </label>
                  <input
                    type="date"
                    value={newIssue.submission_deadline}
                    onChange={(e) => setNewIssue({ ...newIssue, submission_deadline: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Review Deadline
                  </label>
                  <input
                    type="date"
                    value={newIssue.review_deadline}
                    onChange={(e) => setNewIssue({ ...newIssue, review_deadline: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => {
                    setShowCreateModal(false);
                    setNewIssue({
                      number: '',
                      type: 'January',
                      submission_deadline: '',
                      review_deadline: '',
                    });
                  }}
                  className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Create Issue
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </div>
  );
}