import React, { useState, useEffect } from 'react';
import AsyncSelect from 'react-select/async';
import { 
  FaCreditCard, 
  FaMoneyBillWave, 
  FaSearch, 
  FaFilter, 
  FaDownload, 
  FaEye,
  FaCheckCircle,
  FaTimesCircle,
  FaClock,
  FaPlus,
  FaEdit,
  FaCalendarAlt,
  FaUser,
  FaFileAlt,
  FaChevronLeft,
  FaChevronRight,
  FaSave,
  FaTimes,
  FaExclamationTriangle,
  FaReceipt,
  FaTrashAlt
} from 'react-icons/fa';
import { toast } from 'react-toastify';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { submissionService } from '../../services/submissionService';

const PaymentTracking = () => {
  const [payments, setPayments] = useState([]);
  const [allManuscripts, setAllManuscripts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [showRecordModal, setShowRecordModal] = useState(false);
  const [selectedManuscript, setSelectedManuscript] = useState(null);
  const [recording, setRecording] = useState(false);
  const [editingPayment, setEditingPayment] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(null);
  const [deleting, setDeleting] = useState(false);
  const [paymentData, setPaymentData] = useState({
    amount: 75000,
    currency: 'CFA',
    payment_method: 'mobile_money',
    payment_date: new Date().toISOString().split('T')[0],
    transaction_id: '',
    notes: ''
  });

  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    paid: 0,
    overdue: 0,
    totalRevenue: 0
  });

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter]);

  const fetchData = async () => {
    setLoading(true);
    try {
      console.log('Fetching data...');
      const response = await submissionService.getAllSubmissions(1, 100); // Get first 100 for payment tracking
      console.log('API Response:', response);
      
      const allManuscripts = response.data || response || [];
      console.log('All manuscripts from API:', allManuscripts.length);
      console.log('Sample manuscript:', allManuscripts[0]);
      
      // Store all manuscripts for the dropdown
      setAllManuscripts(allManuscripts);
      
      // Extract payment records from manuscripts - try different data structures
      const paymentRecords = [];
      
      allManuscripts.forEach(manuscript => {
        console.log('Checking manuscript:', manuscript.id, 'payment_status:', manuscript.payment_status, 'payment:', manuscript.payment);
        
        // Check different possible payment structures
        if (manuscript.payment_status === 'paid' || manuscript.status === 'paid') {
          let paymentData = null;
          
          // Try different payment data structures
          if (manuscript.payment) {
            paymentData = manuscript.payment;
          } else if (manuscript.payments && manuscript.payments.length > 0) {
            paymentData = manuscript.payments[0]; // Get the latest payment
          } else {
            // Create a basic payment record even if no detailed payment data
            paymentData = {
              amount: 75000,
              currency: 'CFA',
              method: 'unknown',
              paid_at: manuscript.updated_at,
              transaction_id: '',
              notes: 'Payment recorded'
            };
          }
          
          paymentRecords.push({
            id: paymentData.id || `payment-${manuscript.id}-${Date.now()}`,
            manuscript_id: manuscript.id,
            manuscript: manuscript,
            amount: paymentData.amount || 75000,
            currency: paymentData.currency || 'CFA',
            payment_method: paymentData.method || paymentData.payment_method || 'mobile_money',
            payment_date: paymentData.paid_at || paymentData.payment_date || paymentData.created_at || manuscript.updated_at,
            transaction_id: paymentData.transaction_id || '',
            notes: paymentData.notes || '',
            status: 'completed'
          });
        }
      });
      
      console.log('Payment records found:', paymentRecords.length);
      console.log('Sample payment record:', paymentRecords[0]);
      setPayments(paymentRecords);
      calculateStats(paymentRecords, allManuscripts);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to fetch payment data');
      setPayments([]);
      setAllManuscripts([]);
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (paymentRecords, allManuscripts) => {
    const totalPaid = paymentRecords.length;
    const totalRevenue = paymentRecords.reduce((sum, payment) => sum + payment.amount, 0);
    
    // Calculate pending payments (manuscripts that need payment but haven't paid)
    const pendingCount = allManuscripts.filter(m => 
      (m.status === 'accepted' || m.status === 'payment_pending') && 
      m.payment_status !== 'paid'
    ).length;
    
    // Calculate overdue payments (pending for more than 30 days)
    const overdueCount = allManuscripts.filter(m => {
      if (m.payment_status === 'paid') return false;
      if (m.status !== 'accepted' && m.status !== 'payment_pending') return false;
      
      const acceptedDate = new Date(m.updated_at);
      const daysSinceAccepted = (new Date() - acceptedDate) / (1000 * 60 * 60 * 24);
      return daysSinceAccepted > 30;
    }).length;

    setStats({
      total: totalPaid + pendingCount,
      pending: pendingCount,
      paid: totalPaid,
      overdue: overdueCount,
      totalRevenue
    });
  };

  const filteredPayments = payments.filter(payment => {
    if (!payment) return false;
    
    const title = payment.manuscript?.title || '';
    const authorName = payment.manuscript?.author?.name || payment.manuscript?.author_name || '';
    const submissionId = payment.manuscript?.submission_id || '';
    const transactionId = payment.transaction_id || '';
    
    const matchesSearch = title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         authorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         submissionId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transactionId.toLowerCase().includes(searchTerm.toLowerCase());
    
    let matchesStatus = true;
    if (statusFilter === 'completed') {
      matchesStatus = payment.status === 'completed';
    } else if (statusFilter === 'pending') {
      matchesStatus = payment.status === 'pending';
    }
    
    return matchesSearch && matchesStatus;
  });

  // Pagination
  const totalPages = Math.ceil(filteredPayments.length / itemsPerPage);
  const currentPayments = filteredPayments.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }
    
    return pageNumbers;
  };

  const handleRecordPayment = () => {
    setSelectedManuscript(null);
    setEditingPayment(null);
    setPaymentData({
      amount: 75000,
      currency: 'CFA',
      payment_method: 'mobile_money',
      payment_date: new Date().toISOString().split('T')[0],
      transaction_id: '',
      notes: ''
    });
    setShowRecordModal(true);
  };

  const handleEditPayment = (payment) => {
    setEditingPayment(payment);
    setSelectedManuscript(payment.manuscript);
    setPaymentData({
      amount: payment.amount,
      currency: payment.currency,
      payment_method: payment.payment_method,
      payment_date: payment.payment_date ? payment.payment_date.split('T')[0] : new Date().toISOString().split('T')[0],
      transaction_id: payment.transaction_id || '',
      notes: payment.notes || ''
    });
    setShowRecordModal(true);
  };

  const handleDeletePayment = async (payment) => {
    setDeleting(true);
    try {
      console.log('Deleting payment for manuscript:', payment.manuscript.id);
      
      const response = await submissionService.deletePayment(payment.manuscript.id);
      
      console.log('Delete success response:', response);
      toast.success('Payment deleted successfully!');
      setShowDeleteConfirm(null);
      fetchData(); // Refresh the list
      
    } catch (error) {
      console.error('Error deleting payment:', error);
      toast.error(`Failed to delete payment: ${error.message}`);
    } finally {
      setDeleting(false);
    }
  };

  // React Select options and loading function
  const loadManuscriptOptions = async (inputValue) => {
    try {
      console.log('Loading options with input:', inputValue);
      console.log('All manuscripts count:', allManuscripts.length);
      console.log('Sample manuscripts:', allManuscripts.slice(0, 3));

      // If no input or very short input, return first 20 manuscripts
      if (!inputValue || inputValue.length < 1) {
        const options = allManuscripts.slice(0, 20).map(manuscript => ({
          value: manuscript.id,
          label: `${manuscript.submission_id || 'NO-ID'} - ${manuscript.title || 'Untitled'}`,
          manuscript: manuscript,
          subLabel: `by ${manuscript.author?.name || manuscript.author_name || 'Unknown Author'}`
        }));
        console.log('Returning default options:', options.length);
        return options;
      }

      // Filter manuscripts based on search term
      const filtered = allManuscripts.filter(manuscript => {
        const searchLower = inputValue.toLowerCase();
        const title = manuscript.title || '';
        const submissionId = manuscript.submission_id || '';
        const authorName = manuscript.author?.name || manuscript.author_name || '';
        
        return (
          title.toLowerCase().includes(searchLower) ||
          submissionId.toLowerCase().includes(searchLower) ||
          authorName.toLowerCase().includes(searchLower)
        );
      });

      const options = filtered.slice(0, 10).map(manuscript => ({
        value: manuscript.id,
        label: `${manuscript.submission_id || 'NO-ID'} - ${manuscript.title || 'Untitled'}`,
        manuscript: manuscript,
        subLabel: `by ${manuscript.author?.name || manuscript.author_name || 'Unknown Author'}`
      }));

      console.log('Returning filtered options:', options.length);
      return options;
    } catch (error) {
      console.error('Error loading manuscripts:', error);
      return [];
    }
  };

  // Custom option component for React Select
  const customOption = ({ innerRef, innerProps, data, isSelected, isFocused }) => (
    <div
      ref={innerRef}
      {...innerProps}
      className={`p-3 cursor-pointer transition-colors ${
        isSelected ? 'bg-blue-100' : isFocused ? 'bg-gray-50' : 'bg-white'
      }`}
    >
      <div className="font-medium text-sm text-gray-900">
        {data.label}
      </div>
      <div className="text-xs text-gray-500">
        {data.subLabel}
      </div>
    </div>
  );

  // Custom single value component
  const customSingleValue = ({ data }) => (
    <div>
      <div className="font-medium text-sm text-gray-900">
        {data.manuscript.submission_id} - {data.manuscript.title}
      </div>
      <div className="text-xs text-gray-500">
        by {data.manuscript.author?.name || data.manuscript.author_name}
      </div>
    </div>
  );

  // React Select styles
  const selectStyles = {
    control: (provided, state) => ({
      ...provided,
      minHeight: '42px',
      border: state.isFocused ? '2px solid #3B82F6' : '1px solid #D1D5DB',
      boxShadow: state.isFocused ? '0 0 0 1px #3B82F6' : 'none',
      '&:hover': {
        border: state.isFocused ? '2px solid #3B82F6' : '1px solid #9CA3AF'
      }
    }),
    placeholder: (provided) => ({
      ...provided,
      color: '#9CA3AF',
      fontSize: '14px'
    }),
    loadingMessage: (provided) => ({
      ...provided,
      fontSize: '14px',
      color: '#6B7280'
    }),
    noOptionsMessage: (provided) => ({
      ...provided,
      fontSize: '14px',
      color: '#6B7280'
    })
  };

  const submitPaymentRecord = async (e) => {
    e.preventDefault();
    setRecording(true);

    try {
      console.log('Submitting payment for manuscript:', selectedManuscript.id);
      console.log('Payment data:', paymentData);
      console.log('Editing payment:', editingPayment);
      
      const payloadData = {
        amount: parseFloat(paymentData.amount),
        currency: paymentData.currency,
        payment_method: paymentData.payment_method,
        payment_date: paymentData.payment_date,
        transaction_id: paymentData.transaction_id,
        notes: paymentData.notes
      };
      
      console.log('Sending payload:', payloadData);
      
      let response;
      if (editingPayment) {
        // Update existing payment
        response = await submissionService.updatePayment(selectedManuscript.id, payloadData);
        toast.success('Payment updated successfully!');
      } else {
        // Create new payment
        response = await submissionService.recordPayment(selectedManuscript.id, payloadData);
        toast.success('Payment recorded successfully!');
      }
      
      console.log('Payment success response:', response);
      setShowRecordModal(false);
      setEditingPayment(null);
      fetchData(); // Refresh the list
      
    } catch (error) {
      console.error('Error with payment:', error);
      
      // Handle different error types
      if (error.status === 422) {
        if (error.data && error.data.errors) {
          const errorMessages = Object.values(error.data.errors).flat();
          toast.error(`Validation errors: ${errorMessages.join(', ')}`);
        } else {
          toast.error(error.message || 'Payment validation failed');
        }
      } else {
        toast.error(`Failed to ${editingPayment ? 'update' : 'record'} payment: ${error.message}`);
      }
    } finally {
      setRecording(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setPaymentData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 
              className="text-3xl font-bold text-gray-800 mb-2"
            >
              Payment Tracking
            </h1>
            <p 
              className="text-gray-600"
            >
              Monitor and record publication fee payments from authors
            </p>
          </div>
          <button
            onClick={() => handleRecordPayment()}
            className="inline-flex items-center px-6 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors shadow-lg hover:shadow-xl mt-4 md:mt-0"
          >
            <FaPlus className="mr-2" />
            Record Payment
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div 
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8"
      >
        <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 mr-4">
              <FaFileAlt className="text-blue-500 text-xl" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Total Manuscripts</p>
              <p className="text-2xl font-bold text-gray-800">{stats.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 mr-4">
              <FaClock className="text-yellow-500 text-xl" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Pending</p>
              <p className="text-2xl font-bold text-gray-800">{stats.pending}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 mr-4">
              <FaCheckCircle className="text-green-500 text-xl" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Paid</p>
              <p className="text-2xl font-bold text-gray-800">{stats.paid}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-red-100 mr-4">
              <FaExclamationTriangle className="text-red-500 text-xl" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Overdue</p>
              <p className="text-2xl font-bold text-gray-800">{stats.overdue}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 mr-4">
              <FaMoneyBillWave className="text-purple-500 text-xl" />
            </div>
      <div>
              <p className="text-sm font-medium text-gray-500">Total Revenue</p>
              <p className="text-xl font-bold text-gray-800">{stats.totalRevenue.toLocaleString()} CFA</p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div 
        className="bg-white rounded-xl shadow-lg p-6 mb-8"
      >
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search by title, author, or submission ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 w-full md:w-80"
              />
            </div>

            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Payments</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
            </select>
          </div>
        </div>
      </div>

      {/* Payments Table */}
      <div 
        className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"
      >
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Manuscript
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Author
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment Method
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Transaction ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentPayments.map((payment) => (
                <tr 
                  key={payment.id}
                  className="transition-colors"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {payment.manuscript.submission_id}
                      </div>
                      <div className="text-sm text-gray-500 truncate max-w-xs">
                        {payment.manuscript.title}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                        <FaUser className="text-gray-500 text-sm" />
                      </div>
                      <div className="text-sm font-medium text-gray-900">
                        {payment.manuscript.author?.name || payment.manuscript.author_name}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {(payment.amount).toLocaleString()} {payment.currency}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 capitalize">
                      {payment.payment_method.replace('_', ' ')}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {payment.transaction_id || 'N/A'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(payment.payment_date).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button 
                        onClick={() => handleEditPayment(payment)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                        title="Edit Payment"
                      >
                        <FaEdit />
                      </button>
                      <button 
                        onClick={() => setShowDeleteConfirm(payment)}
                        className="text-red-600 hover:text-red-900 p-1 rounded"
                        title="Delete Payment"
                      >
                        <FaTrashAlt />
                      </button>
                    </div>
                  </td>
              </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing{' '}
                  <span className="font-medium">{(currentPage - 1) * itemsPerPage + 1}</span> to{' '}
                  <span className="font-medium">
                    {Math.min(currentPage * itemsPerPage, filteredPayments.length)}
                  </span>{' '}
                  of <span className="font-medium">{filteredPayments.length}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    <FaChevronLeft className="h-3 w-3" />
                  </button>
                  {getPageNumbers().map((pageNumber) => (
                    <button
                      key={pageNumber}
                      onClick={() => setCurrentPage(pageNumber)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        currentPage === pageNumber
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {pageNumber}
                    </button>
                  ))}
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    <FaChevronRight className="h-3 w-3" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}

        {filteredPayments.length === 0 && (
          <div className="text-center py-12">
            <FaReceipt className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No payment records found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || statusFilter !== 'all' 
                ? 'Try adjusting your search criteria.' 
                : 'No payment records available. Click "Record Payment" to add the first payment.'}
            </p>
          </div>
        )}
      </div>

      {/* Record Payment Modal */}
      {showRecordModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-10 mx-auto p-5 border w-full max-w-lg shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {editingPayment ? 'Edit Payment' : 'Record Payment'}
                </h3>
                <button
                  onClick={() => setShowRecordModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <FaTimes />
                </button>
              </div>

              <form onSubmit={submitPaymentRecord} className="space-y-4">
                {/* Manuscript Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Select Manuscript
                  </label>
                  <AsyncSelect
                    cacheOptions
                    defaultOptions={true}
                    loadOptions={loadManuscriptOptions}
                    value={selectedManuscript ? {
                      value: selectedManuscript.id,
                      label: `${selectedManuscript.submission_id || 'NO-ID'} - ${selectedManuscript.title || 'Untitled'}`,
                      manuscript: selectedManuscript
                    } : null}
                    onChange={(selectedOption) => {
                      console.log('Selected option:', selectedOption);
                      setSelectedManuscript(selectedOption ? selectedOption.manuscript : null);
                    }}
                    placeholder="Search and select a manuscript..."
                    isClearable
                    isSearchable
                    components={{
                      Option: customOption,
                      SingleValue: customSingleValue
                    }}
                    styles={selectStyles}
                    loadingMessage={() => "Loading manuscripts..."}
                    noOptionsMessage={({ inputValue }) => {
                      if (!inputValue) {
                        return allManuscripts.length === 0 ? "No manuscripts available" : "Type to search manuscripts";
                      }
                      return `No manuscripts found for "${inputValue}"`;
                    }}
                    className="mb-4"
                    onInputChange={(inputValue, { action }) => {
                      console.log('Input changed:', inputValue, 'Action:', action);
                    }}
                    onMenuOpen={() => {
                      console.log('Menu opened, allManuscripts count:', allManuscripts.length);
                    }}
                  />
                </div>

                {selectedManuscript && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Amount
                      </label>
                      <div className="flex">
                        <input
                          type="number"
                          name="amount"
                          value={paymentData.amount}
                          onChange={handleInputChange}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-blue-500 focus:border-blue-500"
                          required
                        />
                        <select
                          name="currency"
                          value={paymentData.currency}
                          onChange={handleInputChange}
                          className="px-3 py-2 border border-l-0 border-gray-300 rounded-r-lg focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="CFA">CFA</option>
                          <option value="USD">USD</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Payment Method
                      </label>
                      <select
                        name="payment_method"
                        value={paymentData.payment_method}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                        required
                      >
                        <option value="mobile_money">Mobile Money</option>
                        <option value="bank_transfer">Bank Transfer</option>
                        <option value="cash">Cash</option>
                        <option value="check">Check</option>
                        <option value="online">Online Payment</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Payment Date
                      </label>
                      <input
                        type="date"
                        name="payment_date"
                        value={paymentData.payment_date}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Transaction ID
                      </label>
                      <input
                        type="text"
                        name="transaction_id"
                        value={paymentData.transaction_id}
                        onChange={handleInputChange}
                        placeholder="Enter transaction or reference number"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Notes
                      </label>
                      <textarea
                        name="notes"
                        value={paymentData.notes}
                        onChange={handleInputChange}
                        rows={3}
                        placeholder="Additional payment notes..."
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </>
                )}

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowRecordModal(false)}
                    className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={recording || !selectedManuscript}
                    className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                  >
                    {recording ? (
                      <LoadingSpinner size="sm" className="mr-2" />
                    ) : (
                      <FaSave className="mr-2" />
                    )}
                    {recording ? 'Recording...' : 'Record Payment'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <FaExclamationTriangle className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Delete Payment</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete the payment for manuscript{' '}
                  <span className="font-medium">{showDeleteConfirm.manuscript.submission_id}</span>?
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  Amount: <span className="font-medium">{showDeleteConfirm.amount.toLocaleString()} {showDeleteConfirm.currency}</span>
                </p>
                <p className="text-xs text-red-500 mt-2">This action cannot be undone.</p>
              </div>
              <div className="flex justify-center space-x-3 mt-4">
                <button
                  onClick={() => setShowDeleteConfirm(null)}
                  className="px-4 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                  disabled={deleting}
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleDeletePayment(showDeleteConfirm)}
                  disabled={deleting}
                  className="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-red-700 disabled:opacity-50"
                >
                  {deleting ? (
                    <LoadingSpinner size="sm" className="mr-2" />
                  ) : (
                    <FaTrashAlt className="mr-2" />
                  )}
                  {deleting ? 'Deleting...' : 'Delete'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    );
};

export default PaymentTracking;