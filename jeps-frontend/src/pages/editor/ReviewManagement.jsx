import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  FaUserFriends, FaComments, FaSpinner, FaExclamationTriangle,
  FaCheckCircle, FaClock, FaFileAlt, FaSearch, FaFilter, FaEye,
  FaTasks, FaChartLine, FaCalendarAlt, FaUser, FaStar,
  FaChevronLeft, FaChevronRight
} from 'react-icons/fa';
import { motion } from 'framer-motion';
import { submissionService } from '../../services/submissionService';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { toast } from 'react-toastify';

export default function ReviewManagement() {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [submissions, setSubmissions] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [reviewStatusFilter, setReviewStatusFilter] = useState('all');
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10); // Can be made configurable

  useEffect(() => {
    const fetchSubmissions = async () => {
      try {
        const response = await submissionService.getAllSubmissions(1, 100); // Get first 100 for review management
        const data = response.data || response;
        setSubmissions(data);
      } catch (err) {
        console.error('Error fetching submissions:', err);
        setError(err.message || 'Failed to load submissions');
        toast.error('Failed to load submissions');
      } finally {
        setLoading(false);
      }
    };

    fetchSubmissions();
  }, []);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, statusFilter, reviewStatusFilter]);

  const getStatusBadgeClass = (status) => {
    const statusClasses = {
      'under_review': 'bg-gradient-to-r from-yellow-100 to-yellow-200 text-yellow-800 border border-yellow-300',
      'accepted': 'bg-gradient-to-r from-green-100 to-green-200 text-green-800 border border-green-300',
      'rejected': 'bg-gradient-to-r from-red-100 to-red-200 text-red-800 border border-red-300',
      'revision_requested': 'bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 border border-blue-300',
      'submitted': 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 border border-gray-300'
    };
    return statusClasses[status] || 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 border border-gray-300';
  };

  const getReviewProgressStatus = (submission) => {
    const totalReviewers = submission.reviewers?.length || 0;
    const completedReviews = submission.reviews?.filter(review => review.submitted_at)?.length || 0;
    
    if (completedReviews === 0) return 'none';
    if (completedReviews === totalReviewers) return 'complete';
    return 'partial';
  };

  const getProgressPercentage = (submission) => {
    const totalReviewers = submission.reviewers?.length || 0;
    const completedReviews = submission.reviews?.filter(review => review.submitted_at)?.length || 0;
    return totalReviewers > 0 ? (completedReviews / totalReviewers) * 100 : 0;
  };

  const filteredSubmissions = submissions.filter(submission => {
    const matchesSearch = 
      submission.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      submission.submission_id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      submission.author?.name.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === 'all' || submission.status === statusFilter;
    
    const matchesReviewStatus = reviewStatusFilter === 'all' || 
      (reviewStatusFilter === 'none' && getReviewProgressStatus(submission) === 'none') ||
      (reviewStatusFilter === 'partial' && getReviewProgressStatus(submission) === 'partial') ||
      (reviewStatusFilter === 'complete' && getReviewProgressStatus(submission) === 'complete');

    return matchesSearch && matchesStatus && matchesReviewStatus;
  });

  // Pagination calculations
  const totalSubmissions = filteredSubmissions.length;
  const totalPages = Math.ceil(totalSubmissions / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentSubmissions = filteredSubmissions.slice(startIndex, endIndex);

  // Calculate overview stats (use all submissions, not filtered)
  const allTotalSubmissions = submissions.length;
  const needsReview = submissions.filter(s => s.status === 'under_review').length;
  const completedReviews = submissions.filter(s => getReviewProgressStatus(s) === 'complete').length;
  const pendingReviews = submissions.filter(s => getReviewProgressStatus(s) === 'none' || getReviewProgressStatus(s) === 'partial').length;

  const handlePageChange = (page) => {
    setCurrentPage(page);
    // Scroll to top when page changes
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const generatePageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    
    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      if (currentPage <= 3) {
        for (let i = 1; i <= 4; i++) pages.push(i);
        pages.push('...');
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 2) {
        pages.push(1);
        pages.push('...');
        for (let i = totalPages - 3; i <= totalPages; i++) pages.push(i);
      } else {
        pages.push(1);
        pages.push('...');
        for (let i = currentPage - 1; i <= currentPage + 1; i++) pages.push(i);
        pages.push('...');
        pages.push(totalPages);
      }
    }
    
    return pages;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <FaExclamationTriangle className="mx-auto h-12 w-12 text-red-500" />
          <h2 className="mt-4 text-xl font-semibold text-gray-900">Error Loading Submissions</h2>
          <p className="mt-2 text-sm text-gray-600">{error}</p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-8">
      <div className="max-w-9xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex justify-between items-center mb-8"
        >
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <FaTasks className="mr-3 text-blue-600" />
              Review Management
            </h1>
            <p className="mt-2 text-gray-600">Manage submission reviews and reviewer assignments</p>
          </div>
        </motion.div>

        {/* Overview Stats */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
        >
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-300">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                <FaFileAlt className="w-6 h-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Submissions</p>
                <p className="text-2xl font-bold text-gray-900">{allTotalSubmissions}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-300">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
                <FaClock className="w-6 h-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Needs Review</p>
                <p className="text-2xl font-bold text-gray-900">{needsReview}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-300">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 text-green-600">
                <FaCheckCircle className="w-6 h-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completed Reviews</p>
                <p className="text-2xl font-bold text-gray-900">{completedReviews}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-300">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-orange-100 text-orange-600">
                <FaChartLine className="w-6 h-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Reviews</p>
                <p className="text-2xl font-bold text-gray-900">{pendingReviews}</p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Search and Filters */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8 bg-white rounded-xl shadow-lg border border-gray-100 p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <FaFilter className="text-gray-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Search & Filter</h3>
            </div>
            {totalSubmissions > 0 && (
              <div className="text-sm text-gray-600">
                Showing {startIndex + 1}-{Math.min(endIndex, totalSubmissions)} of {totalSubmissions} submissions
              </div>
            )}
          </div>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaSearch className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                placeholder="Search by title, ID, or author..."
              />
            </div>
            
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="block w-full pl-3 pr-10 py-3 text-base border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 rounded-lg transition-all duration-200 bg-white"
            >
              <option value="all">All Submission Status</option>
              <option value="submitted">Submitted</option>
              <option value="under_review">Under Review</option>
              <option value="revision_requested">Revision Requested</option>
              <option value="accepted">Accepted</option>
              <option value="rejected">Rejected</option>
            </select>

            <select
              value={reviewStatusFilter}
              onChange={(e) => setReviewStatusFilter(e.target.value)}
              className="block w-full pl-3 pr-10 py-3 text-base border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 rounded-lg transition-all duration-200 bg-white"
            >
              <option value="all">All Review Status</option>
              <option value="none">No Reviews</option>
              <option value="partial">Partial Reviews</option>
              <option value="complete">Complete Reviews</option>
            </select>
          </div>
        </motion.div>

        {/* Submissions List */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="space-y-6"
        >
          {currentSubmissions.length > 0 ? (
            currentSubmissions.map((submission, index) => (
              <motion.div
                key={submission.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300"
              >
                <div className="p-6">
                  {/* Header Section */}
                  <div className="flex items-start justify-between mb-6">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <div className="p-2 rounded-lg bg-blue-50 text-blue-600">
                          <FaFileAlt className="w-5 h-5" />
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 leading-tight">
                            {submission.title}
                          </h3>
                          <p className="text-sm text-gray-500 font-medium">
                            ID: {submission.submission_id}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          <FaUser className="mr-1" />
                          <span>{submission.author?.name}</span>
                        </div>
                        <div className="flex items-center">
                          <FaCalendarAlt className="mr-1" />
                          <span>{new Date(submission.created_at).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold ${getStatusBadgeClass(submission.status)}`}>
                        {submission.status.replace('_', ' ').toUpperCase()}
                      </span>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => navigate(`/editor/submissions/${submission.id}`)}
                        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors shadow-md"
                      >
                        <FaEye className="mr-2" />
                        View Details
                      </motion.button>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div className="mb-6">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700">Review Progress</span>
                      <span className="text-sm text-gray-500">
                        {submission.reviews?.filter(review => review.submitted_at)?.length || 0} of {submission.reviewers?.length || 0} completed
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${getProgressPercentage(submission)}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Main Content Grid */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Reviewers Section */}
                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-100">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="text-lg font-semibold text-gray-900 flex items-center">
                          <FaUserFriends className="mr-2 text-blue-600" />
                          Assigned Reviewers
                        </h4>
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                          getReviewProgressStatus(submission) === 'complete' 
                            ? 'bg-green-100 text-green-800 border border-green-200' 
                            : getReviewProgressStatus(submission) === 'partial' 
                            ? 'bg-yellow-100 text-yellow-800 border border-yellow-200' 
                            : 'bg-gray-100 text-gray-800 border border-gray-200'
                        }`}>
                          {getReviewProgressStatus(submission) === 'complete' ? 'Complete' : 
                           getReviewProgressStatus(submission) === 'partial' ? 'In Progress' : 'Pending'}
                        </span>
                      </div>
                      
                      <div className="space-y-3">
                        {submission.reviewers?.length > 0 ? (
                          submission.reviewers.map((reviewer) => (
                            <div key={reviewer.id} className="flex items-center space-x-3 bg-white p-3 rounded-lg shadow-sm border border-gray-100">
                              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-md">
                                <span className="text-white text-sm font-bold">
                                  {reviewer.name.split(' ').map(n => n[0]).join('')}
                                </span>
                              </div>
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium text-gray-900 truncate">{reviewer.name}</p>
                                <p className="text-xs text-gray-500">{reviewer.email}</p>
                              </div>
                              {submission.reviews?.some(r => r.reviewer_id === reviewer.id && r.submitted_at) ? (
                                <div className="flex items-center text-green-600">
                                  <FaCheckCircle className="h-5 w-5" />
                                  <span className="ml-1 text-xs font-medium">Done</span>
                                </div>
                              ) : (
                                <div className="flex items-center text-yellow-600">
                                  <FaClock className="h-5 w-5" />
                                  <span className="ml-1 text-xs font-medium">Pending</span>
                                </div>
                              )}
                            </div>
                          ))
                        ) : (
                          <p className="text-sm text-gray-500 text-center py-4">No reviewers assigned</p>
                        )}
                      </div>
                      
                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => navigate(`/editor/submissions/${submission.id}/assign-reviewers`)}
                        className="mt-4 w-full inline-flex items-center justify-center px-4 py-2.5 bg-white text-blue-700 text-sm font-medium rounded-lg hover:bg-blue-50 transition-colors shadow-sm border border-blue-200"
                      >
                        <FaUserFriends className="mr-2" />
                        Manage Reviewers
                      </motion.button>
                    </div>

                    {/* Reviews Section */}
                    <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-5 border border-green-100">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="text-lg font-semibold text-gray-900 flex items-center">
                          <FaComments className="mr-2 text-green-600" />
                          Reviews
                        </h4>
                        {submission.reviews?.length > 0 && (
                          <span className="text-sm text-gray-600">
                            {submission.reviews.length} review{submission.reviews.length !== 1 ? 's' : ''}
                          </span>
                        )}
                      </div>
                      
                      <div className="space-y-3 max-h-64 overflow-y-auto">
                        {submission.reviews?.length > 0 ? (
                          submission.reviews.map((review) => (
                            <div key={review.id} className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                              <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center space-x-3">
                                  <div className="w-8 h-8 rounded-full bg-gradient-to-br from-green-500 to-emerald-600 flex items-center justify-center">
                                    <span className="text-white text-xs font-bold">
                                      {review.reviewer?.name.split(' ').map(n => n[0]).join('')}
                                    </span>
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium text-gray-900">{review.reviewer?.name}</p>
                                    <p className="text-xs text-gray-500">
                                      {new Date(review.submitted_at).toLocaleDateString()}
                                    </p>
                                  </div>
                                </div>
                                <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                                  review.recommendation === 'accept' ? 'bg-green-100 text-green-800 border border-green-200' :
                                  review.recommendation === 'minor' ? 'bg-blue-100 text-blue-800 border border-blue-200' :
                                  review.recommendation === 'major' ? 'bg-yellow-100 text-yellow-800 border border-yellow-200' :
                                  'bg-red-100 text-red-800 border border-red-200'
                                }`}>
                                  {review.recommendation === 'accept' && <FaStar className="mr-1" />}
                                  {review.recommendation.charAt(0).toUpperCase() + review.recommendation.slice(1)}
                                </span>
                              </div>
                              {review.feedback && (
                                <div className="mt-2 text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                                  <div 
                                    className="line-clamp-2"
                                    dangerouslySetInnerHTML={{ 
                                      __html: review.feedback.length > 120 
                                        ? review.feedback.substring(0, 120) + '...' 
                                        : review.feedback 
                                    }} 
                                  />
                                </div>
                              )}
                            </div>
                          ))
                        ) : (
                          <p className="text-sm text-gray-500 text-center py-8">No reviews submitted yet</p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))
          ) : (
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-xl shadow-lg border border-gray-100 text-center py-16"
            >
              <FaFileAlt className="mx-auto h-16 w-16 text-gray-400 mb-4" />
              <h3 className="text-xl font-medium text-gray-900 mb-2">No submissions found</h3>
              <p className="text-gray-500 max-w-md mx-auto">
                {searchQuery || statusFilter !== 'all' || reviewStatusFilter !== 'all' 
                  ? 'No submissions match your search criteria. Try adjusting your filters.'
                  : 'There are no submissions that require review management at the moment.'}
              </p>
            </motion.div>
          )}
        </motion.div>

        {/* Pagination */}
        {totalPages > 1 && (
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mt-12 flex items-center justify-between bg-white rounded-xl shadow-lg border border-gray-100 px-6 py-4"
          >
            <div className="flex items-center text-sm text-gray-600">
              <span>
                Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
                <span className="font-medium">{Math.min(endIndex, totalSubmissions)}</span> of{' '}
                <span className="font-medium">{totalSubmissions}</span> results
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              {/* Previous Button */}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                  currentPage === 1
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                }`}
              >
                <FaChevronLeft className="mr-1" />
                Previous
              </motion.button>
              
              {/* Page Numbers */}
              <div className="flex items-center space-x-1">
                {generatePageNumbers().map((page, index) => (
                  <motion.button
                    key={index}
                    whileHover={page !== '...' ? { scale: 1.1 } : {}}
                    whileTap={page !== '...' ? { scale: 0.9 } : {}}
                    onClick={() => page !== '...' && handlePageChange(page)}
                    disabled={page === '...' || page === currentPage}
                    className={`w-10 h-10 text-sm font-medium rounded-lg transition-colors ${
                      page === currentPage
                        ? 'bg-blue-600 text-white'
                        : page === '...'
                        ? 'text-gray-400 cursor-default'
                        : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </motion.button>
                ))}
              </div>
              
              {/* Next Button */}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                  currentPage === totalPages
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                }`}
              >
                Next
                <FaChevronRight className="ml-1" />
              </motion.button>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}