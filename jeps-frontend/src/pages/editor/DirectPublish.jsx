import { useState, useEffect, useRef } from 'react';
import { FaUpload, FaPlus, FaTrash, FaSearch, FaUserPlus, FaTimes, FaSpinner } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { motion } from 'framer-motion';
import { manuscriptService } from '../../services/manuscriptService';
import { volumeService } from '../../services/volumeService';
import { issueService } from '../../services/issueService';
import userService from '../../services/userService';

const DirectPublish = () => {
  const [formData, setFormData] = useState({
    title: '',
    abstract: '',
    keywords: [],
    authors: [{ name: '', email: '', affiliation: '' }],
    manuscript: null,
    volume_id: '',
    issue_id: '',
    doi: '',
    publication_date: '',
    featured: false
  });

  const [loading, setLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [existingAuthors, setExistingAuthors] = useState([]);
  const [volumes, setVolumes] = useState([]);
  const [issues, setIssues] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAuthorSearch, setShowAuthorSearch] = useState(false);
  const [selectedAuthor, setSelectedAuthor] = useState(null);
  const keywordInputRef = useRef(null);
  const [keywordInput, setKeywordInput] = useState('');

  useEffect(() => {
    fetchExistingAuthors();
    fetchVolumes();
  }, []);

  useEffect(() => {
    if (formData.volume_id) {
      fetchIssues(formData.volume_id);
    }
  }, [formData.volume_id]);

  const fetchExistingAuthors = async () => {
    try {
      const response = await userService.getAuthors();
      setExistingAuthors(response.data);
    } catch (error) {
      console.error('Error fetching authors:', error);
      toast.error('Failed to load existing authors');
    }
  };

  const fetchVolumes = async () => {
    try {
      const data = await volumeService.getAllVolumes();
      setVolumes(data);
    } catch (error) {
      console.error('Error fetching volumes:', error);
      toast.error('Failed to load volumes');
    }
  };

  const fetchIssues = async (volumeId) => {
    try {
      const data = await issueService.getVolumeIssues(volumeId);
      setIssues(data);
    } catch (error) {
      console.error('Error fetching issues:', error);
      toast.error('Failed to load issues');
    }
  };

  const handleAuthorSearch = (query) => {
    setSearchQuery(query);
    setShowAuthorSearch(true);
  };

  const handleAuthorSelect = (author) => {
    setSelectedAuthor(author);
    setShowAuthorSearch(false);
    setSearchQuery('');
  };

  const handleAddSelectedAuthor = () => {
    if (selectedAuthor) {
      const newAuthor = {
        name: selectedAuthor.name,
        email: selectedAuthor.email,
        affiliation: selectedAuthor.affiliation || ''
      };
      let updatedAuthors = [...formData.authors, newAuthor];
      // Remove any empty author objects
      updatedAuthors = updatedAuthors.filter(a => a.name || a.email || a.affiliation);
      setFormData(prev => ({
        ...prev,
        authors: updatedAuthors
      }));
      setSelectedAuthor(null);
    }
  };

  const handleAuthorChange = (index, field, value) => {
    const newAuthors = [...formData.authors];
    newAuthors[index] = { ...newAuthors[index], [field]: value };
    setFormData(prev => ({ ...prev, authors: newAuthors }));
  };

  const handleAddAuthor = () => {
    setFormData(prev => ({
      ...prev,
      authors: [...prev.authors, { name: '', email: '', affiliation: '' }]
    }));
  };

  const handleRemoveAuthor = (index) => {
    setFormData(prev => ({
      ...prev,
      authors: prev.authors.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setUploadProgress(0);

    try {
      const formDataToSend = new FormData();
      
      // Convert keywords array to comma-separated string
      formDataToSend.append('keywords', formData.keywords.join(','));
      
      // Convert authors array to JSON string
      formDataToSend.append('authors', JSON.stringify(formData.authors));
      
      // Add other fields
      formDataToSend.append('title', formData.title);
      formDataToSend.append('abstract', formData.abstract);
      formDataToSend.append('volume', formData.volume_id);
      formDataToSend.append('issue', formData.issue_id);
      formDataToSend.append('doi', formData.doi);
      formDataToSend.append('publishedDate', formData.publication_date);
      formDataToSend.append('featured', formData.featured ? 1 : 0);
      
      if (formData.manuscript) {
        formDataToSend.append('manuscript', formData.manuscript);
      }

      
      await manuscriptService.directPublish(formDataToSend);
      toast.success('Manuscript published successfully');
      
      // Reset form
      setFormData({
        title: '',
        abstract: '',
        keywords: [],
        authors: [{ name: '', email: '', affiliation: '' }],
        manuscript: null,
        volume_id: '',
        issue_id: '',
        doi: '',
        publication_date: '',
        featured: false
      });
      setUploadProgress(0);
    } catch (error) {
      console.error('Error publishing manuscript:', error);
      toast.error(error.response?.data?.message || 'Failed to publish manuscript');
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.type !== 'application/pdf') {
        toast.error('Please upload a PDF file');
        return;
      }
      
      if (file.size > 10 * 1024 * 1024) { // 10MB in bytes
        toast.error('File size must be less than 10MB');
        return;
      }

      setFormData(prev => ({ ...prev, manuscript: file }));
      
      // Simulate upload progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.random() * 10;
        if (progress >= 100) {
          clearInterval(interval);
          setUploadProgress(100);
        } else {
          setUploadProgress(progress);
        }
      }, 200);
    }
  };

  const handleKeywordInputChange = (e) => {
    setKeywordInput(e.target.value);
  };

  const handleKeywordInputKeyDown = (e) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      if (keywordInput.trim() !== '') {
        setFormData(prev => ({
          ...prev,
          keywords: [...prev.keywords, keywordInput.trim()],
          keywordInput: ''
        }));
      }
    }
  };

  const removeKeyword = (index) => {
    setFormData(prev => ({
      ...prev,
      keywords: prev.keywords.filter((_, i) => i !== index)
    }));
  };

  return (
    <div className="max-w-full mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-xl shadow-lg p-8"
      >
        <h1 className="text-2xl font-bold text-gray-800 mb-6">Direct Publish Manuscript</h1>
        
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-700">Basic Information</h2>
            <div className="grid grid-cols-1 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Title
                </label>
                <input
                  type="text"
                  required
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter manuscript title"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Abstract
                </label>
                <textarea
                  required
                  value={formData.abstract}
                  onChange={(e) => setFormData(prev => ({ ...prev, abstract: e.target.value }))}
                  rows="4"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter manuscript abstract"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Keywords <span className="text-red-500">*</span>
                  <span className="ml-2 text-xs text-gray-500">(up to 5 keywords)</span>
                </label>
                <div className="min-h-[42px] px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus-within:ring-1 focus-within:ring-blue-500 focus-within:border-blue-500">
                  <div className="flex flex-wrap gap-2">
                    {formData.keywords.map((keyword, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                      >
                        {keyword}
                        <button
                          type="button"
                          onClick={() => removeKeyword(index)}
                          className="ml-1.5 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-blue-200 focus:outline-none"
                        >
                          <FaTimes className="w-3 h-3" />
                        </button>
                      </span>
                    ))}
                    <input
                      ref={keywordInputRef}
                      type="text"
                      value={keywordInput}
                      onChange={handleKeywordInputChange}
                      onKeyDown={handleKeywordInputKeyDown}
                      className="flex-1 min-w-[120px] outline-none bg-transparent"
                      placeholder={formData.keywords.length === 0 ? "Type keywords and press Enter or comma" : ""}
                    />
                  </div>
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Press Enter or comma to add a keyword. Maximum 5 keywords allowed.
                </p>
              </div>
            </div>
          </div>

          {/* Authors Section */}
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-700">Authors</h2>
              <div className="flex space-x-4">
                <div className="relative">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => handleAuthorSearch(e.target.value)}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Search existing authors..."
                  />
                  {showAuthorSearch && searchQuery && (
                    <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                      {existingAuthors
                        .filter(author => 
                          author.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          author.email.toLowerCase().includes(searchQuery.toLowerCase())
                        )
                        .map(author => (
                          <div
                            key={author.id}
                            onClick={() => handleAuthorSelect(author)}
                            className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                          >
                            <div className="font-medium">{author.name}</div>
                            <div className="text-sm text-gray-600">{author.email}</div>
                          </div>
                        ))}
                    </div>
                  )}
                </div>
                {selectedAuthor && (
                  <button
                    type="button"
                    onClick={handleAddSelectedAuthor}
                    className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                  >
                    <FaUserPlus className="mr-2" />
                    Add Selected Author
                  </button>
                )}
                <button
                  type="button"
                  onClick={handleAddAuthor}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  <FaPlus className="mr-2" />
                  Add New Author
                </button>
              </div>
            </div>

            <div className="space-y-4">
              {formData.authors.map((author, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Name
                    </label>
                    <input
                      type="text"
                      required
                      value={author.name}
                      onChange={(e) => handleAuthorChange(index, 'name', e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Author name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email
                    </label>
                    <input
                      type="email"
                      required
                      value={author.email}
                      onChange={(e) => handleAuthorChange(index, 'email', e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Author email"
                    />
                  </div>
                  <div className="flex items-end space-x-2">
                    <div className="flex-1">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Affiliation
                      </label>
                      <input
                        type="text"
                        value={author.affiliation}
                        onChange={(e) => handleAuthorChange(index, 'affiliation', e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Author affiliation"
                      />
                    </div>
                    {index > 0 && (
                      <button
                        type="button"
                        onClick={() => handleRemoveAuthor(index)}
                        className="p-2 text-red-600 hover:text-red-800"
                      >
                        <FaTrash />
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Publication Details */}
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-700">Publication Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Volume
                </label>
                <select
                  required
                  value={formData.volume_id}
                  onChange={(e) => setFormData(prev => ({ ...prev, volume_id: e.target.value, issue_id: '' }))}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select Volume</option>
                  {volumes.map(volume => (
                    <option key={volume.id} value={volume.id}>
                      Volume {volume.number} ({volume.year})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Issue
                </label>
                <select
                  required
                  value={formData.issue_id}
                  onChange={(e) => setFormData(prev => ({ ...prev, issue_id: e.target.value }))}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  disabled={!formData.volume_id}
                >
                  <option value="">Select Issue</option>
                  {issues.map(issue => (
                    <option key={issue.id} value={issue.id}>
                      {issue.type} Issue
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Publication Date
                </label>
                <input
                  type="date"
                  required
                  value={formData.publication_date}
                  onChange={(e) => setFormData(prev => ({ ...prev, publication_date: e.target.value }))}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="featured"
                  checked={formData.featured}
                  onChange={(e) => setFormData(prev => ({ ...prev, featured: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="featured" className="ml-2 block text-sm text-gray-700">
                  Feature this article on the homepage
                </label>
              </div>
            </div>
          </div>

          {/* Manuscript Upload */}
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-700">Manuscript Upload</h2>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Upload Manuscript (PDF)
              </label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg">
                <div className="space-y-1 text-center">
                  <FaUpload className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="flex text-sm text-gray-600">
                    <label
                      htmlFor="manuscript-upload"
                      className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                    >
                      <span>Upload a file</span>
                      <input
                        id="manuscript-upload"
                        name="manuscript"
                        type="file"
                        accept=".pdf"
                        required
                        onChange={handleFileChange}
                        className="sr-only"
                      />
                    </label>
                    <p className="pl-1">or drag and drop</p>
                  </div>
                  <p className="text-xs text-gray-500">PDF up to 10MB</p>
                  {formData.manuscript && (
                    <div className="mt-2">
                      <p className="text-sm text-gray-600">{formData.manuscript.name}</p>
                      {uploadProgress > 0 && uploadProgress < 100 && (
                        <div className="mt-2">
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div
                              className="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
                              style={{ width: `${uploadProgress}%` }}
                            ></div>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">Uploading... {Math.round(uploadProgress)}%</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
            >
              {loading ? (
                <>
                  <FaSpinner className="animate-spin mr-2" />
                  Publishing...
                </>
              ) : (
                'Publish Manuscript'
              )}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
};

export default DirectPublish; 