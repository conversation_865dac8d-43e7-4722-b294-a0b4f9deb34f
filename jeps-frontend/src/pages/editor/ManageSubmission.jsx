import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import {
  FaFileAlt, FaUser, FaCalendar, FaEdit, FaDownload, FaComments,
  FaCheckCircle, FaTimesCircle, FaClock, FaExclamationTriangle,
  FaArrowLeft, FaSave, FaEye, FaUsers, FaHistory
} from 'react-icons/fa';
import { submissionService } from '../../services/submissionService';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { motion } from 'framer-motion';
const ManageSubmission = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [submission, setSubmission] = useState(null);
  const [error, setError] = useState(null);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [newStatus, setNewStatus] = useState('');
  const [statusComment, setStatusComment] = useState('');
  const [updating, setUpdating] = useState(false);

  useEffect(() => {
    loadSubmission();
  }, [id]);

  const loadSubmission = async () => {
    try {
      setLoading(true);
      const data = await submissionService.getSubmission(id);
      setSubmission(data);
      setNewStatus(data.status);
    } catch (err) {
      setError(err.message || 'Failed to load submission');
      toast.error('Failed to load submission');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async () => {
    if (newStatus === submission.status) {
      setShowStatusModal(false);
      return;
    }

    try {
      setUpdating(true);
      await submissionService.updateSubmissionStatus(id, newStatus);
      toast.success('Status updated successfully');
      setShowStatusModal(false);
      loadSubmission(); // Refresh data
    } catch (err) {
      toast.error(err.message || 'Failed to update status');
    } finally {
      setUpdating(false);
    }
  };

  const getStatusInfo = (status) => {
    const statusConfig = {
      'submitted': {
        color: 'bg-gray-100 text-gray-800',
        icon: <FaFileAlt className="text-gray-500" />,
        label: 'Submitted'
      },
      'under_review': {
        color: 'bg-yellow-100 text-yellow-800',
        icon: <FaClock className="text-yellow-500" />,
        label: 'Under Review'
      },
      'revisions_required': {
        color: 'bg-orange-100 text-orange-800',
        icon: <FaEdit className="text-orange-500" />,
        label: 'Revisions Required'
      },
      'accepted': {
        color: 'bg-green-100 text-green-800',
        icon: <FaCheckCircle className="text-green-500" />,
        label: 'Accepted'
      },
      'rejected': {
        color: 'bg-red-100 text-red-800',
        icon: <FaTimesCircle className="text-red-500" />,
        label: 'Rejected'
      },
      'payment_pending': {
        color: 'bg-purple-100 text-purple-800',
        icon: <FaClock className="text-purple-500" />,
        label: 'Payment Pending'
      },
      'published': {
        color: 'bg-blue-100 text-blue-800',
        icon: <FaCheckCircle className="text-blue-500" />,
        label: 'Published'
      }
    };
    return statusConfig[status] || statusConfig['submitted'];
  };

  const getStatusOptions = () => {
    return [
      { value: 'submitted', label: 'Submitted' },
      { value: 'under_review', label: 'Under Review' },
      { value: 'revisions_required', label: 'Revisions Required' },
      { value: 'accepted', label: 'Accepted' },
      { value: 'rejected', label: 'Rejected' },
      { value: 'payment_pending', label: 'Payment Pending' },
      { value: 'published', label: 'Published' }
    ];
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-6">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex items-center">
              <FaExclamationTriangle className="text-red-500 mr-3" />
              <div>
                <h3 className="text-lg font-medium text-red-800">Error</h3>
                <p className="text-red-700">{error}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const statusInfo = getStatusInfo(submission.status);

  return (
    <div className="min-h-screen bg-gray-50 py-6">
      <div className="max-w-9xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <button
            onClick={() => navigate('/editor/submissions')}
            className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4"
          >
            <FaArrowLeft className="mr-2" />
            Back to Submissions
          </button>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Manage Submission</h1>
              <p className="text-gray-600 mt-2">ID: {submission.submission_id}</p>
            </div>
            <div className="flex items-center space-x-4">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusInfo.color}`}>
                {statusInfo.icon}
                <span className="ml-2">{statusInfo.label}</span>
              </span>
              <button
                onClick={() => setShowStatusModal(true)}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <FaEdit className="mr-2" />
                Change Status
              </button>
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <motion.div 
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="lg:col-span-2 space-y-6"
          >
            {/* Manuscript Details */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-lg font-medium text-gray-900">Manuscript Details</h2>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">{submission.title}</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Author</label>
                    <div className="flex items-center">
                      <FaUser className="text-gray-400 mr-2" />
                      <span className="text-gray-900">{submission.author?.name || 'Unknown'}</span>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Submitted</label>
                    <div className="flex items-center">
                      <FaCalendar className="text-gray-400 mr-2" />
                      <span className="text-gray-900">
                        {new Date(submission.created_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>

                {submission.abstract && (
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Abstract</label>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <p className="text-gray-700 leading-relaxed">{submission.abstract}</p>
                    </div>
                  </div>
                )}

                {submission.keywords && submission.keywords.length > 0 && (
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Keywords</label>
                    <div className="flex flex-wrap gap-2">
                      {submission.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* File Download */}
                {submission.file_path && (
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Manuscript File</label>
                    <button
                      onClick={() => window.open(`/api/v1/manuscripts/${submission.id}/download`, '_blank')}
                      className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                    >
                      <FaDownload className="mr-2" />
                      Download Manuscript
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Version History */}
            {submission.versions && submission.versions.length > 0 && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                  <h2 className="text-lg font-medium text-gray-900 flex items-center">
                    <FaHistory className="mr-2" />
                    Version History
                  </h2>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {submission.versions.map((version) => (
                      <div key={version.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                          <h4 className="font-medium text-gray-900">Version {version.version_number}</h4>
                          <p className="text-sm text-gray-500">
                            Submitted: {new Date(version.submitted_at).toLocaleDateString()}
                          </p>
                          {version.revision_notes && (
                            <p className="text-sm text-gray-600 mt-1">{version.revision_notes}</p>
                          )}
                        </div>
                        <button
                          onClick={() => window.open(`/api/v1/manuscripts/${submission.id}/versions/${version.version_number}/download`, '_blank')}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          <FaDownload />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </motion.div>

          {/* Sidebar */}
          <motion.div 
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            {/* Quick Actions */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-lg font-medium text-gray-900">Quick Actions</h2>
              </div>
              <div className="p-6 space-y-3">
                <button
                  onClick={() => navigate(`/editor/submissions/${id}/assign-reviewers`)}
                  className="w-full flex items-center justify-between px-4 py-3 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors"
                >
                  <span>Assign Reviewers</span>
                  <FaUsers />
                </button>
                <button
                  onClick={() => navigate(`/editor/submissions/${id}/reviews`)}
                  className="w-full flex items-center justify-between px-4 py-3 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors"
                >
                  <span>View Reviews</span>
                  <FaComments />
                </button>
                <button
                  onClick={() => navigate(`/submission/${id}`)}
                  className="w-full flex items-center justify-between px-4 py-3 bg-gray-50 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <span>Preview Submission</span>
                  <FaEye />
                </button>
              </div>
            </div>

            {/* Submission Info */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-lg font-medium text-gray-900">Submission Info</h2>
              </div>
              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Status</label>
                  <div className="mt-1 flex items-center">
                    {statusInfo.icon}
                    <span className="ml-2 text-gray-900">{statusInfo.label}</span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Submission ID</label>
                  <p className="mt-1 text-gray-900">{submission.submission_id}</p>
                </div>
                {submission.issue && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Target Issue</label>
                    <p className="mt-1 text-gray-900">
                      Volume {submission.issue.volume?.number}, Issue {submission.issue.number}
                    </p>
                  </div>
                )}
                <div>
                  <label className="block text-sm font-medium text-gray-700">Language</label>
                  <p className="mt-1 text-gray-900">{submission.language || 'English'}</p>
                </div>
              </div>
            </div>

            {/* Reviewers */}
            {submission.reviewers && submission.reviewers.length > 0 && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                  <h2 className="text-lg font-medium text-gray-900">Assigned Reviewers</h2>
                </div>
                <div className="p-6">
                  <div className="space-y-3">
                    {submission.reviewers.map((reviewer) => (
                      <div key={reviewer.id} className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-blue-600 text-sm font-medium">
                              {reviewer.name.split(' ').map(n => n[0]).join('')}
                            </span>
                          </div>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">{reviewer.name}</p>
                          <p className="text-xs text-gray-500">{reviewer.email}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        </div>

        {/* Status Change Modal */}
        {showStatusModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-bold text-gray-900">Change Status</h3>
                <button
                  onClick={() => setShowStatusModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <FaTimesCircle size={24} />
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Current Status: {getStatusInfo(submission.status).label}
                  </label>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    New Status
                  </label>
                  <select
                    value={newStatus}
                    onChange={(e) => setNewStatus(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {getStatusOptions().map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Comment (Optional)
                  </label>
                  <textarea
                    value={statusComment}
                    onChange={(e) => setStatusComment(e.target.value)}
                    placeholder="Add a comment about this status change..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    rows={3}
                  />
                </div>
                
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    onClick={() => setShowStatusModal(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleStatusChange}
                    disabled={updating || newStatus === submission.status}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {updating ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Updating...
                      </>
                    ) : (
                      <>
                        <FaSave className="mr-2" />
                        Update Status
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ManageSubmission; 