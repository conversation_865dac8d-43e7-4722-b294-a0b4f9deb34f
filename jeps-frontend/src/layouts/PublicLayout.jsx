import React, { createContext, useContext, useState } from "react";
import { Outlet, Link } from "react-router-dom";
import Header from "../components/Header";
import Footer from "../components/Footer";
import QuickActionsSidebar from "../components/common/QuickActionsSidebar";

// Create context for sidebar state
const SidebarContext = createContext();

export const useSidebar = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
};

const PublicLayout = () => {
  const [isCollapsed, setIsCollapsed] = useState(true); // Sidebar collapsed by default

  return (
    <SidebarContext.Provider value={{ isCollapsed, setIsCollapsed }}>
      <div className="min-h-screen flex flex-col">
        <Header />
        <div className="flex-grow relative">
          <main className={`transition-all duration-300 ${
            isCollapsed ? 'lg:pr-16' : 'lg:pr-80'
          }`}>
            <Outlet /> {/* This renders the child routes */}
          </main>
          <QuickActionsSidebar />
        </div>
        <Footer />
      </div>
    </SidebarContext.Provider>
  );
};

export default PublicLayout;