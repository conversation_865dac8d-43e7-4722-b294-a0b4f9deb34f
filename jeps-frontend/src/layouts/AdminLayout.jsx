import React, { useState, useEffect } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { 
  FaHome, FaUser, FaUsers, FaFileAlt, FaSearch, 
  FaChartBar, FaCog, FaComments, FaCalendarAlt,
  FaBook, FaMoneyBillWave, FaEnvelope, FaDatabase,
  FaCheckCircle, FaBell, FaSignOutAlt, FaChevronDown,
  FaChevronRight, FaUserShield, FaTasks, FaFileUpload,
  FaFileDownload, FaClipboardCheck, FaVolumeUp,
  FaChevronLeft,
  FaBookOpen, FaBars, FaTimes, FaUpload, FaBullhorn
} from 'react-icons/fa';
import { motion } from 'framer-motion';
import authService from '../services/authService';
import { toast } from 'react-toastify';
import { formatDistanceToNow } from 'date-fns';
import { useAuth } from '../contexts/AuthContext';
import reviewService from '../services/reviewService';
import { manuscriptService } from '../services/manuscriptService';
import { submissionService } from '../services/submissionService';

const AdminLayout = ({ role }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [profileOpen, setProfileOpen] = useState(false);
  const { user, loading } = useAuth();
  const [completedReviewsCount, setCompletedReviewsCount] = useState(0);
  const [manuscriptsUnderReview, setManuscriptsUnderReview] = useState(0);
  const [manuscriptsCount, setManuscriptsCount] = useState(0);
  const [assignedReviewsCount, setAssignedReviewsCount] = useState(0);
  const [pendingReviewsCount, setPendingReviewsCount] = useState(0);
  const [totalSubmissionsCount, setTotalSubmissionsCount] = useState(0);
  const [pendingPaymentsCount, setPendingPaymentsCount] = useState(0);

  useEffect(() => {
    if (!loading && !user) {
      navigate('/login');
      return;
    }
    
    // Validate user role matches the expected role
    if (!loading && user && user.role !== role) {
      navigate(`/${user.role}`, { replace: true });
      return;
    }
  }, [user, loading, navigate, role]);

  useEffect(() => {
    if (role === 'reviewer') {
      fetchReviewerCounts();
    } else if (role === 'author') {
      fetchAuthorCounts();
    } else if (role === 'editor') {
      fetchEditorCounts();
    }
  }, [role]);

  const fetchReviewerCounts = async () => {
    try {
      const data = await reviewService.getAssignedReviews();
      const completedReviews = data.data.filter(submission => 
        submission.reviews?.some(review => review.submitted_at)
      );
      const pendingReviews = data.data.filter(submission => 
        !submission.reviews?.some(review => review.submitted_at)
      );
      setCompletedReviewsCount(completedReviews.length);
      setAssignedReviewsCount(pendingReviews.length);
    } catch (err) {
      console.error('Error fetching reviewer counts:', err);
    }
  };

  const fetchAuthorCounts = async () => {
    try {
      const response = await manuscriptService.getAuthorManuscripts();
      const manuscripts = response.data;
      const underReviewCount = manuscripts.filter(m => m.reviews && m.reviews.length > 0).length;
      setManuscriptsUnderReview(underReviewCount);
      setManuscriptsCount(manuscripts.length);
    } catch (error) {
      console.error('Error fetching author counts:', error);
    }
  };

  const fetchEditorCounts = async () => {
    try {
      const [submissionsResponse, paymentsData] = await Promise.all([
        submissionService.getAllSubmissions(1, 100), // Get first 100 submissions for counting
        submissionService.getPendingPayments()
      ]);
      
      const submissionsData = submissionsResponse.data || submissionsResponse;
      setTotalSubmissionsCount(submissionsData.length);
      setPendingReviewsCount(submissionsData.filter(s => s.status === 'under_review').length);
      setPendingPaymentsCount(paymentsData.length);
    } catch (error) {
      console.error('Error fetching editor counts:', error);
    }
  };

  // Role titles mapping
  const roleTitles = {
    admin: "Administrator",
    editor: "Editor-in-Chief",
    reviewer: "Reviewer",
    author: "Author"
  };

  const handleLogout = async () => {
    try {
      await authService.logout();
      navigate('/login');
      toast.success('Logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
      // Even if the server request fails, we should still clear local storage and redirect
      localStorage.removeItem('user');
      localStorage.removeItem('token');
      navigate('/login');
      toast.success('Logged out successfully');
    }
  };

  // Navigation items based on role
  const navItems = {
    author: [
      { name: 'Dashboard', path: '/author', icon: <FaHome />, badge: null },
      { name: 'My Submissions', path: '/author/submissions', icon: <FaFileAlt />, badge: manuscriptsCount },
      { name: 'New Submission', path: '/author/submit', icon: <FaFileUpload />, badge: null },
      { name: 'Review Status', path: '/author/reviews', icon: <FaClipboardCheck />, badge: manuscriptsUnderReview },
      { name: 'Profile', path: '/author/profile', icon: <FaUser />, badge: null }
    ],
    reviewer: [
      { name: 'Dashboard', path: '/reviewer', icon: <FaHome />, badge: null },
      { name: 'Assigned Reviews', path: '/reviewer/assigned', icon: <FaTasks />, badge: assignedReviewsCount },
      { name: 'Completed Reviews', path: '/reviewer/completed', icon: <FaCheckCircle />, badge: completedReviewsCount },
      { name: 'Review Guidelines', path: '/reviewer/guidelines', icon: <FaBook />, badge: null },
      { name: 'Profile', path: '/reviewer/profile', icon: <FaUser />, badge: null }
    ],
    editor: [
      { name: 'Dashboard', path: '/editor', icon: <FaHome />, badge: null },
      { name: 'Submissions', path: '/editor/submissions', icon: <FaFileAlt />, badge: totalSubmissionsCount },
      { name: 'Review Management', path: '/editor/reviews', icon: <FaComments />, badge: pendingReviewsCount },
      { name: 'Announcements', path: '/editor/announcements', icon: <FaBullhorn />, badge: null },
      { name: 'Volumes', path: '/editor/volumes', icon: <FaVolumeUp />, badge: null },
      { name: 'Issue Planning', path: '/editor/issues', icon: <FaCalendarAlt />, badge: null },
      { name: 'Payment Tracking', path: '/editor/payments', icon: <FaMoneyBillWave />, badge: pendingPaymentsCount },
      { name: 'Direct Publish', path: '/editor/direct-publish', icon: <FaUpload />, badge: null }
    ],
    admin: [
      { name: 'Dashboard', path: '/admin', icon: <FaHome />, badge: null },
      { name: 'Users', path: '/admin/users', icon: <FaUsers />, badge: null },
      { name: 'Manuscripts', path: '/admin/manuscripts', icon: <FaFileAlt />, badge: null },
      { name: 'Payments', path: '/admin/payments', icon: <FaMoneyBillWave />, badge: null },
      { name: 'Reviews', path: '/admin/reviews', icon: <FaComments />, badge: null },
      { name: 'Content', path: '/admin/content', icon: <FaDatabase />, badge: null },
      { name: 'System Monitoring', path: '/admin/monitoring', icon: <FaClipboardCheck />, badge: null },
      { name: 'Settings', path: '/admin/settings', icon: <FaCog />, badge: null }
    ]
  };

  const notifications = [
    { id: 1, text: "New submission requires assignment", time: "10 min ago", read: false },
    { id: 2, text: "Review deadline approaching for JEMS-2025-012", time: "2 hours ago", read: true },
    { id: 3, text: "Payment received for accepted paper", time: "1 day ago", read: true }
  ];

  const unreadCount = notifications.filter(n => !n.read).length;

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="flex h-screen bg-gradient-to-br from-orange-50 to-yellow-50">
      {/* Sidebar */}
      <motion.div
        className={`${isSidebarOpen ? 'w-64' : 'w-20'} bg-gradient-to-b from-blue-900 to-blue-800 text-white flex flex-col relative z-20 border-r-2 border-orange-400`}
        initial={{ width: 256 }}
        animate={{ width: isSidebarOpen ? 256 : 80 }}
        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
      >
        <div className="p-4 flex items-center justify-between border-b border-orange-400 h-16">
          {isSidebarOpen ? (
            <motion.h1 
              className="text-xl font-bold whitespace-nowrap"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              JEMS {roleTitles[role]}
            </motion.h1>
          ) : (
            <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
              <FaBookOpen className="text-white" />
            </div>
          )}
          <button
            onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            className="text-white hover:text-yellow-300 p-1 rounded-full hover:bg-green-600 transition-colors"
          >
            {isSidebarOpen ? (
              <FaChevronLeft className="w-4 h-4" />
            ) : (
              <FaChevronRight className="w-4 h-4" />
            )}
          </button>
        </div>
        
        <div className="flex-1 overflow-y-auto py-4">
          <nav className="px-2">
            <ul className="space-y-1">
              {navItems[role]?.map((item) => (
                <motion.li 
                  key={item.name}
                  whileHover={{ scale: 1.02 }}
                >
                  <Link
                    to={item.path}
                    className={`flex items-center justify-between px-4 py-3 text-sm font-medium rounded-md transition-colors mx-2 ${
                      location.pathname.startsWith(item.path)
                        ? 'bg-green-600 text-white shadow-md border border-orange-400'
                        : 'text-yellow-100 hover:bg-green-600 hover:text-white'
                    }`}
                  >
                    <div className="flex items-center">
                      <span className={`${isSidebarOpen ? 'mr-3' : 'mx-auto'}`}>
                        {item.icon}
                      </span>
                      {isSidebarOpen && <span>{item.name}</span>}
                    </div>
                    {item.badge && isSidebarOpen && (
                      <span className="bg-orange-400 text-blue-900 text-xs font-bold px-2 py-0.5 rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                </motion.li>
              ))}
            </ul>
          </nav>
        </div>
        
        <div className="p-4 border-t border-blue-700">
          <div 
            className="flex items-center cursor-pointer"
            onClick={() => setProfileOpen(!profileOpen)}
          >
            <img 
              src={user.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}&background=random`} 
              alt="User" 
              className="w-10 h-10 rounded-full mr-3"
            />
            {isSidebarOpen && (
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">{user.name}</p>
                <p className="text-xs text-blue-200 truncate">{roleTitles[role]}</p>
              </div>
            )}
            {isSidebarOpen && (
              <FaChevronDown className={`text-blue-200 text-xs transition-transform ${profileOpen ? 'transform rotate-180' : ''}`} />
            )}
          </div>

          {/* Profile Dropdown */}
          {profileOpen && isSidebarOpen && (
            <motion.div 
              className="mt-2 bg-blue-700 rounded-md p-2"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
            >
              <div className="text-xs text-blue-100 p-2">{user.email}</div>
              <div className="text-xs text-blue-200 p-2">
                Last login: {user.last_login_at ? formatDistanceToNow(new Date(user.last_login_at), { addSuffix: true }) : 'Just now'}
              </div>
              <Link 
                to={`/${role}/security`}
                className="w-full flex items-center justify-between px-2 py-1.5 text-sm text-blue-100 hover:bg-blue-600 rounded"
              >
                <span>Security Settings</span>
                <FaUserShield className="text-xs" />
              </Link>
              <button 
                onClick={handleLogout}
                className="w-full flex items-center justify-between px-2 py-1.5 text-sm text-blue-100 hover:bg-blue-600 rounded"
              >
                <span>Sign Out</span>
                <FaSignOutAlt className="text-xs" />
              </button>
            </motion.div>
          )}
        </div>
      </motion.div>
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation */}
        <header className="bg-white shadow-sm z-10">
          <div className="px-6 py-4 flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-800">
              {navItems[role]?.find(item => location.pathname.startsWith(item.path))?.name || 'Dashboard'}
            </h2>
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-500 hover:text-gray-700 relative">
                <FaSearch className="w-5 h-5" />
              </button>
              
              <div className="relative">
                <button 
                  className="p-2 text-gray-500 hover:text-gray-700 relative"
                  onClick={() => setNotificationsOpen(!notificationsOpen)}
                >
                  <FaBell className="w-5 h-5" />
                  {unreadCount > 0 && (
                    <span className="absolute top-0 right-0 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                      {unreadCount}
                    </span>
                  )}
                </button>
                
                {/* Notifications Dropdown */}
                {notificationsOpen && (
                  <motion.div 
                    className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg overflow-hidden z-20"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                  >
                    <div className="px-4 py-2 bg-blue-600 text-white font-medium">
                      Notifications
                    </div>
                    <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
                      {notifications.map(notification => (
                        <div 
                          key={notification.id} 
                          className={`p-4 hover:bg-gray-50 cursor-pointer ${!notification.read ? 'bg-blue-50' : ''}`}
                        >
                          <div className="flex justify-between">
                            <p className="text-sm font-medium text-gray-800">{notification.text}</p>
                            {!notification.read && (
                              <span className="h-2 w-2 bg-blue-600 rounded-full"></span>
                            )}
                          </div>
                          <p className="text-xs text-gray-500 mt-1">{notification.time}</p>
                        </div>
                      ))}
                    </div>
                    <div className="px-4 py-2 bg-gray-50 text-center text-sm">
                      <button className="text-blue-600 hover:underline">
                        View All Notifications
                      </button>
                    </div>
                  </motion.div>
                )}
              </div>
              
              <button className="p-2 text-gray-500 hover:text-gray-700">
                <FaEnvelope className="w-5 h-5" />
              </button>
            </div>
          </div>
        </header>
        
        {/* Content Area */}
        <main className="flex-1 overflow-y-auto p-6 bg-gray-50">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;