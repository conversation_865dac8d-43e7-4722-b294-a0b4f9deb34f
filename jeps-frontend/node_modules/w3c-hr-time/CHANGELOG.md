# Change log

## 1.0.2 (2020-03-05)

* Do not allow infinite loops when calculating `hrtime()`'s offset relative to UNIX time. ([#9])
* Add a LICENSE file. ([#3])
* Add Tidelift sponsorship link to README.

## 1.0.1 (2018-01-03)

* Make `performance.toJSON()` return an object with `timeOrigin` property, per [clarifications from specification authors][heycam/webidl#505].

## 1.0.0 (2018-01-02)

* Initial release.

[heycam/webidl#505]: https://github.com/heycam/webidl/pull/505
[#3]: https://github.com/jsdom/w3c-hr-time/issues/3
[#9]: https://github.com/jsdom/w3c-hr-time/issues/9
