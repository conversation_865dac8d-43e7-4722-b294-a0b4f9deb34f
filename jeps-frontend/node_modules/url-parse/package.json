{"name": "url-parse", "version": "1.5.10", "description": "Small footprint URL parser that works seamlessly across Node.js and browser environments", "main": "index.js", "scripts": {"browserify": "rm -rf dist && mkdir -p dist && browserify index.js -s URLParse -o dist/url-parse.js", "minify": "uglifyjs dist/url-parse.js --source-map -cm -o dist/url-parse.min.js", "test": "c8 --reporter=lcov --reporter=text mocha test/test.js", "test-browser": "node test/browser.js", "prepublishOnly": "npm run browserify && npm run minify", "watch": "mocha --watch test/test.js"}, "files": ["index.js", "dist"], "repository": {"type": "git", "url": "https://github.com/unshiftio/url-parse.git"}, "keywords": ["URL", "parser", "uri", "url", "parse", "query", "string", "querystring", "stringify"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"querystringify": "^2.1.1", "requires-port": "^1.0.0"}, "devDependencies": {"assume": "^2.2.0", "browserify": "^17.0.0", "c8": "^7.3.1", "mocha": "^9.0.3", "pre-commit": "^1.2.2", "sauce-browsers": "^2.0.0", "sauce-test": "^1.3.3", "uglify-js": "^3.5.7"}}