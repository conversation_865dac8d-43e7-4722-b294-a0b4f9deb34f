{"name": "turbo-stream", "version": "2.4.0", "description": "A streaming data transport format that aims to support built-in features such as Promises, Dates, RegExps, Maps, Sets and more.", "files": ["dist", "README.md"], "main": "dist/turbo-stream.js", "types": "dist/turbo-stream.d.ts", "exports": {".": {"types": "./dist/turbo-stream.d.ts", "import": "./dist/turbo-stream.mjs", "require": "./dist/turbo-stream.js", "default": "./dist/turbo-stream.js"}, "./package.json": "./package.json"}, "scripts": {"build": "pnpm build:esm && pnpm build:cjs && cp ./dist/turbo-stream.mjs ./viewer/scripts/turbo-stream.js", "build:cjs": "tsc --outDir dist --project tsconfig.lib.json", "build:esm": "esbuild --bundle --platform=node --target=node16 --format=esm --outfile=dist/turbo-stream.mjs src/turbo-stream.ts", "test": "node --no-warnings --loader tsm --enable-source-maps --test-reporter tap --test src/*.spec.ts", "test-typecheck": "tsc --noEmit --project tsconfig.spec.json", "prepublish": "attw $(npm pack) --ignore-rules false-cjs"}, "keywords": ["devalue", "turbo", "stream", "enhanced", "json"], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "repository": {"type": "git", "url": "https://github.com/jacob-ebey/turbo-stream.git"}, "sideEffects": false, "devDependencies": {"@arethetypeswrong/cli": "^0.12.2", "@types/node": "^20.8.7", "esbuild": "^0.19.5", "expect": "^29.7.0", "tsm": "^2.3.0", "typescript": "^5.2.2"}}