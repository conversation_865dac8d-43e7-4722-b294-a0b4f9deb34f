{"version": 3, "file": "rgba-string-color-picker.js", "sourceRoot": "", "sources": ["src/rgba-string-color-picker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,kCAAkC,CAAC;AAElE;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,OAAO,qBAAsB,SAAQ,cAAc;CAAG;AAE5D,cAAc,CAAC,MAAM,CAAC,0BAA0B,EAAE,qBAAqB,CAAC,CAAC", "sourcesContent": ["import { RgbaStringBase } from './lib/entrypoints/rgba-string.js';\n\n/**\n * A color picker custom element that uses RGBA string format.\n *\n * @element rgba-string-color-picker\n *\n * @prop {string} color - Selected color in RGBA string format.\n * @attr {string} color - Selected color in RGBA string format.\n *\n * @fires color-changed - Event fired when color property changes.\n *\n * @csspart hue - A hue selector container.\n * @csspart saturation - A saturation selector container\n * @csspart alpha - An alpha selector container.\n * @csspart hue-pointer - A hue pointer element.\n * @csspart saturation-pointer - A saturation pointer element.\n * @csspart alpha-pointer - An alpha pointer element.\n */\nexport class RgbaStringColorPicker extends RgbaStringBase {}\n\ncustomElements.define('rgba-string-color-picker', RgbaStringColorPicker);\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'rgba-string-color-picker': RgbaStringColorPicker;\n  }\n}\n"]}