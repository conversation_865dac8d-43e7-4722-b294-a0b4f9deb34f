{"version": 3, "file": "hex-alpha-color-picker.js", "sourceRoot": "", "sources": ["src/hex-alpha-color-picker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAE9D;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,OAAO,mBAAoB,SAAQ,YAAY;CAAG;AAExD,cAAc,CAAC,MAAM,CAAC,wBAAwB,EAAE,mBAAmB,CAAC,CAAC", "sourcesContent": ["import { HexAlphaBase } from './lib/entrypoints/hex-alpha.js';\n\n/**\n * A color picker custom element that uses HEX format with alpha.\n *\n * @element hex-alpha-color-picker\n *\n * @prop {string} color - Selected color in HEX format.\n * @attr {string} color - Selected color in HEX format.\n *\n * @fires color-changed - Event fired when color property changes.\n *\n * @csspart hue - A hue selector container.\n * @csspart saturation - A saturation selector container\n * @csspart alpha - An alpha selector container.\n * @csspart hue-pointer - A hue pointer element.\n * @csspart saturation-pointer - A saturation pointer element.\n * @csspart alpha-pointer - An alpha pointer element.\n */\nexport class HexAlphaColorPicker extends HexAlphaBase {}\n\ncustomElements.define('hex-alpha-color-picker', HexAlphaColorPicker);\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'hex-alpha-color-picker': HexAlphaColorPicker;\n  }\n}\n"]}