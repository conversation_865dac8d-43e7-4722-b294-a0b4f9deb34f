{"version": 3, "file": "hsl-string-color-picker.js", "sourceRoot": "", "sources": ["src/hsl-string-color-picker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,iCAAiC,CAAC;AAEhE;;;;;;;;;;;;;;GAcG;AACH,MAAM,OAAO,oBAAqB,SAAQ,aAAa;CAAG;AAE1D,cAAc,CAAC,MAAM,CAAC,yBAAyB,EAAE,oBAAoB,CAAC,CAAC", "sourcesContent": ["import { HslStringBase } from './lib/entrypoints/hsl-string.js';\n\n/**\n * A color picker custom element that uses HSL string format.\n *\n * @element hsl-string-color-picker\n *\n * @prop {string} color - Selected color in HSL string format.\n * @attr {string} color - Selected color in HSL string format.\n *\n * @fires color-changed - Event fired when color property changes.\n *\n * @csspart hue - A hue selector container.\n * @csspart saturation - A saturation selector container\n * @csspart hue-pointer - A hue pointer element.\n * @csspart saturation-pointer - A saturation pointer element.\n */\nexport class HslStringColorPicker extends HslStringBase {}\n\ncustomElements.define('hsl-string-color-picker', HslStringColorPicker);\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'hsl-string-color-picker': HslStringColorPicker;\n  }\n}\n"]}