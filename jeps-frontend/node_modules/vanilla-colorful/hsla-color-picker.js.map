{"version": 3, "file": "hsla-color-picker.js", "sourceRoot": "", "sources": ["src/hsla-color-picker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAC;AAGrD;;;;;;;;;;;;;;;GAeG;AACH,MAAM,OAAO,eAAgB,SAAQ,QAAQ;CAAG;AAEhD,cAAc,CAAC,MAAM,CAAC,mBAAmB,EAAE,eAAe,CAAC,CAAC", "sourcesContent": ["import { HslaBase } from './lib/entrypoints/hsla.js';\nexport type { HslaColor } from './lib/types';\n\n/**\n * A color picker custom element that uses HSLA object format.\n *\n * @element hsla-color-picker\n *\n * @prop {HslaColor} color - Selected color in HSLA object format.\n *\n * @fires color-changed - Event fired when color property changes.\n *\n * @csspart hue - A hue selector container.\n * @csspart saturation - A saturation selector container\n * @csspart alpha - An alpha selector container.\n * @csspart hue-pointer - A hue pointer element.\n * @csspart saturation-pointer - A saturation pointer element.\n * @csspart alpha-pointer - An alpha pointer element.\n */\nexport class HslaColorPicker extends HslaBase {}\n\ncustomElements.define('hsla-color-picker', HslaColorPicker);\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'hsla-color-picker': HslaColorPicker;\n  }\n}\n"]}