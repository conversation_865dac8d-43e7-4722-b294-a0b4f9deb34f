{"schemaVersion": "1.0.0", "readme": "", "modules": [{"kind": "javascript-module", "path": "hex-alpha-color-picker.js", "declarations": [{"kind": "class", "description": "A color picker custom element that uses HEX format with alpha.", "name": "HexAlphaColorPicker", "cssParts": [{"description": "A hue selector container.", "name": "hue"}, {"description": "A saturation selector container", "name": "saturation"}, {"description": "An alpha selector container.", "name": "alpha"}, {"description": "A hue pointer element.", "name": "hue-pointer"}, {"description": "A saturation pointer element.", "name": "saturation-pointer"}, {"description": "An alpha pointer element.", "name": "alpha-pointer"}], "members": [{"kind": "field", "name": "color", "type": {"text": "string"}, "description": "Selected color in HEX format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "colorModel", "inheritedFrom": {"name": "HexAlphaBase", "module": "lib/entrypoints/hex-alpha.js"}}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "events": [{"description": "Event fired when color property changes.", "name": "color-changed"}], "attributes": [{"name": "color", "type": {"text": "string"}, "description": "Selected color in HEX format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "HexAlphaBase", "module": "/lib/entrypoints/hex-alpha.js"}, "tagName": "hex-alpha-color-picker", "customElement": true}], "exports": [{"kind": "js", "name": "HexAlphaColorPicker", "declaration": {"name": "HexAlphaColorPicker", "module": "hex-alpha-color-picker.js"}}, {"kind": "custom-element-definition", "name": "hex-alpha-color-picker", "declaration": {"name": "HexAlphaColorPicker", "module": "hex-alpha-color-picker.js"}}]}, {"kind": "javascript-module", "path": "hex-color-picker.js", "declarations": [{"kind": "class", "description": "A color picker custom element that uses HEX format.", "name": "HexColorPicker", "cssParts": [{"description": "A hue selector container.", "name": "hue"}, {"description": "A saturation selector container", "name": "saturation"}, {"description": "A hue pointer element.", "name": "hue-pointer"}, {"description": "A saturation pointer element.", "name": "saturation-pointer"}], "members": [{"kind": "field", "name": "color", "type": {"text": "string"}, "description": "Selected color in HEX format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "colorModel", "inheritedFrom": {"name": "HexBase", "module": "lib/entrypoints/hex.js"}}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "events": [{"description": "Event fired when color property changes.", "name": "color-changed"}], "attributes": [{"name": "color", "type": {"text": "string"}, "description": "Selected color in HEX format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "HexBase", "module": "/lib/entrypoints/hex.js"}, "tagName": "hex-color-picker", "customElement": true}], "exports": [{"kind": "js", "name": "HexColorPicker", "declaration": {"name": "HexColorPicker", "module": "hex-color-picker.js"}}, {"kind": "custom-element-definition", "name": "hex-color-picker", "declaration": {"name": "HexColorPicker", "module": "hex-color-picker.js"}}]}, {"kind": "javascript-module", "path": "hex-input.js", "declarations": [{"kind": "class", "description": "A custom element for entering color in HEX format.", "name": "HexInput", "cssParts": [{"description": "A native input element.", "name": "input"}], "members": [{"kind": "field", "name": "color", "type": {"text": "string"}, "description": "Color in HEX format.", "inheritedFrom": {"name": "HexInputBase", "module": "lib/entrypoints/hex-input.js"}}, {"kind": "field", "name": "alpha", "type": {"text": "boolean"}, "description": "When true, `#rgba` and `#rrggbbaa` color formats are allowed.", "inheritedFrom": {"name": "HexInputBase", "module": "lib/entrypoints/hex-input.js"}}, {"kind": "field", "name": "prefixed", "type": {"text": "boolean"}, "description": "When true, `#` prefix is displayed in the input.", "inheritedFrom": {"name": "HexInputBase", "module": "lib/entrypoints/hex-input.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "HexInputBase", "module": "lib/entrypoints/hex-input.js"}}, {"kind": "method", "name": "[$init]", "parameters": [{"name": "root"}], "inheritedFrom": {"name": "HexInputBase", "module": "lib/entrypoints/hex-input.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hex"}], "inheritedFrom": {"name": "HexInputBase", "module": "lib/entrypoints/hex-input.js"}}], "events": [{"name": "color-changed", "type": {"text": "CustomEvent"}, "description": "Event fired when color is changed.", "inheritedFrom": {"name": "HexInputBase", "module": "lib/entrypoints/hex-input.js"}}], "attributes": [{"name": "color", "type": {"text": "string"}, "description": "Selected color in HEX format.", "inheritedFrom": {"name": "HexInputBase", "module": "lib/entrypoints/hex-input.js"}}, {"name": "alpha", "type": {"text": "boolean"}, "description": "Allows `#rgba` and `#rrggbbaa` color formats.", "inheritedFrom": {"name": "HexInputBase", "module": "lib/entrypoints/hex-input.js"}}, {"name": "prefixed", "type": {"text": "boolean"}, "description": "Enables `#` prefix displaying.", "inheritedFrom": {"name": "HexInputBase", "module": "lib/entrypoints/hex-input.js"}}], "superclass": {"name": "HexInputBase", "module": "/lib/entrypoints/hex-input.js"}, "tagName": "hex-input", "customElement": true}], "exports": [{"kind": "js", "name": "HexInput", "declaration": {"name": "HexInput", "module": "hex-input.js"}}, {"kind": "custom-element-definition", "name": "hex-input", "declaration": {"name": "HexInput", "module": "hex-input.js"}}]}, {"kind": "javascript-module", "path": "hsl-color-picker.js", "declarations": [{"kind": "class", "description": "A color picker custom element that uses HSL object format.", "name": "HslColorPicker", "cssParts": [{"description": "A hue selector container.", "name": "hue"}, {"description": "A saturation selector container", "name": "saturation"}, {"description": "A hue pointer element.", "name": "hue-pointer"}, {"description": "A saturation pointer element.", "name": "saturation-pointer"}], "members": [{"kind": "field", "name": "color", "type": {"text": "HslColor"}, "description": "Selected color in HSL object format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "colorModel", "inheritedFrom": {"name": "HslBase", "module": "lib/entrypoints/hsl.js"}}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "events": [{"description": "Event fired when color property changes.", "name": "color-changed"}], "superclass": {"name": "HslBase", "module": "/lib/entrypoints/hsl.js"}, "tagName": "hsl-color-picker", "customElement": true, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "HslColorPicker", "declaration": {"name": "HslColorPicker", "module": "hsl-color-picker.js"}}, {"kind": "custom-element-definition", "name": "hsl-color-picker", "declaration": {"name": "HslColorPicker", "module": "hsl-color-picker.js"}}]}, {"kind": "javascript-module", "path": "hsl-string-color-picker.js", "declarations": [{"kind": "class", "description": "A color picker custom element that uses HSL string format.", "name": "HslStringColorPicker", "cssParts": [{"description": "A hue selector container.", "name": "hue"}, {"description": "A saturation selector container", "name": "saturation"}, {"description": "A hue pointer element.", "name": "hue-pointer"}, {"description": "A saturation pointer element.", "name": "saturation-pointer"}], "members": [{"kind": "field", "name": "color", "type": {"text": "string"}, "description": "Selected color in HSL string format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "colorModel", "inheritedFrom": {"name": "HslStringBase", "module": "lib/entrypoints/hsl-string.js"}}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "events": [{"description": "Event fired when color property changes.", "name": "color-changed"}], "attributes": [{"name": "color", "type": {"text": "string"}, "description": "Selected color in HSL string format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "HslStringBase", "module": "/lib/entrypoints/hsl-string.js"}, "tagName": "hsl-string-color-picker", "customElement": true}], "exports": [{"kind": "js", "name": "HslStringColorPicker", "declaration": {"name": "HslStringColorPicker", "module": "hsl-string-color-picker.js"}}, {"kind": "custom-element-definition", "name": "hsl-string-color-picker", "declaration": {"name": "HslStringColorPicker", "module": "hsl-string-color-picker.js"}}]}, {"kind": "javascript-module", "path": "hsla-color-picker.js", "declarations": [{"kind": "class", "description": "A color picker custom element that uses HSLA object format.", "name": "HslaColorPicker", "cssParts": [{"description": "A hue selector container.", "name": "hue"}, {"description": "A saturation selector container", "name": "saturation"}, {"description": "An alpha selector container.", "name": "alpha"}, {"description": "A hue pointer element.", "name": "hue-pointer"}, {"description": "A saturation pointer element.", "name": "saturation-pointer"}, {"description": "An alpha pointer element.", "name": "alpha-pointer"}], "members": [{"kind": "field", "name": "color", "type": {"text": "HslaColor"}, "description": "Selected color in HSLA object format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "colorModel", "inheritedFrom": {"name": "HslaBase", "module": "lib/entrypoints/hsla.js"}}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "events": [{"description": "Event fired when color property changes.", "name": "color-changed"}], "superclass": {"name": "HslaBase", "module": "/lib/entrypoints/hsla.js"}, "tagName": "hsla-color-picker", "customElement": true, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "HslaColorPicker", "declaration": {"name": "HslaColorPicker", "module": "hsla-color-picker.js"}}, {"kind": "custom-element-definition", "name": "hsla-color-picker", "declaration": {"name": "HslaColorPicker", "module": "hsla-color-picker.js"}}]}, {"kind": "javascript-module", "path": "hsla-string-color-picker.js", "declarations": [{"kind": "class", "description": "A color picker custom element that uses HSLA string format.", "name": "HslaStringColorPicker", "cssParts": [{"description": "A hue selector container.", "name": "hue"}, {"description": "A saturation selector container", "name": "saturation"}, {"description": "An alpha selector container.", "name": "alpha"}, {"description": "A hue pointer element.", "name": "hue-pointer"}, {"description": "A saturation pointer element.", "name": "saturation-pointer"}, {"description": "An alpha pointer element.", "name": "alpha-pointer"}], "members": [{"kind": "field", "name": "color", "type": {"text": "string"}, "description": "Selected color in HSLA string format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "colorModel", "inheritedFrom": {"name": "HslaStringBase", "module": "lib/entrypoints/hsla-string.js"}}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "events": [{"description": "Event fired when color property changes.", "name": "color-changed"}], "attributes": [{"name": "color", "type": {"text": "string"}, "description": "Selected color in HSLA string format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "HslaStringBase", "module": "/lib/entrypoints/hsla-string.js"}, "tagName": "hsla-string-color-picker", "customElement": true}], "exports": [{"kind": "js", "name": "HslaStringColorPicker", "declaration": {"name": "HslaStringColorPicker", "module": "hsla-string-color-picker.js"}}, {"kind": "custom-element-definition", "name": "hsla-string-color-picker", "declaration": {"name": "HslaStringColorPicker", "module": "hsla-string-color-picker.js"}}]}, {"kind": "javascript-module", "path": "hsv-color-picker.js", "declarations": [{"kind": "class", "description": "A color picker custom element that uses HSV object format.", "name": "HsvColorPicker", "cssParts": [{"description": "A hue selector container.", "name": "hue"}, {"description": "A saturation selector container", "name": "saturation"}, {"description": "A hue pointer element.", "name": "hue-pointer"}, {"description": "A saturation pointer element.", "name": "saturation-pointer"}], "members": [{"kind": "field", "name": "color", "type": {"text": "HsvColor"}, "description": "Selected color in HSV object format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "colorModel", "inheritedFrom": {"name": "HsvBase", "module": "lib/entrypoints/hsv.js"}}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "events": [{"description": "Event fired when color property changes.", "name": "color-changed"}], "superclass": {"name": "HsvBase", "module": "/lib/entrypoints/hsv.js"}, "tagName": "hsv-color-picker", "customElement": true, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "HsvColorPicker", "declaration": {"name": "HsvColorPicker", "module": "hsv-color-picker.js"}}, {"kind": "custom-element-definition", "name": "hsv-color-picker", "declaration": {"name": "HsvColorPicker", "module": "hsv-color-picker.js"}}]}, {"kind": "javascript-module", "path": "hsv-string-color-picker.js", "declarations": [{"kind": "class", "description": "A color picker custom element that uses HSV string format.", "name": "HsvStringColorPicker", "cssParts": [{"description": "A hue selector container.", "name": "hue"}, {"description": "A saturation selector container", "name": "saturation"}, {"description": "A hue pointer element.", "name": "hue-pointer"}, {"description": "A saturation pointer element.", "name": "saturation-pointer"}], "members": [{"kind": "field", "name": "color", "type": {"text": "string"}, "description": "Selected color in HSV string format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "colorModel", "inheritedFrom": {"name": "HsvStringBase", "module": "lib/entrypoints/hsv-string.js"}}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "events": [{"description": "Event fired when color property changes.", "name": "color-changed"}], "attributes": [{"name": "color", "type": {"text": "string"}, "description": "Selected color in HSV string format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "HsvStringBase", "module": "/lib/entrypoints/hsv-string.js"}, "tagName": "hsv-string-color-picker", "customElement": true}], "exports": [{"kind": "js", "name": "HsvStringColorPicker", "declaration": {"name": "HsvStringColorPicker", "module": "hsv-string-color-picker.js"}}, {"kind": "custom-element-definition", "name": "hsv-string-color-picker", "declaration": {"name": "HsvStringColorPicker", "module": "hsv-string-color-picker.js"}}]}, {"kind": "javascript-module", "path": "hsva-color-picker.js", "declarations": [{"kind": "class", "description": "A color picker custom element that uses HSVA object format.", "name": "HsvaColorPicker", "cssParts": [{"description": "A hue selector container.", "name": "hue"}, {"description": "A saturation selector container", "name": "saturation"}, {"description": "An alpha selector container.", "name": "alpha"}, {"description": "A hue pointer element.", "name": "hue-pointer"}, {"description": "A saturation pointer element.", "name": "saturation-pointer"}, {"description": "An alpha pointer element.", "name": "alpha-pointer"}], "members": [{"kind": "field", "name": "color", "type": {"text": "HsvaColor"}, "description": "Selected color in HSVA object format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "colorModel", "inheritedFrom": {"name": "HsvaBase", "module": "lib/entrypoints/hsva.js"}}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "events": [{"description": "Event fired when color property changes.", "name": "color-changed"}], "superclass": {"name": "HsvaBase", "module": "/lib/entrypoints/hsva.js"}, "tagName": "hsva-color-picker", "customElement": true, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "HsvaColorPicker", "declaration": {"name": "HsvaColorPicker", "module": "hsva-color-picker.js"}}, {"kind": "custom-element-definition", "name": "hsva-color-picker", "declaration": {"name": "HsvaColorPicker", "module": "hsva-color-picker.js"}}]}, {"kind": "javascript-module", "path": "hsva-string-color-picker.js", "declarations": [{"kind": "class", "description": "A color picker custom element that uses HSVA string format.", "name": "HsvaStringColorPicker", "cssParts": [{"description": "A hue selector container.", "name": "hue"}, {"description": "A saturation selector container", "name": "saturation"}, {"description": "An alpha selector container.", "name": "alpha"}, {"description": "A hue pointer element.", "name": "hue-pointer"}, {"description": "A saturation pointer element.", "name": "saturation-pointer"}, {"description": "An alpha pointer element.", "name": "alpha-pointer"}], "members": [{"kind": "field", "name": "color", "type": {"text": "string"}, "description": "Selected color in HSVA string format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "colorModel", "inheritedFrom": {"name": "HsvaStringBase", "module": "lib/entrypoints/hsva-string.js"}}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "events": [{"description": "Event fired when color property changes.", "name": "color-changed"}], "attributes": [{"name": "color", "type": {"text": "string"}, "description": "Selected color in HSVA string format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "HsvaStringBase", "module": "/lib/entrypoints/hsva-string.js"}, "tagName": "hsva-string-color-picker", "customElement": true}], "exports": [{"kind": "js", "name": "HsvaStringColorPicker", "declaration": {"name": "HsvaStringColorPicker", "module": "hsva-string-color-picker.js"}}, {"kind": "custom-element-definition", "name": "hsva-string-color-picker", "declaration": {"name": "HsvaStringColorPicker", "module": "hsva-string-color-picker.js"}}]}, {"kind": "javascript-module", "path": "rgb-color-picker.js", "declarations": [{"kind": "class", "description": "A color picker custom element that uses RGB object format.", "name": "RgbColorPicker", "cssParts": [{"description": "A hue selector container.", "name": "hue"}, {"description": "A saturation selector container", "name": "saturation"}, {"description": "A hue pointer element.", "name": "hue-pointer"}, {"description": "A saturation pointer element.", "name": "saturation-pointer"}], "members": [{"kind": "field", "name": "color", "type": {"text": "RgbColor"}, "description": "Selected color in RGB object format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "colorModel", "inheritedFrom": {"name": "RgbBase", "module": "lib/entrypoints/rgb.js"}}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "events": [{"description": "Event fired when color property changes.", "name": "color-changed"}], "superclass": {"name": "RgbBase", "module": "/lib/entrypoints/rgb.js"}, "tagName": "rgb-color-picker", "customElement": true, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "RgbColorPicker", "declaration": {"name": "RgbColorPicker", "module": "rgb-color-picker.js"}}, {"kind": "custom-element-definition", "name": "rgb-color-picker", "declaration": {"name": "RgbColorPicker", "module": "rgb-color-picker.js"}}]}, {"kind": "javascript-module", "path": "rgb-string-color-picker.js", "declarations": [{"kind": "class", "description": "A color picker custom element that uses RGB string format.", "name": "RgbStringColorPicker", "cssParts": [{"description": "A hue selector container.", "name": "hue"}, {"description": "A saturation selector container", "name": "saturation"}, {"description": "A hue pointer element.", "name": "hue-pointer"}, {"description": "A saturation pointer element.", "name": "saturation-pointer"}], "members": [{"kind": "field", "name": "color", "type": {"text": "string"}, "description": "Selected color in RGB string format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "colorModel", "inheritedFrom": {"name": "RgbStringBase", "module": "lib/entrypoints/rgb-string.js"}}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "events": [{"description": "Event fired when color property changes.", "name": "color-changed"}], "attributes": [{"name": "color", "type": {"text": "string"}, "description": "Selected color in RGB string format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "RgbStringBase", "module": "/lib/entrypoints/rgb-string.js"}, "tagName": "rgb-string-color-picker", "customElement": true}], "exports": [{"kind": "js", "name": "RgbStringColorPicker", "declaration": {"name": "RgbStringColorPicker", "module": "rgb-string-color-picker.js"}}, {"kind": "custom-element-definition", "name": "rgb-string-color-picker", "declaration": {"name": "RgbStringColorPicker", "module": "rgb-string-color-picker.js"}}]}, {"kind": "javascript-module", "path": "rgba-color-picker.js", "declarations": [{"kind": "class", "description": "A color picker custom element that uses RGBA object format.", "name": "RgbaColorPicker", "cssParts": [{"description": "A hue selector container.", "name": "hue"}, {"description": "A saturation selector container", "name": "saturation"}, {"description": "An alpha selector container.", "name": "alpha"}, {"description": "A hue pointer element.", "name": "hue-pointer"}, {"description": "A saturation pointer element.", "name": "saturation-pointer"}, {"description": "An alpha pointer element.", "name": "alpha-pointer"}], "members": [{"kind": "field", "name": "color", "type": {"text": "RgbaColor"}, "description": "Selected color in RGBA object format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "colorModel", "inheritedFrom": {"name": "RgbaBase", "module": "lib/entrypoints/rgba.js"}}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "events": [{"description": "Event fired when color property changes.", "name": "color-changed"}], "superclass": {"name": "RgbaBase", "module": "/lib/entrypoints/rgba.js"}, "tagName": "rgba-color-picker", "customElement": true, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "RgbaColorPicker", "declaration": {"name": "RgbaColorPicker", "module": "rgba-color-picker.js"}}, {"kind": "custom-element-definition", "name": "rgba-color-picker", "declaration": {"name": "RgbaColorPicker", "module": "rgba-color-picker.js"}}]}, {"kind": "javascript-module", "path": "rgba-string-color-picker.js", "declarations": [{"kind": "class", "description": "A color picker custom element that uses RGBA string format.", "name": "RgbaStringColorPicker", "cssParts": [{"description": "A hue selector container.", "name": "hue"}, {"description": "A saturation selector container", "name": "saturation"}, {"description": "An alpha selector container.", "name": "alpha"}, {"description": "A hue pointer element.", "name": "hue-pointer"}, {"description": "A saturation pointer element.", "name": "saturation-pointer"}, {"description": "An alpha pointer element.", "name": "alpha-pointer"}], "members": [{"kind": "field", "name": "color", "type": {"text": "string"}, "description": "Selected color in RGBA string format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "colorModel", "inheritedFrom": {"name": "RgbaStringBase", "module": "lib/entrypoints/rgba-string.js"}}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "events": [{"description": "Event fired when color property changes.", "name": "color-changed"}], "attributes": [{"name": "color", "type": {"text": "string"}, "description": "Selected color in RGBA string format.", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "RgbaStringBase", "module": "/lib/entrypoints/rgba-string.js"}, "tagName": "rgba-string-color-picker", "customElement": true}], "exports": [{"kind": "js", "name": "RgbaStringColorPicker", "declaration": {"name": "RgbaStringColorPicker", "module": "rgba-string-color-picker.js"}}, {"kind": "custom-element-definition", "name": "rgba-string-color-picker", "declaration": {"name": "RgbaStringColorPicker", "module": "rgba-string-color-picker.js"}}]}, {"kind": "javascript-module", "path": "lib/components/alpha-color-picker.js", "declarations": [{"kind": "class", "description": "", "name": "AlphaColorPicker", "members": [{"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "ColorPicker", "module": "/lib/components/color-picker.js"}, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "AlphaColorPicker", "declaration": {"name": "AlphaColorPicker", "module": "lib/components/alpha-color-picker.js"}}]}, {"kind": "javascript-module", "path": "lib/components/alpha.js", "declarations": [{"kind": "class", "description": "", "name": "Alpha", "members": [{"kind": "method", "name": "update", "parameters": [{"name": "hsva"}]}, {"kind": "method", "name": "getMove", "parameters": [{"name": "offset"}, {"name": "key"}]}, {"kind": "field", "name": "dragging", "inheritedFrom": {"name": "Slide<PERSON>", "module": "lib/components/slider.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "Slide<PERSON>", "module": "lib/components/slider.js"}}, {"kind": "method", "name": "style", "parameters": [{"name": "styles"}], "inheritedFrom": {"name": "Slide<PERSON>", "module": "lib/components/slider.js"}}, {"kind": "field", "name": "el", "default": "el", "inheritedFrom": {"name": "Slide<PERSON>", "module": "lib/components/slider.js"}}, {"kind": "field", "name": "xy", "default": "xy", "inheritedFrom": {"name": "Slide<PERSON>", "module": "lib/components/slider.js"}}, {"kind": "field", "name": "nodes", "type": {"text": "array"}, "default": "[el.<PERSON><PERSON><PERSON><PERSON>, el]", "inheritedFrom": {"name": "Slide<PERSON>", "module": "lib/components/slider.js"}}], "superclass": {"name": "Slide<PERSON>", "module": "/lib/components/slider.js"}}], "exports": [{"kind": "js", "name": "Alpha", "declaration": {"name": "Alpha", "module": "lib/components/alpha.js"}}]}, {"kind": "javascript-module", "path": "lib/components/color-picker.js", "declarations": [{"kind": "variable", "name": "$css"}, {"kind": "variable", "name": "$sliders"}, {"kind": "class", "description": "", "name": "ColorPicker", "members": [{"kind": "field", "name": "[$css]"}, {"kind": "field", "name": "[$sliders]"}, {"kind": "field", "name": "color"}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}]}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}]}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}]}, {"kind": "field"}], "attributes": [{"name": "color"}], "superclass": {"name": "HTMLElement"}, "customElement": true}], "exports": [{"kind": "js", "name": "$css", "declaration": {"name": "$css", "module": "lib/components/color-picker.js"}}, {"kind": "js", "name": "$sliders", "declaration": {"name": "$sliders", "module": "lib/components/color-picker.js"}}, {"kind": "js", "name": "ColorPicker", "declaration": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}, {"kind": "javascript-module", "path": "lib/components/hue.js", "declarations": [{"kind": "class", "description": "", "name": "<PERSON><PERSON>", "members": [{"kind": "method", "name": "update", "parameters": [{"name": "{ h }"}]}, {"kind": "method", "name": "getMove", "parameters": [{"name": "offset"}, {"name": "key"}]}, {"kind": "field", "name": "dragging", "inheritedFrom": {"name": "Slide<PERSON>", "module": "lib/components/slider.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "Slide<PERSON>", "module": "lib/components/slider.js"}}, {"kind": "method", "name": "style", "parameters": [{"name": "styles"}], "inheritedFrom": {"name": "Slide<PERSON>", "module": "lib/components/slider.js"}}, {"kind": "field", "name": "el", "default": "el", "inheritedFrom": {"name": "Slide<PERSON>", "module": "lib/components/slider.js"}}, {"kind": "field", "name": "xy", "default": "xy", "inheritedFrom": {"name": "Slide<PERSON>", "module": "lib/components/slider.js"}}, {"kind": "field", "name": "nodes", "type": {"text": "array"}, "default": "[el.<PERSON><PERSON><PERSON><PERSON>, el]", "inheritedFrom": {"name": "Slide<PERSON>", "module": "lib/components/slider.js"}}], "superclass": {"name": "Slide<PERSON>", "module": "/lib/components/slider.js"}}], "exports": [{"kind": "js", "name": "<PERSON><PERSON>", "declaration": {"name": "<PERSON><PERSON>", "module": "lib/components/hue.js"}}]}, {"kind": "javascript-module", "path": "lib/components/saturation.js", "declarations": [{"kind": "class", "description": "", "name": "Saturation", "members": [{"kind": "method", "name": "update", "parameters": [{"name": "hsva"}]}, {"kind": "method", "name": "getMove", "parameters": [{"name": "offset"}, {"name": "key"}]}, {"kind": "field", "name": "dragging", "inheritedFrom": {"name": "Slide<PERSON>", "module": "lib/components/slider.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "Slide<PERSON>", "module": "lib/components/slider.js"}}, {"kind": "method", "name": "style", "parameters": [{"name": "styles"}], "inheritedFrom": {"name": "Slide<PERSON>", "module": "lib/components/slider.js"}}, {"kind": "field", "name": "el", "default": "el", "inheritedFrom": {"name": "Slide<PERSON>", "module": "lib/components/slider.js"}}, {"kind": "field", "name": "xy", "default": "xy", "inheritedFrom": {"name": "Slide<PERSON>", "module": "lib/components/slider.js"}}, {"kind": "field", "name": "nodes", "type": {"text": "array"}, "default": "[el.<PERSON><PERSON><PERSON><PERSON>, el]", "inheritedFrom": {"name": "Slide<PERSON>", "module": "lib/components/slider.js"}}], "superclass": {"name": "Slide<PERSON>", "module": "/lib/components/slider.js"}}], "exports": [{"kind": "js", "name": "Saturation", "declaration": {"name": "Saturation", "module": "lib/components/saturation.js"}}]}, {"kind": "javascript-module", "path": "lib/components/slider.js", "declarations": [{"kind": "class", "description": "", "name": "Slide<PERSON>", "members": [{"kind": "field", "name": "dragging"}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}]}, {"kind": "method", "name": "style", "parameters": [{"name": "styles"}]}, {"kind": "field", "name": "el", "default": "el"}, {"kind": "field", "name": "xy", "default": "xy"}, {"kind": "field", "name": "nodes", "type": {"text": "array"}, "default": "[el.<PERSON><PERSON><PERSON><PERSON>, el]"}]}], "exports": [{"kind": "js", "name": "Slide<PERSON>", "declaration": {"name": "Slide<PERSON>", "module": "lib/components/slider.js"}}]}, {"kind": "javascript-module", "path": "lib/entrypoints/hex-alpha.js", "declarations": [{"kind": "class", "description": "", "name": "HexAlphaBase", "members": [{"kind": "field", "name": "colorModel"}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "AlphaColorPicker", "module": "/lib/components/alpha-color-picker.js"}, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "HexAlphaBase", "declaration": {"name": "HexAlphaBase", "module": "lib/entrypoints/hex-alpha.js"}}]}, {"kind": "javascript-module", "path": "lib/entrypoints/hex-input.js", "declarations": [{"kind": "class", "description": "", "name": "HexInputBase", "members": [{"kind": "field", "name": "color"}, {"kind": "field", "name": "alpha"}, {"kind": "field", "name": "prefixed"}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}]}, {"kind": "method", "name": "[$init]", "parameters": [{"name": "root"}]}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hex"}]}], "events": [{"name": "color-changed", "type": {"text": "CustomEvent"}}], "attributes": [{"name": "alpha"}, {"name": "color"}, {"name": "prefixed"}], "superclass": {"name": "HTMLElement"}, "customElement": true}], "exports": [{"kind": "js", "name": "HexInputBase", "declaration": {"name": "HexInputBase", "module": "lib/entrypoints/hex-input.js"}}]}, {"kind": "javascript-module", "path": "lib/entrypoints/hex.js", "declarations": [{"kind": "class", "description": "", "name": "HexBase", "members": [{"kind": "field", "name": "colorModel"}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "ColorPicker", "module": "/lib/components/color-picker.js"}, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "HexBase", "declaration": {"name": "HexBase", "module": "lib/entrypoints/hex.js"}}]}, {"kind": "javascript-module", "path": "lib/entrypoints/hsl-string.js", "declarations": [{"kind": "class", "description": "", "name": "HslStringBase", "members": [{"kind": "field", "name": "colorModel"}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "ColorPicker", "module": "/lib/components/color-picker.js"}, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "HslStringBase", "declaration": {"name": "HslStringBase", "module": "lib/entrypoints/hsl-string.js"}}]}, {"kind": "javascript-module", "path": "lib/entrypoints/hsl.js", "declarations": [{"kind": "class", "description": "", "name": "HslBase", "members": [{"kind": "field", "name": "colorModel"}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "ColorPicker", "module": "/lib/components/color-picker.js"}, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "HslBase", "declaration": {"name": "HslBase", "module": "lib/entrypoints/hsl.js"}}]}, {"kind": "javascript-module", "path": "lib/entrypoints/hsla-string.js", "declarations": [{"kind": "class", "description": "", "name": "HslaStringBase", "members": [{"kind": "field", "name": "colorModel"}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "AlphaColorPicker", "module": "/lib/components/alpha-color-picker.js"}, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "HslaStringBase", "declaration": {"name": "HslaStringBase", "module": "lib/entrypoints/hsla-string.js"}}]}, {"kind": "javascript-module", "path": "lib/entrypoints/hsla.js", "declarations": [{"kind": "class", "description": "", "name": "HslaBase", "members": [{"kind": "field", "name": "colorModel"}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "AlphaColorPicker", "module": "/lib/components/alpha-color-picker.js"}, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "HslaBase", "declaration": {"name": "HslaBase", "module": "lib/entrypoints/hsla.js"}}]}, {"kind": "javascript-module", "path": "lib/entrypoints/hsv-string.js", "declarations": [{"kind": "class", "description": "", "name": "HsvStringBase", "members": [{"kind": "field", "name": "colorModel"}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "ColorPicker", "module": "/lib/components/color-picker.js"}, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "HsvStringBase", "declaration": {"name": "HsvStringBase", "module": "lib/entrypoints/hsv-string.js"}}]}, {"kind": "javascript-module", "path": "lib/entrypoints/hsv.js", "declarations": [{"kind": "class", "description": "", "name": "HsvBase", "members": [{"kind": "field", "name": "colorModel"}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "ColorPicker", "module": "/lib/components/color-picker.js"}, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "HsvBase", "declaration": {"name": "HsvBase", "module": "lib/entrypoints/hsv.js"}}]}, {"kind": "javascript-module", "path": "lib/entrypoints/hsva-string.js", "declarations": [{"kind": "class", "description": "", "name": "HsvaStringBase", "members": [{"kind": "field", "name": "colorModel"}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "AlphaColorPicker", "module": "/lib/components/alpha-color-picker.js"}, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "HsvaStringBase", "declaration": {"name": "HsvaStringBase", "module": "lib/entrypoints/hsva-string.js"}}]}, {"kind": "javascript-module", "path": "lib/entrypoints/hsva.js", "declarations": [{"kind": "class", "description": "", "name": "HsvaBase", "members": [{"kind": "field", "name": "colorModel"}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "AlphaColorPicker", "module": "/lib/components/alpha-color-picker.js"}, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "HsvaBase", "declaration": {"name": "HsvaBase", "module": "lib/entrypoints/hsva.js"}}]}, {"kind": "javascript-module", "path": "lib/entrypoints/rgb-string.js", "declarations": [{"kind": "class", "description": "", "name": "RgbStringBase", "members": [{"kind": "field", "name": "colorModel"}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "ColorPicker", "module": "/lib/components/color-picker.js"}, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "RgbStringBase", "declaration": {"name": "RgbStringBase", "module": "lib/entrypoints/rgb-string.js"}}]}, {"kind": "javascript-module", "path": "lib/entrypoints/rgb.js", "declarations": [{"kind": "class", "description": "", "name": "RgbBase", "members": [{"kind": "field", "name": "colorModel"}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "ColorPicker", "module": "/lib/components/color-picker.js"}, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "RgbBase", "declaration": {"name": "RgbBase", "module": "lib/entrypoints/rgb.js"}}]}, {"kind": "javascript-module", "path": "lib/entrypoints/rgba-string.js", "declarations": [{"kind": "class", "description": "", "name": "RgbaStringBase", "members": [{"kind": "field", "name": "colorModel"}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "AlphaColorPicker", "module": "/lib/components/alpha-color-picker.js"}, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "RgbaStringBase", "declaration": {"name": "RgbaStringBase", "module": "lib/entrypoints/rgba-string.js"}}]}, {"kind": "javascript-module", "path": "lib/entrypoints/rgba.js", "declarations": [{"kind": "class", "description": "", "name": "RgbaBase", "members": [{"kind": "field", "name": "colorModel"}, {"kind": "field", "name": "[$css]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "[$sliders]", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "handleEvent", "parameters": [{"name": "event"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$isSame]", "parameters": [{"name": "color"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "method", "name": "[$update]", "parameters": [{"name": "hsva"}], "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}, {"kind": "field", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}], "superclass": {"name": "AlphaColorPicker", "module": "/lib/components/alpha-color-picker.js"}, "attributes": [{"name": "color", "inheritedFrom": {"name": "ColorPicker", "module": "lib/components/color-picker.js"}}]}], "exports": [{"kind": "js", "name": "RgbaBase", "declaration": {"name": "RgbaBase", "module": "lib/entrypoints/rgba.js"}}]}]}