{"version": 3, "file": "hex-color-picker.js", "sourceRoot": "", "sources": ["src/hex-color-picker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,0BAA0B,CAAC;AAEnD;;;;;;;;;;;;;;GAcG;AACH,MAAM,OAAO,cAAe,SAAQ,OAAO;CAAG;AAE9C,cAAc,CAAC,MAAM,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC", "sourcesContent": ["import { HexBase } from './lib/entrypoints/hex.js';\n\n/**\n * A color picker custom element that uses HEX format.\n *\n * @element hex-color-picker\n *\n * @prop {string} color - Selected color in HEX format.\n * @attr {string} color - Selected color in HEX format.\n *\n * @fires color-changed - Event fired when color property changes.\n *\n * @csspart hue - A hue selector container.\n * @csspart saturation - A saturation selector container\n * @csspart hue-pointer - A hue pointer element.\n * @csspart saturation-pointer - A saturation pointer element.\n */\nexport class HexColorPicker extends HexBase {}\n\ncustomElements.define('hex-color-picker', HexColorPicker);\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'hex-color-picker': HexColorPicker;\n  }\n}\n"]}