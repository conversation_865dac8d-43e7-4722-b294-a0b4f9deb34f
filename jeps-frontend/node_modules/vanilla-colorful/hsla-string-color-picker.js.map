{"version": 3, "file": "hsla-string-color-picker.js", "sourceRoot": "", "sources": ["src/hsla-string-color-picker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,kCAAkC,CAAC;AAElE;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,OAAO,qBAAsB,SAAQ,cAAc;CAAG;AAE5D,cAAc,CAAC,MAAM,CAAC,0BAA0B,EAAE,qBAAqB,CAAC,CAAC", "sourcesContent": ["import { HslaStringBase } from './lib/entrypoints/hsla-string.js';\n\n/**\n * A color picker custom element that uses HSLA string format.\n *\n * @element hsla-string-color-picker\n *\n * @prop {string} color - Selected color in HSLA string format.\n * @attr {string} color - Selected color in HSLA string format.\n *\n * @fires color-changed - Event fired when color property changes.\n *\n * @csspart hue - A hue selector container.\n * @csspart saturation - A saturation selector container\n * @csspart alpha - An alpha selector container.\n * @csspart hue-pointer - A hue pointer element.\n * @csspart saturation-pointer - A saturation pointer element.\n * @csspart alpha-pointer - An alpha pointer element.\n */\nexport class HslaStringColorPicker extends HslaStringBase {}\n\ncustomElements.define('hsla-string-color-picker', HslaStringColorPicker);\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'hsla-string-color-picker': HslaStringColorPicker;\n  }\n}\n"]}