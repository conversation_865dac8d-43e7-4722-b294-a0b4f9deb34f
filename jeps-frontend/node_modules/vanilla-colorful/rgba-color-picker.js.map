{"version": 3, "file": "rgba-color-picker.js", "sourceRoot": "", "sources": ["src/rgba-color-picker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAC;AAGrD;;;;;;;;;;;;;;;GAeG;AACH,MAAM,OAAO,eAAgB,SAAQ,QAAQ;CAAG;AAEhD,cAAc,CAAC,MAAM,CAAC,mBAAmB,EAAE,eAAe,CAAC,CAAC", "sourcesContent": ["import { RgbaBase } from './lib/entrypoints/rgba.js';\nexport type { RgbaColor } from './lib/types';\n\n/**\n * A color picker custom element that uses RGBA object format.\n *\n * @element rgba-color-picker\n *\n * @prop {RgbaColor} color - Selected color in RGBA object format.\n *\n * @fires color-changed - Event fired when color property changes.\n *\n * @csspart hue - A hue selector container.\n * @csspart saturation - A saturation selector container\n * @csspart alpha - An alpha selector container.\n * @csspart hue-pointer - A hue pointer element.\n * @csspart saturation-pointer - A saturation pointer element.\n * @csspart alpha-pointer - An alpha pointer element.\n */\nexport class RgbaColorPicker extends RgbaBase {}\n\ncustomElements.define('rgba-color-picker', RgbaColorPicker);\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'rgba-color-picker': RgbaColorPicker;\n  }\n}\n"]}