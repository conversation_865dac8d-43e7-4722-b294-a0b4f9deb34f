{"version": 3, "file": "hsva-color-picker.js", "sourceRoot": "", "sources": ["src/hsva-color-picker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAC;AAGrD;;;;;;;;;;;;;;;GAeG;AACH,MAAM,OAAO,eAAgB,SAAQ,QAAQ;CAAG;AAEhD,cAAc,CAAC,MAAM,CAAC,mBAAmB,EAAE,eAAe,CAAC,CAAC", "sourcesContent": ["import { HsvaBase } from './lib/entrypoints/hsva.js';\nexport type { HsvaColor } from './lib/types';\n\n/**\n * A color picker custom element that uses HSVA object format.\n *\n * @element hsva-color-picker\n *\n * @prop {HsvaColor} color - Selected color in HSVA object format.\n *\n * @fires color-changed - Event fired when color property changes.\n *\n * @csspart hue - A hue selector container.\n * @csspart saturation - A saturation selector container\n * @csspart alpha - An alpha selector container.\n * @csspart hue-pointer - A hue pointer element.\n * @csspart saturation-pointer - A saturation pointer element.\n * @csspart alpha-pointer - An alpha pointer element.\n */\nexport class HsvaColorPicker extends HsvaBase {}\n\ncustomElements.define('hsva-color-picker', HsvaColorPicker);\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'hsva-color-picker': HsvaColorPicker;\n  }\n}\n"]}