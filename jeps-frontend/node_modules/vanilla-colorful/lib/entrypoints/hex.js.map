{"version": 3, "file": "hex.js", "sourceRoot": "", "sources": ["../../src/lib/entrypoints/hex.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAC3D,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AAE/C,MAAM,UAAU,GAAuB;IACrC,YAAY,EAAE,MAAM;IACpB,MAAM,EAAE,SAAS;IACjB,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACvD,KAAK,EAAE,QAAQ;IACf,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK;CAC3B,CAAC;AAgBF,MAAM,OAAO,OAAQ,SAAQ,WAAmB;IAC9C,IAAc,UAAU;QACtB,OAAO,UAAU,CAAC;IACpB,CAAC;CACF", "sourcesContent": ["import type { ColorModel, ColorPickerEventListener, ColorPickerEventMap } from '../types';\nimport { ColorPicker } from '../components/color-picker.js';\nimport { hexToHsva, hsvaToHex } from '../utils/convert.js';\nimport { equalHex } from '../utils/compare.js';\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: '#000',\n  toHsva: hexToHsva,\n  fromHsva: ({ h, s, v }) => hsvaToHex({ h, s, v, a: 1 }),\n  equal: equalHex,\n  fromAttr: (color) => color\n};\n\nexport interface HexBase {\n  addEventListener<T extends keyof ColorPickerEventMap<string>>(\n    type: T,\n    listener: ColorPickerEventListener<ColorPickerEventMap<string>[T]>,\n    options?: boolean | AddEventListenerOptions\n  ): void;\n\n  removeEventListener<T extends keyof ColorPickerEventMap<string>>(\n    type: T,\n    listener: ColorPickerEventListener<ColorPickerEventMap<string>[T]>,\n    options?: boolean | EventListenerOptions\n  ): void;\n}\n\nexport class HexBase extends ColorPicker<string> {\n  protected get colorModel(): ColorModel<string> {\n    return colorModel;\n  }\n}\n"]}