{"version": 3, "file": "hsva-string.js", "sourceRoot": "", "sources": ["../../src/lib/entrypoints/hsva-string.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,gBAAgB,EAAE,MAAM,qCAAqC,CAAC;AACvE,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AACzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAEvD,MAAM,UAAU,GAAuB;IACrC,YAAY,EAAE,oBAAoB;IAClC,MAAM,EAAE,gBAAgB;IACxB,QAAQ,EAAE,gBAAgB;IAC1B,KAAK,EAAE,gBAAgB;IACvB,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK;CAC3B,CAAC;AAgBF,MAAM,OAAO,cAAe,SAAQ,gBAAwB;IAC1D,IAAc,UAAU;QACtB,OAAO,UAAU,CAAC;IACpB,CAAC;CACF", "sourcesContent": ["import type { ColorModel, ColorPickerEventListener, ColorPickerEventMap } from '../types';\nimport { AlphaColorPicker } from '../components/alpha-color-picker.js';\nimport { hsvaStringToHsva, hsvaToHsvaString } from '../utils/convert.js';\nimport { equalColorString } from '../utils/compare.js';\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: 'hsva(0, 0%, 0%, 1)',\n  toHsva: hsvaStringToHsva,\n  fromHsva: hsvaToHsvaString,\n  equal: equalColorString,\n  fromAttr: (color) => color\n};\n\nexport interface HsvaStringBase {\n  addEventListener<T extends keyof ColorPickerEventMap<string>>(\n    type: T,\n    listener: ColorPickerEventListener<ColorPickerEventMap<string>[T]>,\n    options?: boolean | AddEventListenerOptions\n  ): void;\n\n  removeEventListener<T extends keyof ColorPickerEventMap<string>>(\n    type: T,\n    listener: ColorPickerEventListener<ColorPickerEventMap<string>[T]>,\n    options?: boolean | EventListenerOptions\n  ): void;\n}\n\nexport class HsvaStringBase extends AlphaColorPicker<string> {\n  protected get colorModel(): ColorModel<string> {\n    return colorModel;\n  }\n}\n"]}