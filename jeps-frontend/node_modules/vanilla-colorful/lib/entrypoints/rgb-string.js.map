{"version": 3, "file": "rgb-string.js", "sourceRoot": "", "sources": ["../../src/lib/entrypoints/rgb-string.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EAAE,eAAe,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AACvE,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAEvD,MAAM,UAAU,GAAuB;IACrC,YAAY,EAAE,cAAc;IAC5B,MAAM,EAAE,eAAe;IACvB,QAAQ,EAAE,eAAe;IACzB,KAAK,EAAE,gBAAgB;IACvB,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK;CAC3B,CAAC;AAgBF,MAAM,OAAO,aAAc,SAAQ,WAAmB;IACpD,IAAc,UAAU;QACtB,OAAO,UAAU,CAAC;IACpB,CAAC;CACF", "sourcesContent": ["import type { ColorModel, ColorPickerEventListener, ColorPickerEventMap } from '../types';\nimport { ColorPicker } from '../components/color-picker.js';\nimport { rgbStringToHsva, hsvaToRgbString } from '../utils/convert.js';\nimport { equalColorString } from '../utils/compare.js';\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: 'rgb(0, 0, 0)',\n  toHsva: rgbStringToHsva,\n  fromHsva: hsvaToRgbString,\n  equal: equalColorString,\n  fromAttr: (color) => color\n};\n\nexport interface RgbStringBase {\n  addEventListener<T extends keyof ColorPickerEventMap<string>>(\n    type: T,\n    listener: ColorPickerEventListener<ColorPickerEventMap<string>[T]>,\n    options?: boolean | AddEventListenerOptions\n  ): void;\n\n  removeEventListener<T extends keyof ColorPickerEventMap<string>>(\n    type: T,\n    listener: ColorPickerEventListener<ColorPickerEventMap<string>[T]>,\n    options?: boolean | EventListenerOptions\n  ): void;\n}\n\nexport class RgbStringBase extends ColorPicker<string> {\n  protected get colorModel(): ColorModel<string> {\n    return colorModel;\n  }\n}\n"]}