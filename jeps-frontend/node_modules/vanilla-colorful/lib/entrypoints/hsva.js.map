{"version": 3, "file": "hsva.js", "sourceRoot": "", "sources": ["../../src/lib/entrypoints/hsva.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,gBAAgB,EAAE,MAAM,qCAAqC,CAAC;AACvE,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAEhD,MAAM,UAAU,GAA0B;IACxC,YAAY,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;IACxC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI;IACtB,QAAQ,EAAE,SAAS;IACnB,KAAK,EAAE,iBAAiB;IACxB,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;CACvC,CAAC;AAgBF,MAAM,OAAO,QAAS,SAAQ,gBAA2B;IACvD,IAAc,UAAU;QACtB,OAAO,UAAU,CAAC;IACpB,CAAC;CACF", "sourcesContent": ["import type {\n  ColorModel,\n  ColorPickerEventListener,\n  ColorPickerEventMap,\n  HsvaColor\n} from '../types';\nimport { AlphaColorPicker } from '../components/alpha-color-picker.js';\nimport { equalColorObjects } from '../utils/compare.js';\nimport { roundHsva } from '../utils/convert.js';\n\nconst colorModel: ColorModel<HsvaColor> = {\n  defaultColor: { h: 0, s: 0, v: 0, a: 1 },\n  toHsva: (hsva) => hsva,\n  fromHsva: roundHsva,\n  equal: equalColorObjects,\n  fromAttr: (color) => JSON.parse(color)\n};\n\nexport interface HsvaBase {\n  addEventListener<T extends keyof ColorPickerEventMap<HsvaColor>>(\n    type: T,\n    listener: ColorPickerEventListener<ColorPickerEventMap<HsvaColor>[T]>,\n    options?: boolean | AddEventListenerOptions\n  ): void;\n\n  removeEventListener<T extends keyof ColorPickerEventMap<HsvaColor>>(\n    type: T,\n    listener: ColorPickerEventListener<ColorPickerEventMap<HsvaColor>[T]>,\n    options?: boolean | EventListenerOptions\n  ): void;\n}\n\nexport class HsvaBase extends AlphaColorPicker<HsvaColor> {\n  protected get colorModel(): ColorModel<HsvaColor> {\n    return colorModel;\n  }\n}\n"]}