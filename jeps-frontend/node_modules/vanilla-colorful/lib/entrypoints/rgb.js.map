{"version": 3, "file": "rgb.js", "sourceRoot": "", "sources": ["../../src/lib/entrypoints/rgb.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AACxE,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAExD,MAAM,UAAU,GAAyB;IACvC,YAAY,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;IAClC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACtD,QAAQ,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC/C,KAAK,EAAE,iBAAiB;IACxB,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;CACvC,CAAC;AAgBF,MAAM,OAAO,OAAQ,SAAQ,WAAqB;IAChD,IAAc,UAAU;QACtB,OAAO,UAAU,CAAC;IACpB,CAAC;CACF", "sourcesContent": ["import type { ColorModel, ColorPickerEventListener, ColorPickerEventMap, RgbColor } from '../types';\nimport { ColorPicker } from '../components/color-picker.js';\nimport { rgbaToHsva, hsvaToRgba, rgbaToRgb } from '../utils/convert.js';\nimport { equalColorObjects } from '../utils/compare.js';\n\nconst colorModel: ColorModel<RgbColor> = {\n  defaultColor: { r: 0, g: 0, b: 0 },\n  toHsva: ({ r, g, b }) => rgbaToHsva({ r, g, b, a: 1 }),\n  fromHsva: (hsva) => rgbaToRgb(hsvaToRgba(hsva)),\n  equal: equalColorObjects,\n  fromAttr: (color) => JSON.parse(color)\n};\n\nexport interface RgbBase {\n  addEventListener<T extends keyof ColorPickerEventMap<RgbColor>>(\n    type: T,\n    listener: ColorPickerEventListener<ColorPickerEventMap<RgbColor>[T]>,\n    options?: boolean | AddEventListenerOptions\n  ): void;\n\n  removeEventListener<T extends keyof ColorPickerEventMap<RgbColor>>(\n    type: T,\n    listener: ColorPickerEventListener<ColorPickerEventMap<RgbColor>[T]>,\n    options?: boolean | EventListenerOptions\n  ): void;\n}\n\nexport class RgbBase extends ColorPicker<RgbColor> {\n  protected get colorModel(): ColorModel<RgbColor> {\n    return colorModel;\n  }\n}\n"]}