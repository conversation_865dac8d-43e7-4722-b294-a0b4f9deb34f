{"version": 3, "file": "hex-alpha.js", "sourceRoot": "", "sources": ["../../src/lib/entrypoints/hex-alpha.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,qCAAqC,CAAC;AAEvE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAC3D,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AAE/C,MAAM,UAAU,GAAuB;IACrC,YAAY,EAAE,OAAO;IACrB,MAAM,EAAE,SAAS;IACjB,QAAQ,EAAE,SAAS;IACnB,KAAK,EAAE,QAAQ;IACf,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK;CAC3B,CAAC;AAgBF,MAAM,OAAO,YAAa,SAAQ,gBAAwB;IACxD,IAAc,UAAU;QACtB,OAAO,UAAU,CAAC;IACpB,CAAC;CACF", "sourcesContent": ["import { AlphaColorPicker } from '../components/alpha-color-picker.js';\nimport type { ColorModel, ColorPickerEventListener, ColorPickerEventMap } from '../types';\nimport { hexToHsva, hsvaToHex } from '../utils/convert.js';\nimport { equalHex } from '../utils/compare.js';\n\nconst colorModel: ColorModel<string> = {\n  defaultColor: '#0001',\n  toHsva: hexToHsva,\n  fromHsva: hsvaToHex,\n  equal: equalHex,\n  fromAttr: (color) => color\n};\n\nexport interface HexAlphaBase {\n  addEventListener<T extends keyof ColorPickerEventMap<string>>(\n    type: T,\n    listener: ColorPickerEventListener<ColorPickerEventMap<string>[T]>,\n    options?: boolean | AddEventListenerOptions\n  ): void;\n\n  removeEventListener<T extends keyof ColorPickerEventMap<string>>(\n    type: T,\n    listener: ColorPickerEventListener<ColorPickerEventMap<string>[T]>,\n    options?: boolean | EventListenerOptions\n  ): void;\n}\n\nexport class HexAlphaBase extends AlphaColorPicker<string> {\n  protected get colorModel(): ColorModel<string> {\n    return colorModel;\n  }\n}\n"]}