{"version": 3, "file": "hsv.js", "sourceRoot": "", "sources": ["../../src/lib/entrypoints/hsv.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAChD,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAExD,MAAM,UAAU,GAAyB;IACvC,YAAY,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;IAClC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC5C,QAAQ,EAAE,SAAS;IACnB,KAAK,EAAE,iBAAiB;IACxB,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;CACvC,CAAC;AAgBF,MAAM,OAAO,OAAQ,SAAQ,WAAqB;IAChD,IAAc,UAAU;QACtB,OAAO,UAAU,CAAC;IACpB,CAAC;CACF", "sourcesContent": ["import type { ColorModel, ColorPickerEventListener, ColorPickerEventMap, HsvColor } from '../types';\nimport { ColorPicker } from '../components/color-picker.js';\nimport { hsvaToHsv } from '../utils/convert.js';\nimport { equalColorObjects } from '../utils/compare.js';\n\nconst colorModel: ColorModel<HsvColor> = {\n  defaultColor: { h: 0, s: 0, v: 0 },\n  toHsva: ({ h, s, v }) => ({ h, s, v, a: 1 }),\n  fromHsva: hsvaToHsv,\n  equal: equalColorObjects,\n  fromAttr: (color) => JSON.parse(color)\n};\n\nexport interface HsvBase {\n  addEventListener<T extends keyof ColorPickerEventMap<HsvColor>>(\n    type: T,\n    listener: ColorPickerEventListener<ColorPickerEventMap<HsvColor>[T]>,\n    options?: boolean | AddEventListenerOptions\n  ): void;\n\n  removeEventListener<T extends keyof ColorPickerEventMap<HsvColor>>(\n    type: T,\n    listener: ColorPickerEventListener<ColorPickerEventMap<HsvColor>[T]>,\n    options?: boolean | EventListenerOptions\n  ): void;\n}\n\nexport class HsvBase extends ColorPicker<HsvColor> {\n  protected get colorModel(): ColorModel<HsvColor> {\n    return colorModel;\n  }\n}\n"]}