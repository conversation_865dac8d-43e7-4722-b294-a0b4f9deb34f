{"version": 3, "file": "hex-input.js", "sourceRoot": "", "sources": ["../../src/lib/entrypoints/hex-input.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAChD,OAAO,EAAE,GAAG,EAAE,MAAM,iBAAiB,CAAC;AAEtC,MAAM,QAAQ,GAAG,GAAG,CAAC,sDAAsD,CAAC,CAAC;AAE7E,uDAAuD;AACvD,MAAM,MAAM,GAAG,CAAC,GAAW,EAAE,KAAc,EAAE,EAAE,CAC7C,GAAG,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAEhE,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;AAC/B,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;AAC/B,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;AAC/B,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;AAC/B,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AAC7B,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AACjC,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAgBjC,MAAM,OAAO,YAAa,SAAQ,WAAW;IAC3C,MAAM,KAAK,kBAAkB;QAC3B,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;IACxC,CAAC;IAYD,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;IAED,IAAI,KAAK,CAAC,GAAW;QACnB,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;QACnB,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IACrB,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;IAED,IAAI,KAAK,CAAC,KAAc;QACtB,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAErC,wCAAwC;QACxC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;YACpC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC;gBAChC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChD,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACpD;IACH,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;IACvB,CAAC;IAED,IAAI,QAAQ,CAAC,QAAiB;QAC5B,IAAI,CAAC,OAAO,CAAC,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC3C,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAED;QACE,KAAK,EAAE,CAAC;QAER,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;QACjD,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QACnD,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAoC,CAAC;QACvD,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/D,CAAC;IAED,iBAAiB;QACf,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAwB,CAAC,CAAC;QAE3C,4DAA4D;QAC5D,yDAAyD;QACzD,4DAA4D;QAC5D,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;YAChC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACzB,OAAO,IAAI,CAAC,OAAqB,CAAC,CAAC;YACnC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACpB;aAAM;YACL,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;SACzC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;YACnC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC5B,OAAO,IAAI,CAAC,UAAwB,CAAC,CAAC;YACtC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;SACvB;aAAM;YACL,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;SAC/C;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;YAChC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACzB,OAAO,IAAI,CAAC,OAAqB,CAAC,CAAC;YACnC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACpB;aAAM,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;YAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;SAC/C;aAAM,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;YACvB,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;SAC7B;IACH,CAAC;IAED,WAAW,CAAC,KAAY;QACtB,MAAM,MAAM,GAAG,KAAK,CAAC,MAA0B,CAAC;QAChD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;QACzB,QAAQ,KAAK,CAAC,IAAI,EAAE;YAClB,KAAK,OAAO;gBACV,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;gBAC1B,IAAI,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE,EAAE;oBAC7C,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;oBACjB,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,eAAe,EAAE;wBAC/B,OAAO,EAAE,IAAI;wBACb,MAAM,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;qBACxC,CAAC,CACH,CAAC;iBACH;gBACD,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;oBACzC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;iBAC3B;SACJ;IACH,CAAC;IAED,wBAAwB,CAAC,IAAY,EAAE,OAAe,EAAE,MAAc;QACpE,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE;YAC7C,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC;SACrB;QAED,MAAM,cAAc,GAAG,MAAM,IAAI,IAAI,CAAC;QACtC,IAAI,IAAI,KAAK,OAAO,EAAE;YACpB,IAAI,IAAI,CAAC,KAAK,KAAK,cAAc,EAAE;gBACjC,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC;aAC7B;SACF;QAED,IAAI,IAAI,KAAK,UAAU,EAAE;YACvB,IAAI,IAAI,CAAC,QAAQ,KAAK,cAAc,EAAE;gBACpC,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;aAChC;SACF;IACH,CAAC;IAEO,CAAC,KAAK,CAAC,CAAC,IAAgB;QAC9B,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,KAAK,EAAE;YACV,0CAA0C;YAC1C,IAAI,CAAC,CAAC;YACN,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE;gBAC5B,CAAC,CAAC,MAAM,EAAE,CAAC;aACZ;YAED,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAqB,CAAC;SACzD;QACD,KAAK,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACtC,KAAK,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAEO,CAAC,OAAO,CAAC,CAAC,GAAW;QAC3B,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK;gBAChB,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;SACxF;IACH,CAAC;CACF", "sourcesContent": ["import type { ColorPickerEventListener, ColorPickerEventMap } from '../types';\nimport { validHex } from '../utils/validate.js';\nimport { tpl } from '../utils/dom.js';\n\nconst template = tpl('<slot><input part=\"input\" spellcheck=\"false\"></slot>');\n\n// Escapes all non-hexadecimal characters including \"#\"\nconst escape = (hex: string, alpha: boolean) =>\n  hex.replace(/([^0-9A-F]+)/gi, '').substring(0, alpha ? 8 : 6);\n\nconst $alpha = Symbol('alpha');\nconst $color = Symbol('color');\nconst $saved = Symbol('saved');\nconst $input = Symbol('input');\nconst $init = Symbol('init');\nconst $prefix = Symbol('prefix');\nconst $update = Symbol('update');\n\nexport interface HexInputBase {\n  addEventListener<T extends keyof ColorPickerEventMap<string>>(\n    type: T,\n    listener: ColorPickerEventListener<ColorPickerEventMap<string>[T]>,\n    options?: boolean | AddEventListenerOptions\n  ): void;\n\n  removeEventListener<T extends keyof ColorPickerEventMap<string>>(\n    type: T,\n    listener: ColorPickerEventListener<ColorPickerEventMap<string>[T]>,\n    options?: boolean | EventListenerOptions\n  ): void;\n}\n\nexport class HexInputBase extends HTMLElement {\n  static get observedAttributes(): string[] {\n    return ['alpha', 'color', 'prefixed'];\n  }\n\n  private declare [$color]: string;\n\n  private declare [$alpha]: boolean;\n\n  private declare [$prefix]: boolean;\n\n  private declare [$saved]: string;\n\n  private declare [$input]: HTMLInputElement;\n\n  get color(): string {\n    return this[$color];\n  }\n\n  set color(hex: string) {\n    this[$color] = hex;\n    this[$update](hex);\n  }\n\n  get alpha(): boolean {\n    return this[$alpha];\n  }\n\n  set alpha(alpha: boolean) {\n    this[$alpha] = alpha;\n    this.toggleAttribute('alpha', alpha);\n\n    // When alpha set to false, update color\n    const color = this.color;\n    if (color && !validHex(color, alpha)) {\n      this.color = color.startsWith('#')\n        ? color.substring(0, color.length === 5 ? 4 : 7)\n        : color.substring(0, color.length === 4 ? 3 : 6);\n    }\n  }\n\n  get prefixed(): boolean {\n    return this[$prefix];\n  }\n\n  set prefixed(prefixed: boolean) {\n    this[$prefix] = prefixed;\n    this.toggleAttribute('prefixed', prefixed);\n    this[$update](this.color);\n  }\n\n  constructor() {\n    super();\n\n    const root = this.attachShadow({ mode: 'open' });\n    root.appendChild(template.content.cloneNode(true));\n    const slot = root.firstElementChild as HTMLSlotElement;\n    slot.addEventListener('slotchange', () => this[$init](root));\n  }\n\n  connectedCallback(): void {\n    this[$init](this.shadowRoot as ShadowRoot);\n\n    // A user may set a property on an _instance_ of an element,\n    // before its prototype has been connected to this class.\n    // If so, we need to run it through the proper class setter.\n    if (this.hasOwnProperty('alpha')) {\n      const value = this.alpha;\n      delete this['alpha' as keyof this];\n      this.alpha = value;\n    } else {\n      this.alpha = this.hasAttribute('alpha');\n    }\n\n    if (this.hasOwnProperty('prefixed')) {\n      const value = this.prefixed;\n      delete this['prefixed' as keyof this];\n      this.prefixed = value;\n    } else {\n      this.prefixed = this.hasAttribute('prefixed');\n    }\n\n    if (this.hasOwnProperty('color')) {\n      const value = this.color;\n      delete this['color' as keyof this];\n      this.color = value;\n    } else if (this.color == null) {\n      this.color = this.getAttribute('color') || '';\n    } else if (this[$color]) {\n      this[$update](this[$color]);\n    }\n  }\n\n  handleEvent(event: Event): void {\n    const target = event.target as HTMLInputElement;\n    const { value } = target;\n    switch (event.type) {\n      case 'input':\n        const hex = escape(value, this.alpha);\n        this[$saved] = this.color;\n        if (validHex(hex, this.alpha) || value === '') {\n          this.color = hex;\n          this.dispatchEvent(\n            new CustomEvent('color-changed', {\n              bubbles: true,\n              detail: { value: hex ? '#' + hex : '' }\n            })\n          );\n        }\n        break;\n      case 'blur':\n        if (value && !validHex(value, this.alpha)) {\n          this.color = this[$saved];\n        }\n    }\n  }\n\n  attributeChangedCallback(attr: string, _oldVal: string, newVal: string): void {\n    if (attr === 'color' && this.color !== newVal) {\n      this.color = newVal;\n    }\n\n    const hasBooleanAttr = newVal != null;\n    if (attr === 'alpha') {\n      if (this.alpha !== hasBooleanAttr) {\n        this.alpha = hasBooleanAttr;\n      }\n    }\n\n    if (attr === 'prefixed') {\n      if (this.prefixed !== hasBooleanAttr) {\n        this.prefixed = hasBooleanAttr;\n      }\n    }\n  }\n\n  private [$init](root: ShadowRoot): void {\n    let input = this.querySelector('input');\n    if (!input) {\n      // remove all child node if no input found\n      let c;\n      while ((c = this.firstChild)) {\n        c.remove();\n      }\n\n      input = root.querySelector('input') as HTMLInputElement;\n    }\n    input.addEventListener('input', this);\n    input.addEventListener('blur', this);\n    this[$input] = input;\n    this[$update](this.color);\n  }\n\n  private [$update](hex: string): void {\n    if (this[$input]) {\n      this[$input].value =\n        hex == null || hex == '' ? '' : (this.prefixed ? '#' : '') + escape(hex, this.alpha);\n    }\n  }\n}\n"]}