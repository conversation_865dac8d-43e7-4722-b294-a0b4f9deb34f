{"version": 3, "file": "rgba.js", "sourceRoot": "", "sources": ["../../src/lib/entrypoints/rgba.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,gBAAgB,EAAE,MAAM,qCAAqC,CAAC;AACvE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAC7D,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAExD,MAAM,UAAU,GAA0B;IACxC,YAAY,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;IACxC,MAAM,EAAE,UAAU;IAClB,QAAQ,EAAE,UAAU;IACpB,KAAK,EAAE,iBAAiB;IACxB,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;CACvC,CAAC;AAgBF,MAAM,OAAO,QAAS,SAAQ,gBAA2B;IACvD,IAAc,UAAU;QACtB,OAAO,UAAU,CAAC;IACpB,CAAC;CACF", "sourcesContent": ["import type {\n  ColorModel,\n  ColorPickerEventListener,\n  ColorPickerEventMap,\n  RgbaColor\n} from '../types';\nimport { AlphaColorPicker } from '../components/alpha-color-picker.js';\nimport { rgbaToHsva, hsvaToRgba } from '../utils/convert.js';\nimport { equalColorObjects } from '../utils/compare.js';\n\nconst colorModel: ColorModel<RgbaColor> = {\n  defaultColor: { r: 0, g: 0, b: 0, a: 1 },\n  toHsva: rgbaToHsva,\n  fromHsva: hsvaToRgba,\n  equal: equalColorObjects,\n  fromAttr: (color) => JSON.parse(color)\n};\n\nexport interface RgbaBase {\n  addEventListener<T extends keyof ColorPickerEventMap<RgbaColor>>(\n    type: T,\n    listener: ColorPickerEventListener<ColorPickerEventMap<RgbaColor>[T]>,\n    options?: boolean | AddEventListenerOptions\n  ): void;\n\n  removeEventListener<T extends keyof ColorPickerEventMap<RgbaColor>>(\n    type: T,\n    listener: ColorPickerEventListener<ColorPickerEventMap<RgbaColor>[T]>,\n    options?: boolean | EventListenerOptions\n  ): void;\n}\n\nexport class RgbaBase extends AlphaColorPicker<RgbaColor> {\n  protected get colorModel(): ColorModel<RgbaColor> {\n    return colorModel;\n  }\n}\n"]}