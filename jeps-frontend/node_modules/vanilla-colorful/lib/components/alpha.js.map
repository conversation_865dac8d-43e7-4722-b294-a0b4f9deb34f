{"version": 3, "file": "alpha.js", "sourceRoot": "", "sources": ["../../src/lib/components/alpha.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAU,MAAM,aAAa,CAAC;AAC7C,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AACvD,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAGhD,MAAM,OAAO,KAAM,SAAQ,MAAM;IAG/B,YAAY,IAAgB;QAC1B,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,wDAAwD,EAAE,KAAK,CAAC,CAAC;IACxF,CAAC;IAED,MAAM,CAAC,IAAe;QACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,MAAM,SAAS,GAAG,gBAAgB,CAAC,EAAE,GAAG,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACtD,MAAM,OAAO,GAAG,gBAAgB,CAAC,EAAE,GAAG,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACpD,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;QAE3B,IAAI,CAAC,KAAK,CAAC;YACT;gBACE,IAAI,EAAE,GAAG,KAAK,GAAG;gBACjB,KAAK,EAAE,gBAAgB,CAAC,IAAI,CAAC;aAC9B;YACD;gBACE,YAAY,EAAE,0BAA0B,SAAS,KAAK,OAAO,EAAE;aAChE;SACF,CAAC,CAAC;QAEH,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QACvB,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;QAC9C,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,gBAAgB,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;IAClD,CAAC;IAED,OAAO,CAAC,MAAc,EAAE,GAAa;QACnC,qCAAqC;QACrC,OAAO,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;IAC/D,CAAC;CACF", "sourcesContent": ["import { Slider, Offset } from './slider.js';\nimport { hsvaToHslaString } from '../utils/convert.js';\nimport { clamp, round } from '../utils/math.js';\nimport type { HsvaColor } from '../types';\n\nexport class Alpha extends Slider {\n  declare hsva: HsvaColor;\n\n  constructor(root: ShadowRoot) {\n    super(root, 'alpha', 'aria-label=\"Alpha\" aria-valuemin=\"0\" aria-valuemax=\"1\"', false);\n  }\n\n  update(hsva: HsvaColor): void {\n    this.hsva = hsva;\n    const colorFrom = hsvaToHslaString({ ...hsva, a: 0 });\n    const colorTo = hsvaToHslaString({ ...hsva, a: 1 });\n    const value = hsva.a * 100;\n\n    this.style([\n      {\n        left: `${value}%`,\n        color: hsvaToHslaString(hsva)\n      },\n      {\n        '--gradient': `linear-gradient(90deg, ${colorFrom}, ${colorTo}`\n      }\n    ]);\n\n    const v = round(value);\n    this.el.setAttribute('aria-valuenow', `${v}`);\n    this.el.setAttribute('aria-valuetext', `${v}%`);\n  }\n\n  getMove(offset: Offset, key?: boolean): Record<string, number> {\n    // Alpha always fit into [0, 1] range\n    return { a: key ? clamp(this.hsva.a + offset.x) : offset.x };\n  }\n}\n"]}