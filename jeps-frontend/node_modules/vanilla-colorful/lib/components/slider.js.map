{"version": 3, "file": "slider.js", "sourceRoot": "", "sources": ["../../src/lib/components/slider.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAOzC,IAAI,UAAU,GAAG,KAAK,CAAC;AAEvB,2CAA2C;AAC3C,MAAM,OAAO,GAAG,CAAC,CAAQ,EAAmB,EAAE,CAAC,SAAS,IAAI,CAAC,CAAC;AAE9D,oFAAoF;AACpF,sFAAsF;AACtF,MAAM,OAAO,GAAG,CAAC,KAAY,EAAW,EAAE;IACxC,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QAAE,OAAO,KAAK,CAAC;IAChD,IAAI,CAAC,UAAU;QAAE,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;IAC7C,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,WAAW,GAAG,CAAC,MAAc,EAAE,KAAY,EAAQ,EAAE;IACzD,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAE,KAAoB,CAAC;IAC1E,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,qBAAqB,EAAE,CAAC;IAE/C,IAAI,CACF,MAAM,CAAC,EAAE,EACT,MAAM,EACN,MAAM,CAAC,OAAO,CAAC;QACb,CAAC,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;QACzE,CAAC,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;KAC1E,CAAC,CACH,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,OAAO,GAAG,CAAC,MAAc,EAAE,KAAoB,EAAQ,EAAE;IAC7D,uEAAuE;IACvE,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;IAC9B,uEAAuE;IACvE,IAAI,OAAO,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,OAAO,GAAG,EAAE,CAAC,IAAI,OAAO,GAAG,EAAE;QAAE,OAAO;IACxE,kEAAkE;IAClE,KAAK,CAAC,cAAc,EAAE,CAAC;IACvB,gDAAgD;IAChD,IAAI,CACF,MAAM,CAAC,EAAE,EACT,MAAM,EACN,MAAM,CAAC,OAAO,CACZ;QACE,CAAC,EACC,OAAO,KAAK,EAAE,CAAC,cAAc;YAC3B,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC,aAAa;gBAC9B,CAAC,CAAC,CAAC,IAAI;gBACP,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC,YAAY;oBAC7B,CAAC,CAAC,IAAI;oBACN,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC,UAAU;wBAC3B,CAAC,CAAC,CAAC,IAAI;wBACP,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC,MAAM;4BACvB,CAAC,CAAC,CAAC;4BACH,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC,OAAO;gCACxB,CAAC,CAAC,CAAC,CAAC;gCACJ,CAAC,CAAC,CAAC;QACP,CAAC,EACC,OAAO,KAAK,EAAE,CAAC,aAAa;YAC1B,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC,WAAW;gBAC5B,CAAC,CAAC,CAAC,IAAI;gBACP,CAAC,CAAC,CAAC;KACR,EACD,IAAI,CACL,CACF,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,OAAgB,MAAM;IAO1B,YAAY,IAAgB,EAAE,IAAY,EAAE,IAAY,EAAE,EAAW;QACnE,MAAM,QAAQ,GAAG,GAAG,CAClB,yCAAyC,IAAI,KAAK,IAAI,eAAe,IAAI,wBAAwB,CAClG,CAAC;QACF,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QAEnD,MAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,IAAI,GAAG,CAAgB,CAAC;QAC/D,EAAE,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACvC,EAAE,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QACxC,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QAEb,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,UAAyB,EAAE,EAAE,CAAC,CAAC;IAClD,CAAC;IAED,IAAI,QAAQ,CAAC,KAAc;QACzB,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,mBAAmB,CAAC;QACrF,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAC1D,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IACzD,CAAC;IAED,WAAW,CAAC,KAAY;QACtB,QAAQ,KAAK,CAAC,IAAI,EAAE;YAClB,KAAK,WAAW,CAAC;YACjB,KAAK,YAAY;gBACf,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,4DAA4D;gBAC5D,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,UAAU,IAAK,KAAoB,CAAC,MAAM,IAAI,CAAC,CAAC;oBAAE,OAAO;gBAClF,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;gBAChB,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,MAAM;YACR,KAAK,WAAW,CAAC;YACjB,KAAK,WAAW;gBACd,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBACzB,MAAM;YACR,KAAK,SAAS,CAAC;YACf,KAAK,UAAU;gBACb,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;gBACtB,MAAM;YACR,KAAK,SAAS;gBACZ,OAAO,CAAC,IAAI,EAAE,KAAsB,CAAC,CAAC;gBACtC,MAAM;SACT;IACH,CAAC;IAMD,KAAK,CAAC,MAAqC;QACzC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;YAC1B,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE;gBACrB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aAC9C;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["import type { HsvaColor } from '../types.js';\nimport { fire, tpl } from '../utils/dom.js';\nimport { clamp } from '../utils/math.js';\n\nexport interface Offset {\n  x: number;\n  y: number;\n}\n\nlet hasTouched = false;\n\n// Check if an event was triggered by touch\nconst isTouch = (e: Event): e is TouchEvent => 'touches' in e;\n\n// Prevent mobile browsers from handling mouse events (conflicting with touch ones).\n// If we detected a touch interaction before, we prefer reacting to touch events only.\nconst isValid = (event: Event): boolean => {\n  if (hasTouched && !isTouch(event)) return false;\n  if (!hasTouched) hasTouched = isTouch(event);\n  return true;\n};\n\nconst pointerMove = (target: Slider, event: Event): void => {\n  const pointer = isTouch(event) ? event.touches[0] : (event as MouseEvent);\n  const rect = target.el.getBoundingClientRect();\n\n  fire(\n    target.el,\n    'move',\n    target.getMove({\n      x: clamp((pointer.pageX - (rect.left + window.pageXOffset)) / rect.width),\n      y: clamp((pointer.pageY - (rect.top + window.pageYOffset)) / rect.height)\n    })\n  );\n};\n\nconst keyMove = (target: Slider, event: KeyboardEvent): void => {\n  // We use `keyCode` instead of `key` to reduce the size of the library.\n  const keyCode = event.keyCode;\n  // Ignore all keys except arrow ones, Page Up, Page Down, Home and End.\n  if (keyCode > 40 || (target.xy && keyCode < 37) || keyCode < 33) return;\n  // Do not scroll page by keys when color picker element has focus.\n  event.preventDefault();\n  // Send relative offset to the parent component.\n  fire(\n    target.el,\n    'move',\n    target.getMove(\n      {\n        x:\n          keyCode === 39 // Arrow Right\n            ? 0.01\n            : keyCode === 37 // Arrow Left\n            ? -0.01\n            : keyCode === 34 // Page Down\n            ? 0.05\n            : keyCode === 33 // Page Up\n            ? -0.05\n            : keyCode === 35 // End\n            ? 1\n            : keyCode === 36 // Home\n            ? -1\n            : 0,\n        y:\n          keyCode === 40 // Arrow down\n            ? 0.01\n            : keyCode === 38 // Arrow Up\n            ? -0.01\n            : 0\n      },\n      true\n    )\n  );\n};\n\nexport abstract class Slider {\n  declare nodes: HTMLElement[];\n\n  declare el: HTMLElement;\n\n  declare xy: boolean;\n\n  constructor(root: ShadowRoot, part: string, aria: string, xy: boolean) {\n    const template = tpl(\n      `<div role=\"slider\" tabindex=\"0\" part=\"${part}\" ${aria}><div part=\"${part}-pointer\"></div></div>`\n    );\n    root.appendChild(template.content.cloneNode(true));\n\n    const el = root.querySelector(`[part=${part}]`) as HTMLElement;\n    el.addEventListener('mousedown', this);\n    el.addEventListener('touchstart', this);\n    el.addEventListener('keydown', this);\n    this.el = el;\n\n    this.xy = xy;\n    this.nodes = [el.firstChild as HTMLElement, el];\n  }\n\n  set dragging(state: boolean) {\n    const toggleEvent = state ? document.addEventListener : document.removeEventListener;\n    toggleEvent(hasTouched ? 'touchmove' : 'mousemove', this);\n    toggleEvent(hasTouched ? 'touchend' : 'mouseup', this);\n  }\n\n  handleEvent(event: Event): void {\n    switch (event.type) {\n      case 'mousedown':\n      case 'touchstart':\n        event.preventDefault();\n        // event.button is 0 in mousedown for left button activation\n        if (!isValid(event) || (!hasTouched && (event as MouseEvent).button != 0)) return;\n        this.el.focus();\n        pointerMove(this, event);\n        this.dragging = true;\n        break;\n      case 'mousemove':\n      case 'touchmove':\n        event.preventDefault();\n        pointerMove(this, event);\n        break;\n      case 'mouseup':\n      case 'touchend':\n        this.dragging = false;\n        break;\n      case 'keydown':\n        keyMove(this, event as KeyboardEvent);\n        break;\n    }\n  }\n\n  abstract getMove(offset: Offset, key?: boolean): Record<string, number>;\n\n  abstract update(hsva: HsvaColor): void;\n\n  style(styles: Array<Record<string, string>>): void {\n    styles.forEach((style, i) => {\n      for (const p in style) {\n        this.nodes[i].style.setProperty(p, style[p]);\n      }\n    });\n  }\n}\n"]}