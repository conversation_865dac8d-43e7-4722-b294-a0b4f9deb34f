{"version": 3, "file": "hue.js", "sourceRoot": "", "sources": ["../../src/lib/components/hue.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAU,MAAM,aAAa,CAAC;AAC7C,OAAO,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AACtD,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAGhD,MAAM,OAAO,GAAI,SAAQ,MAAM;IAG7B,YAAY,IAAgB;QAC1B,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,wDAAwD,EAAE,KAAK,CAAC,CAAC;IACtF,CAAC;IAED,MAAM,CAAC,EAAE,CAAC,EAAa;QACrB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACX,IAAI,CAAC,KAAK,CAAC;YACT;gBACE,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG;gBAC3B,KAAK,EAAE,eAAe,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;aACpD;SACF,CAAC,CAAC;QACH,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,eAAe,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACvD,CAAC;IAED,OAAO,CAAC,MAAc,EAAE,GAAa;QACnC,oEAAoE;QACpE,OAAO,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC;IAC9E,CAAC;CACF", "sourcesContent": ["import { Slider, Offset } from './slider.js';\nimport { hsvaToHslString } from '../utils/convert.js';\nimport { clamp, round } from '../utils/math.js';\nimport type { HsvaColor } from '../types';\n\nexport class <PERSON><PERSON> extends Slider {\n  declare h: number;\n\n  constructor(root: ShadowRoot) {\n    super(root, 'hue', 'aria-label=\"Hue\" aria-valuemin=\"0\" aria-valuemax=\"360\"', false);\n  }\n\n  update({ h }: HsvaColor): void {\n    this.h = h;\n    this.style([\n      {\n        left: `${(h / 360) * 100}%`,\n        color: hsvaToHslString({ h, s: 100, v: 100, a: 1 })\n      }\n    ]);\n    this.el.setAttribute('aria-valuenow', `${round(h)}`);\n  }\n\n  getMove(offset: Offset, key?: boolean): Record<string, number> {\n    // Hue measured in degrees of the color circle ranging from 0 to 360\n    return { h: key ? clamp(this.h + offset.x * 360, 0, 360) : 360 * offset.x };\n  }\n}\n"]}