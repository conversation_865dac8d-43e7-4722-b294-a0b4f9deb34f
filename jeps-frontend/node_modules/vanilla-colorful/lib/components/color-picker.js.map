{"version": 3, "file": "color-picker.js", "sourceRoot": "", "sources": ["../../src/lib/components/color-picker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,iBAAiB,CAAC;AAE5C,OAAO,EAAE,GAAG,EAAE,MAAM,UAAU,CAAC;AAC/B,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAE7C,OAAO,GAAG,MAAM,2BAA2B,CAAC;AAC5C,OAAO,MAAM,MAAM,kBAAkB,CAAC;AACtC,OAAO,aAAa,MAAM,yBAAyB,CAAC;AAEpD,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AAC/B,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;AAC/B,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AAC7B,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AACjC,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;AAE/B,MAAM,CAAC,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAClC,MAAM,CAAC,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AAI1C,MAAM,OAAgB,WAAgC,SAAQ,WAAW;IACvE,MAAM,KAAK,kBAAkB;QAC3B,OAAO,CAAC,OAAO,CAAC,CAAC;IACnB,CAAC;IAED,IAAc,CAAC,IAAI,CAAC;QAClB,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IACtC,CAAC;IAED,IAAc,CAAC,QAAQ,CAAC;QACtB,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IAC3B,CAAC;IAUD,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;IAED,IAAI,KAAK,CAAC,QAAW;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE;YAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACjD,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;SACzB;IACH,CAAC;IAED;QACE,KAAK,EAAE,CAAC;QACR,MAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;QAC9D,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;QACjD,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IAClE,CAAC;IAED,iBAAiB;QACf,4DAA4D;QAC5D,yDAAyD;QACzD,4DAA4D;QAC5D,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;YAChC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACzB,OAAO,IAAI,CAAC,OAAqB,CAAC,CAAC;YACnC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACpB;aAAM,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;SAC3C;IACH,CAAC;IAED,wBAAwB,CAAC,KAAa,EAAE,OAAe,EAAE,MAAc;QACrE,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC/C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE;YACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACpB;IACH,CAAC;IAED,WAAW,CAAC,KAAkB;QAC5B,0DAA0D;QAC1D,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5B,MAAM,OAAO,GAAG,EAAE,GAAG,OAAO,EAAE,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;QAChD,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC;QACvB,IAAI,QAAQ,CAAC;QACb,IACE,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC;YACpC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAC9D;YACA,IAAI,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;YACxB,IAAI,CAAC,IAAI,EAAE,eAAe,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;SAClD;IACH,CAAC;IAEO,CAAC,OAAO,CAAC,CAAC,KAAQ;QACxB,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IAChE,CAAC;IAEO,CAAC,OAAO,CAAC,CAAC,IAAe;QAC/B,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACpD,CAAC;CACF", "sourcesContent": ["import { equalColorObjects } from '../utils/compare.js';\nimport { fire, tpl } from '../utils/dom.js';\nimport type { AnyColor, ColorModel, HsvaColor } from '../types';\nimport { Hue } from './hue.js';\nimport { Saturation } from './saturation.js';\nimport type { Slider } from './slider.js';\nimport css from '../styles/color-picker.js';\nimport hueCss from '../styles/hue.js';\nimport saturationCss from '../styles/saturation.js';\n\nconst $isSame = Symbol('same');\nconst $color = Symbol('color');\nconst $hsva = Symbol('hsva');\nconst $update = Symbol('update');\nconst $parts = Symbol('parts');\n\nexport const $css = Symbol('css');\nexport const $sliders = Symbol('sliders');\n\nexport type Sliders = Array<new (root: ShadowRoot) => Slider>;\n\nexport abstract class ColorPicker<C extends AnyColor> extends HTMLElement {\n  static get observedAttributes(): string[] {\n    return ['color'];\n  }\n\n  protected get [$css](): string[] {\n    return [css, hueCss, saturationCss];\n  }\n\n  protected get [$sliders](): Sliders {\n    return [Saturation, Hue];\n  }\n\n  protected abstract get colorModel(): ColorModel<C>;\n\n  private declare [$hsva]: HsvaColor;\n\n  private declare [$color]: C;\n\n  private declare [$parts]: Slider[];\n\n  get color(): C {\n    return this[$color];\n  }\n\n  set color(newColor: C) {\n    if (!this[$isSame](newColor)) {\n      const newHsva = this.colorModel.toHsva(newColor);\n      this[$update](newHsva);\n      this[$color] = newColor;\n    }\n  }\n\n  constructor() {\n    super();\n    const template = tpl(`<style>${this[$css].join('')}</style>`);\n    const root = this.attachShadow({ mode: 'open' });\n    root.appendChild(template.content.cloneNode(true));\n    root.addEventListener('move', this);\n    this[$parts] = this[$sliders].map((slider) => new slider(root));\n  }\n\n  connectedCallback(): void {\n    // A user may set a property on an _instance_ of an element,\n    // before its prototype has been connected to this class.\n    // If so, we need to run it through the proper class setter.\n    if (this.hasOwnProperty('color')) {\n      const value = this.color;\n      delete this['color' as keyof this];\n      this.color = value;\n    } else if (!this.color) {\n      this.color = this.colorModel.defaultColor;\n    }\n  }\n\n  attributeChangedCallback(_attr: string, _oldVal: string, newVal: string): void {\n    const color = this.colorModel.fromAttr(newVal);\n    if (!this[$isSame](color)) {\n      this.color = color;\n    }\n  }\n\n  handleEvent(event: CustomEvent): void {\n    // Merge the current HSV color object with updated params.\n    const oldHsva = this[$hsva];\n    const newHsva = { ...oldHsva, ...event.detail };\n    this[$update](newHsva);\n    let newColor;\n    if (\n      !equalColorObjects(newHsva, oldHsva) &&\n      !this[$isSame]((newColor = this.colorModel.fromHsva(newHsva)))\n    ) {\n      this[$color] = newColor;\n      fire(this, 'color-changed', { value: newColor });\n    }\n  }\n\n  private [$isSame](color: C): boolean {\n    return this.color && this.colorModel.equal(color, this.color);\n  }\n\n  private [$update](hsva: HsvaColor): void {\n    this[$hsva] = hsva;\n    this[$parts].forEach((part) => part.update(hsva));\n  }\n}\n"]}