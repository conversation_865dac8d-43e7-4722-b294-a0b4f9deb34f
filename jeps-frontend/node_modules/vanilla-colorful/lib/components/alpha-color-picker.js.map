{"version": 3, "file": "alpha-color-picker.js", "sourceRoot": "", "sources": ["../../src/lib/components/alpha-color-picker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAW,IAAI,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAEzE,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACnC,OAAO,QAAQ,MAAM,oBAAoB,CAAC;AAE1C,MAAM,OAAgB,gBAAqC,SAAQ,WAAc;IAC/E,IAAc,CAAC,IAAI,CAAC;QAClB,OAAO,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;IACpC,CAAC;IAED,IAAc,CAAC,QAAQ,CAAC;QACtB,OAAO,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,CAAC;IACrC,CAAC;CACF", "sourcesContent": ["import { ColorPicker, Sliders, $css, $sliders } from './color-picker.js';\nimport type { AnyColor } from '../types';\nimport { Alpha } from './alpha.js';\nimport alphaCss from '../styles/alpha.js';\n\nexport abstract class AlphaColorPicker<C extends AnyColor> extends ColorPicker<C> {\n  protected get [$css](): string[] {\n    return [...super[$css], alphaCss];\n  }\n\n  protected get [$sliders](): Sliders {\n    return [...super[$sliders], Alpha];\n  }\n}\n"]}