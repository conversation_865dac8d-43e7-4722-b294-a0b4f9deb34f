{"version": 3, "file": "saturation.js", "sourceRoot": "", "sources": ["../../src/lib/components/saturation.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAU,MAAM,aAAa,CAAC;AAC7C,OAAO,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AACtD,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAGhD,MAAM,OAAO,UAAW,SAAQ,MAAM;IAGpC,YAAY,IAAgB;QAC1B,KAAK,CAAC,IAAI,EAAE,YAAY,EAAE,oBAAoB,EAAE,IAAI,CAAC,CAAC;IACxD,CAAC;IAED,MAAM,CAAC,IAAe;QACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,CAAC;YACT;gBACE,GAAG,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG;gBACvB,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG;gBAClB,KAAK,EAAE,eAAe,CAAC,IAAI,CAAC;aAC7B;YACD;gBACE,kBAAkB,EAAE,eAAe,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;aACzE;SACF,CAAC,CAAC;QACH,IAAI,CAAC,EAAE,CAAC,YAAY,CAClB,gBAAgB,EAChB,cAAc,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAC7D,CAAC;IACJ,CAAC;IAED,OAAO,CAAC,MAAc,EAAE,GAAa;QACnC,2DAA2D;QAC3D,OAAO;YACL,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,GAAG;YACrE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;SACxF,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["import { Slider, Offset } from './slider.js';\nimport { hsvaToHslString } from '../utils/convert.js';\nimport { clamp, round } from '../utils/math.js';\nimport type { HsvaColor } from '../types';\n\nexport class Saturation extends Slider {\n  declare hsva: HsvaColor;\n\n  constructor(root: ShadowRoot) {\n    super(root, 'saturation', 'aria-label=\"Color\"', true);\n  }\n\n  update(hsva: HsvaColor): void {\n    this.hsva = hsva;\n    this.style([\n      {\n        top: `${100 - hsva.v}%`,\n        left: `${hsva.s}%`,\n        color: hsvaToHslString(hsva)\n      },\n      {\n        'background-color': hsvaToHslString({ h: hsva.h, s: 100, v: 100, a: 1 })\n      }\n    ]);\n    this.el.setAttribute(\n      'aria-valuetext',\n      `Saturation ${round(hsva.s)}%, Brightness ${round(hsva.v)}%`\n    );\n  }\n\n  getMove(offset: Offset, key?: boolean): Record<string, number> {\n    // Saturation and brightness always fit into [0, 100] range\n    return {\n      s: key ? clamp(this.hsva.s + offset.x * 100, 0, 100) : offset.x * 100,\n      v: key ? clamp(this.hsva.v - offset.y * 100, 0, 100) : Math.round(100 - offset.y * 100)\n    };\n  }\n}\n"]}