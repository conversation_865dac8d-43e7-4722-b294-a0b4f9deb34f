{"version": 3, "file": "compare.js", "sourceRoot": "", "sources": ["../../src/lib/utils/compare.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAGzC,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAAC,KAAkB,EAAE,MAAmB,EAAW,EAAE;IACpF,IAAI,KAAK,KAAK,MAAM;QAAE,OAAO,IAAI,CAAC;IAElC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;QACxB,6GAA6G;QAC7G,iHAAiH;QACjH,4GAA4G;QAC5G,8GAA8G;QAC9G,4DAA4D;QAC5D,IACG,KAA2C,CAAC,IAAI,CAAC;YACjD,MAA4C,CAAC,IAAI,CAAC;YAEnD,OAAO,KAAK,CAAC;KAChB;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,KAAa,EAAE,MAAc,EAAW,EAAE;IACzE,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAChE,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,KAAa,EAAE,MAAc,EAAW,EAAE;IACjE,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,WAAW,EAAE;QAAE,OAAO,IAAI,CAAC;IAE9D,8EAA8E;IAC9E,OAAO,iBAAiB,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;AAChE,CAAC,CAAC", "sourcesContent": ["import { hexToRgba } from './convert.js';\nimport type { ObjectColor } from '../types';\n\nexport const equalColorObjects = (first: ObjectColor, second: ObjectColor): boolean => {\n  if (first === second) return true;\n\n  for (const prop in first) {\n    // The following allows for a type-safe calling of this function (first & second have to be HSL, HSV, or RGB)\n    // with type-unsafe iterating over object keys. TS does not allow this without an index (`[key: string]: number`)\n    // on an object to define how iteration is normally done. To ensure extra keys are not allowed on our types,\n    // we must cast our object to unknown (as RGB demands `r` be a key, while `Record<string, x>` does not care if\n    // there is or not), and then as a type TS can iterate over.\n    if (\n      (first as unknown as Record<string, number>)[prop] !==\n      (second as unknown as Record<string, number>)[prop]\n    )\n      return false;\n  }\n\n  return true;\n};\n\nexport const equalColorString = (first: string, second: string): boolean => {\n  return first.replace(/\\s/g, '') === second.replace(/\\s/g, '');\n};\n\nexport const equalHex = (first: string, second: string): boolean => {\n  if (first.toLowerCase() === second.toLowerCase()) return true;\n\n  // To compare colors like `#FFF` and `ffffff` we convert them into RGB objects\n  return equalColorObjects(hexToRgba(first), hexToRgba(second));\n};\n"]}