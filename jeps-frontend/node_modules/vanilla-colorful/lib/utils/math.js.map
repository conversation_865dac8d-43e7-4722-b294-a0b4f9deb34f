{"version": 3, "file": "math.js", "sourceRoot": "", "sources": ["../../src/lib/utils/math.ts"], "names": [], "mappings": "AAAA,mDAAmD;AACnD,8DAA8D;AAC9D,mDAAmD;AACnD,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,MAAc,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAU,EAAE;IAChE,OAAO,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;AAC1D,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,MAAc,EAAE,MAAM,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,EAAU,EAAE;IACvF,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC;AAC1C,CAAC,CAAC", "sourcesContent": ["// Clamps a value between an upper and lower bound.\n// We use ternary operators because it makes the minified code\n// 2 times shorter then `Math.min(Math.max(a,b),c)`\nexport const clamp = (number: number, min = 0, max = 1): number => {\n  return number > max ? max : number < min ? min : number;\n};\n\nexport const round = (number: number, digits = 0, base = Math.pow(10, digits)): number => {\n  return Math.round(base * number) / base;\n};\n"]}