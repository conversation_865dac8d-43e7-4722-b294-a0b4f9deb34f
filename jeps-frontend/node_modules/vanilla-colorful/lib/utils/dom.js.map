{"version": 3, "file": "dom.js", "sourceRoot": "", "sources": ["../../src/lib/utils/dom.ts"], "names": [], "mappings": "AAAA,MAAM,KAAK,GAAwC,EAAE,CAAC;AAEtD,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,IAAY,EAAuB,EAAE;IACvD,IAAI,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;IAC3B,IAAI,CAAC,QAAQ,EAAE;QACb,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAC9C,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;KACxB;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,MAAmB,EAAE,IAAY,EAAE,MAA+B,EAAQ,EAAE;IAC/F,MAAM,CAAC,aAAa,CAClB,IAAI,WAAW,CAAC,IAAI,EAAE;QACpB,OAAO,EAAE,IAAI;QACb,MAAM;KACP,CAAC,CACH,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["const cache: Record<string, HTMLTemplateElement> = {};\n\nexport const tpl = (html: string): HTMLTemplateElement => {\n  let template = cache[html];\n  if (!template) {\n    template = document.createElement('template');\n    template.innerHTML = html;\n    cache[html] = template;\n  }\n  return template;\n};\n\nexport const fire = (target: HTMLElement, type: string, detail: Record<string, unknown>): void => {\n  target.dispatchEvent(\n    new CustomEvent(type, {\n      bubbles: true,\n      detail\n    })\n  );\n};\n"]}