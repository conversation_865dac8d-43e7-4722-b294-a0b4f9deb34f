import { RgbaColor, RgbColor, HslaColor, HslColor, HsvaColor, HsvColor } from '../types';
export declare const hexToHsva: (hex: string) => HsvaColor;
export declare const hexToRgba: (hex: string) => RgbaColor;
export declare const parseHue: (value: string, unit?: string) => number;
export declare const hslaStringToHsva: (hslString: string) => HsvaColor;
export declare const hslStringToHsva: (hslString: string) => HsvaColor;
export declare const hslaToHsva: ({ h, s, l, a }: HslaColor) => HsvaColor;
export declare const hsvaToHex: (hsva: HsvaColor) => string;
export declare const hsvaToHsla: ({ h, s, v, a }: HsvaColor) => HslaColor;
export declare const hsvaToHsvString: (hsva: HsvaColor) => string;
export declare const hsvaToHsvaString: (hsva: HsvaColor) => string;
export declare const hsvaToHslString: (hsva: HsvaColor) => string;
export declare const hsvaToHslaString: (hsva: HsvaColor) => string;
export declare const hsvaToRgba: ({ h, s, v, a }: HsvaColor) => RgbaColor;
export declare const hsvaToRgbString: (hsva: HsvaColor) => string;
export declare const hsvaToRgbaString: (hsva: HsvaColor) => string;
export declare const hsvaStringToHsva: (hsvString: string) => HsvaColor;
export declare const hsvStringToHsva: (hsvString: string) => HsvaColor;
export declare const rgbaStringToHsva: (rgbaString: string) => HsvaColor;
export declare const rgbStringToHsva: (rgbaString: string) => HsvaColor;
export declare const rgbaToHex: ({ r, g, b, a }: RgbaColor) => string;
export declare const rgbaToHsva: ({ r, g, b, a }: RgbaColor) => HsvaColor;
export declare const roundHsva: (hsva: HsvaColor) => HsvaColor;
export declare const rgbaToRgb: ({ r, g, b }: RgbaColor) => RgbColor;
export declare const hslaToHsl: ({ h, s, l }: HslaColor) => HslColor;
export declare const hsvaToHsv: (hsva: HsvaColor) => HsvColor;
//# sourceMappingURL=convert.d.ts.map