{"version": 3, "file": "validate.js", "sourceRoot": "", "sources": ["../../src/lib/utils/validate.ts"], "names": [], "mappings": "AAAA,MAAM,OAAO,GAAG,sBAAsB,CAAC;AAEvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,KAAa,EAAE,KAAe,EAAW,EAAE;IAClE,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClC,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAE3C,OAAO,CACL,MAAM,KAAK,CAAC,IAAI,gBAAgB;QAChC,MAAM,KAAK,CAAC,IAAI,mBAAmB;QACnC,CAAC,CAAC,CAAC,KAAK,IAAI,MAAM,KAAK,CAAC,CAAC,IAAI,iBAAiB;QAC9C,CAAC,CAAC,CAAC,KAAK,IAAI,MAAM,KAAK,CAAC,CAAC,CAAC,qBAAqB;KAChD,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["const matcher = /^#?([0-9A-F]{3,8})$/i;\n\nexport const validHex = (value: string, alpha?: boolean): boolean => {\n  const match = matcher.exec(value);\n  const length = match ? match[1].length : 0;\n\n  return (\n    length === 3 || // '#rgb' format\n    length === 6 || // '#rrggbb' format\n    (!!alpha && length === 4) || // '#rgba' format\n    (!!alpha && length === 8) // '#rrggbbaa' format\n  );\n};\n"]}