{"version": 3, "file": "convert.js", "sourceRoot": "", "sources": ["../../src/lib/utils/convert.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,MAAM,WAAW,CAAC;AAElC;;;GAGG;AACH,MAAM,UAAU,GAA2B;IACzC,IAAI,EAAE,GAAG,GAAG,GAAG;IACf,IAAI,EAAE,GAAG;IACT,GAAG,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;CACzB,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,GAAW,EAAa,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;AAEhF,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,GAAW,EAAa,EAAE;IAClD,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG;QAAE,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAE3C,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;QAClB,OAAO;YACL,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YAChC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YAChC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YAChC,CAAC,EAAE,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACxE,CAAC;KACH;IAED,OAAO;QACL,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;QACpC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;QACpC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;QACpC,CAAC,EAAE,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC5E,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,KAAa,EAAE,IAAI,GAAG,KAAK,EAAU,EAAE;IAC9D,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACjD,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,SAAiB,EAAa,EAAE;IAC/D,MAAM,OAAO,GACX,4HAA4H,CAAC;IAC/H,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAEtC,IAAI,CAAC,KAAK;QAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAE9C,OAAO,UAAU,CAAC;QAChB,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACxE,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAG,gBAAgB,CAAC;AAEhD,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAa,EAAa,EAAE;IACjE,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;IAElC,OAAO;QACL,CAAC,EAAE,CAAC;QACJ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,EAAE,CAAC,GAAG,CAAC;QACR,CAAC;KACF,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,IAAe,EAAU,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;AAElF,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAa,EAAa,EAAE;IACjE,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;IAEjC,OAAO;QACL,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;QACX,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACtF,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;QAChB,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;KACf,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,IAAe,EAAU,EAAE;IACzD,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;IACpC,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;AACnC,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,IAAe,EAAU,EAAE;IAC1D,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;IACvC,OAAO,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC;AAC1C,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,IAAe,EAAU,EAAE;IACzD,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;IACrC,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;AACnC,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,IAAe,EAAU,EAAE;IAC1D,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;IACxC,OAAO,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC;AAC1C,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAa,EAAa,EAAE;IACjE,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAClB,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;IACZ,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;IAEZ,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EACtB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EACf,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAC1B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAC9B,MAAM,GAAG,EAAE,GAAG,CAAC,CAAC;IAElB,OAAO;QACL,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;QAC1C,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;QAC1C,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;QAC1C,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;KACf,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,IAAe,EAAU,EAAE;IACzD,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;IACrC,OAAO,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC;AACjC,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,IAAe,EAAU,EAAE;IAC1D,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;IACxC,OAAO,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC;AACxC,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,SAAiB,EAAa,EAAE;IAC/D,MAAM,OAAO,GACX,4HAA4H,CAAC;IAC/H,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAEtC,IAAI,CAAC,KAAK;QAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAE9C,OAAO,SAAS,CAAC;QACf,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACxE,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAG,gBAAgB,CAAC;AAEhD,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,UAAkB,EAAa,EAAE;IAChE,MAAM,OAAO,GACX,gHAAgH,CAAC;IACnH,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAEvC,IAAI,CAAC,KAAK;QAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAE9C,OAAO,UAAU,CAAC;QAChB,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACxE,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAG,gBAAgB,CAAC;AAEhD,MAAM,MAAM,GAAG,CAAC,MAAc,EAAE,EAAE;IAChC,MAAM,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAChC,OAAO,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;AAC1C,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAa,EAAU,EAAE;IAC7D,MAAM,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACrD,OAAO,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;AAC5D,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAa,EAAa,EAAE;IACjE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9B,MAAM,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAEtC,kBAAkB;IAClB,MAAM,EAAE,GAAG,KAAK;QACd,CAAC,CAAC,GAAG,KAAK,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;YACjB,CAAC,CAAC,GAAG,KAAK,CAAC;gBACT,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;gBACrB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;QACzB,CAAC,CAAC,CAAC,CAAC;IAEN,OAAO;QACL,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACrC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAC3B,CAAC;KACF,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,IAAe,EAAa,EAAE,CAAC,CAAC;IACxD,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IAChB,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IAChB,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IAChB,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;CACpB,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAa,EAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAE7E,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAa,EAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAE7E,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,IAAe,EAAY,EAAE;IACrD,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;IACpC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACrB,CAAC,CAAC", "sourcesContent": ["import { RgbaColor, RgbColor, HslaColor, HslColor, HsvaColor, HsvColor } from '../types';\nimport { round } from './math.js';\n\n/**\n * Valid CSS <angle> units.\n * https://developer.mozilla.org/en-US/docs/Web/CSS/angle\n */\nconst angleUnits: Record<string, number> = {\n  grad: 360 / 400,\n  turn: 360,\n  rad: 360 / (Math.PI * 2)\n};\n\nexport const hexToHsva = (hex: string): HsvaColor => rgbaToHsva(hexToRgba(hex));\n\nexport const hexToRgba = (hex: string): RgbaColor => {\n  if (hex[0] === '#') hex = hex.substring(1);\n\n  if (hex.length < 6) {\n    return {\n      r: parseInt(hex[0] + hex[0], 16),\n      g: parseInt(hex[1] + hex[1], 16),\n      b: parseInt(hex[2] + hex[2], 16),\n      a: hex.length === 4 ? round(parseInt(hex[3] + hex[3], 16) / 255, 2) : 1\n    };\n  }\n\n  return {\n    r: parseInt(hex.substring(0, 2), 16),\n    g: parseInt(hex.substring(2, 4), 16),\n    b: parseInt(hex.substring(4, 6), 16),\n    a: hex.length === 8 ? round(parseInt(hex.substring(6, 8), 16) / 255, 2) : 1\n  };\n};\n\nexport const parseHue = (value: string, unit = 'deg'): number => {\n  return Number(value) * (angleUnits[unit] || 1);\n};\n\nexport const hslaStringToHsva = (hslString: string): HsvaColor => {\n  const matcher =\n    /hsla?\\(?\\s*(-?\\d*\\.?\\d+)(deg|rad|grad|turn)?[,\\s]+(-?\\d*\\.?\\d+)%?[,\\s]+(-?\\d*\\.?\\d+)%?,?\\s*[/\\s]*(-?\\d*\\.?\\d+)?(%)?\\s*\\)?/i;\n  const match = matcher.exec(hslString);\n\n  if (!match) return { h: 0, s: 0, v: 0, a: 1 };\n\n  return hslaToHsva({\n    h: parseHue(match[1], match[2]),\n    s: Number(match[3]),\n    l: Number(match[4]),\n    a: match[5] === undefined ? 1 : Number(match[5]) / (match[6] ? 100 : 1)\n  });\n};\n\nexport const hslStringToHsva = hslaStringToHsva;\n\nexport const hslaToHsva = ({ h, s, l, a }: HslaColor): HsvaColor => {\n  s *= (l < 50 ? l : 100 - l) / 100;\n\n  return {\n    h: h,\n    s: s > 0 ? ((2 * s) / (l + s)) * 100 : 0,\n    v: l + s,\n    a\n  };\n};\n\nexport const hsvaToHex = (hsva: HsvaColor): string => rgbaToHex(hsvaToRgba(hsva));\n\nexport const hsvaToHsla = ({ h, s, v, a }: HsvaColor): HslaColor => {\n  const hh = ((200 - s) * v) / 100;\n\n  return {\n    h: round(h),\n    s: round(hh > 0 && hh < 200 ? ((s * v) / 100 / (hh <= 100 ? hh : 200 - hh)) * 100 : 0),\n    l: round(hh / 2),\n    a: round(a, 2)\n  };\n};\n\nexport const hsvaToHsvString = (hsva: HsvaColor): string => {\n  const { h, s, v } = roundHsva(hsva);\n  return `hsv(${h}, ${s}%, ${v}%)`;\n};\n\nexport const hsvaToHsvaString = (hsva: HsvaColor): string => {\n  const { h, s, v, a } = roundHsva(hsva);\n  return `hsva(${h}, ${s}%, ${v}%, ${a})`;\n};\n\nexport const hsvaToHslString = (hsva: HsvaColor): string => {\n  const { h, s, l } = hsvaToHsla(hsva);\n  return `hsl(${h}, ${s}%, ${l}%)`;\n};\n\nexport const hsvaToHslaString = (hsva: HsvaColor): string => {\n  const { h, s, l, a } = hsvaToHsla(hsva);\n  return `hsla(${h}, ${s}%, ${l}%, ${a})`;\n};\n\nexport const hsvaToRgba = ({ h, s, v, a }: HsvaColor): RgbaColor => {\n  h = (h / 360) * 6;\n  s = s / 100;\n  v = v / 100;\n\n  const hh = Math.floor(h),\n    b = v * (1 - s),\n    c = v * (1 - (h - hh) * s),\n    d = v * (1 - (1 - h + hh) * s),\n    module = hh % 6;\n\n  return {\n    r: round([v, c, b, b, d, v][module] * 255),\n    g: round([d, v, v, c, b, b][module] * 255),\n    b: round([b, b, d, v, v, c][module] * 255),\n    a: round(a, 2)\n  };\n};\n\nexport const hsvaToRgbString = (hsva: HsvaColor): string => {\n  const { r, g, b } = hsvaToRgba(hsva);\n  return `rgb(${r}, ${g}, ${b})`;\n};\n\nexport const hsvaToRgbaString = (hsva: HsvaColor): string => {\n  const { r, g, b, a } = hsvaToRgba(hsva);\n  return `rgba(${r}, ${g}, ${b}, ${a})`;\n};\n\nexport const hsvaStringToHsva = (hsvString: string): HsvaColor => {\n  const matcher =\n    /hsva?\\(?\\s*(-?\\d*\\.?\\d+)(deg|rad|grad|turn)?[,\\s]+(-?\\d*\\.?\\d+)%?[,\\s]+(-?\\d*\\.?\\d+)%?,?\\s*[/\\s]*(-?\\d*\\.?\\d+)?(%)?\\s*\\)?/i;\n  const match = matcher.exec(hsvString);\n\n  if (!match) return { h: 0, s: 0, v: 0, a: 1 };\n\n  return roundHsva({\n    h: parseHue(match[1], match[2]),\n    s: Number(match[3]),\n    v: Number(match[4]),\n    a: match[5] === undefined ? 1 : Number(match[5]) / (match[6] ? 100 : 1)\n  });\n};\n\nexport const hsvStringToHsva = hsvaStringToHsva;\n\nexport const rgbaStringToHsva = (rgbaString: string): HsvaColor => {\n  const matcher =\n    /rgba?\\(?\\s*(-?\\d*\\.?\\d+)(%)?[,\\s]+(-?\\d*\\.?\\d+)(%)?[,\\s]+(-?\\d*\\.?\\d+)(%)?,?\\s*[/\\s]*(-?\\d*\\.?\\d+)?(%)?\\s*\\)?/i;\n  const match = matcher.exec(rgbaString);\n\n  if (!match) return { h: 0, s: 0, v: 0, a: 1 };\n\n  return rgbaToHsva({\n    r: Number(match[1]) / (match[2] ? 100 / 255 : 1),\n    g: Number(match[3]) / (match[4] ? 100 / 255 : 1),\n    b: Number(match[5]) / (match[6] ? 100 / 255 : 1),\n    a: match[7] === undefined ? 1 : Number(match[7]) / (match[8] ? 100 : 1)\n  });\n};\n\nexport const rgbStringToHsva = rgbaStringToHsva;\n\nconst format = (number: number) => {\n  const hex = number.toString(16);\n  return hex.length < 2 ? '0' + hex : hex;\n};\n\nexport const rgbaToHex = ({ r, g, b, a }: RgbaColor): string => {\n  const alphaHex = a < 1 ? format(round(a * 255)) : '';\n  return '#' + format(r) + format(g) + format(b) + alphaHex;\n};\n\nexport const rgbaToHsva = ({ r, g, b, a }: RgbaColor): HsvaColor => {\n  const max = Math.max(r, g, b);\n  const delta = max - Math.min(r, g, b);\n\n  // prettier-ignore\n  const hh = delta\n    ? max === r\n      ? (g - b) / delta\n      : max === g\n        ? 2 + (b - r) / delta\n        : 4 + (r - g) / delta\n    : 0;\n\n  return {\n    h: round(60 * (hh < 0 ? hh + 6 : hh)),\n    s: round(max ? (delta / max) * 100 : 0),\n    v: round((max / 255) * 100),\n    a\n  };\n};\n\nexport const roundHsva = (hsva: HsvaColor): HsvaColor => ({\n  h: round(hsva.h),\n  s: round(hsva.s),\n  v: round(hsva.v),\n  a: round(hsva.a, 2)\n});\n\nexport const rgbaToRgb = ({ r, g, b }: RgbaColor): RgbColor => ({ r, g, b });\n\nexport const hslaToHsl = ({ h, s, l }: HslaColor): HslColor => ({ h, s, l });\n\nexport const hsvaToHsv = (hsva: HsvaColor): HsvColor => {\n  const { h, s, v } = roundHsva(hsva);\n  return { h, s, v };\n};\n"]}