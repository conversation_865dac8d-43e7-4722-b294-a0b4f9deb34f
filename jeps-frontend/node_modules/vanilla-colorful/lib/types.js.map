{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../src/lib/types.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface RgbColor {\n  r: number;\n  g: number;\n  b: number;\n}\n\nexport interface RgbaColor extends RgbColor {\n  a: number;\n}\n\nexport interface HslColor {\n  h: number;\n  s: number;\n  l: number;\n}\n\nexport interface HslaColor extends HslColor {\n  a: number;\n}\n\nexport interface HsvColor {\n  h: number;\n  s: number;\n  v: number;\n}\n\nexport interface HsvaColor extends HsvColor {\n  a: number;\n}\n\nexport type ObjectColor = RgbColor | HslColor | HsvColor | RgbaColor | HslaColor | HsvaColor;\n\nexport type AnyColor = string | ObjectColor;\n\nexport interface ColorModel<T extends AnyColor> {\n  defaultColor: T;\n  toHsva: (color: T) => HsvaColor;\n  fromHsva: (hsva: HsvaColor) => T;\n  equal: (first: T, second: T) => boolean;\n  fromAttr: (attr: string) => T;\n}\n\nexport interface ColorChangedEventListener<T> {\n  (evt: T): void;\n}\n\nexport interface ColorChangedEventListenerObject<T> {\n  handleEvent(evt: T): void;\n}\n\nexport interface ColorPickerEventMap<T> extends HTMLElementEventMap {\n  'color-changed': CustomEvent<{ value: T }>;\n}\n\nexport type ColorPickerEventListener<T> =\n  | ColorChangedEventListener<T>\n  | ColorChangedEventListenerObject<T>;\n"]}