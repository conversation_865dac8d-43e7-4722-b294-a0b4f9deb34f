{"version": 3, "file": "hsv-color-picker.js", "sourceRoot": "", "sources": ["src/hsv-color-picker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,0BAA0B,CAAC;AAGnD;;;;;;;;;;;;;GAaG;AACH,MAAM,OAAO,cAAe,SAAQ,OAAO;CAAG;AAE9C,cAAc,CAAC,MAAM,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC", "sourcesContent": ["import { HsvBase } from './lib/entrypoints/hsv.js';\nexport type { HsvColor } from './lib/types';\n\n/**\n * A color picker custom element that uses HSV object format.\n *\n * @element hsv-color-picker\n *\n * @prop {HsvColor} color - Selected color in HSV object format.\n *\n * @fires color-changed - Event fired when color property changes.\n *\n * @csspart hue - A hue selector container.\n * @csspart saturation - A saturation selector container\n * @csspart hue-pointer - A hue pointer element.\n * @csspart saturation-pointer - A saturation pointer element.\n */\nexport class HsvColorPicker extends HsvBase {}\n\ncustomElements.define('hsv-color-picker', HsvColorPicker);\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'hsv-color-picker': HsvColorPicker;\n  }\n}\n"]}