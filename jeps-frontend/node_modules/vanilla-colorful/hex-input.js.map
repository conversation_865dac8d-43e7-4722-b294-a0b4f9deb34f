{"version": 3, "file": "hex-input.js", "sourceRoot": "", "sources": ["src/hex-input.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAE9D;;;;;;;;;;;;;;;GAeG;AACH,MAAM,OAAO,QAAS,SAAQ,YAAY;CAAG;AAE7C,cAAc,CAAC,MAAM,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC", "sourcesContent": ["import { HexInputBase } from './lib/entrypoints/hex-input.js';\n\n/**\n * A custom element for entering color in HEX format.\n *\n * @element hex-input\n *\n * @prop {string} color - Color in HEX format.\n * @attr {string} color - Selected color in HEX format.\n * @prop {boolean} alpha - When true, `#rgba` and `#rrggbbaa` color formats are allowed.\n * @attr {boolean} alpha - Allows `#rgba` and `#rrggbbaa` color formats.\n * @prop {boolean} prefixed - When true, `#` prefix is displayed in the input.\n * @attr {boolean} prefixed - Enables `#` prefix displaying.\n *\n * @fires color-changed - Event fired when color is changed.\n *\n * @csspart input - A native input element.\n */\nexport class HexInput extends HexInputBase {}\n\ncustomElements.define('hex-input', HexInput);\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'hex-input': HexInput;\n  }\n}\n"]}