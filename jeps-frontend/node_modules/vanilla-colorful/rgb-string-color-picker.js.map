{"version": 3, "file": "rgb-string-color-picker.js", "sourceRoot": "", "sources": ["src/rgb-string-color-picker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,iCAAiC,CAAC;AAEhE;;;;;;;;;;;;;;GAcG;AACH,MAAM,OAAO,oBAAqB,SAAQ,aAAa;CAAG;AAE1D,cAAc,CAAC,MAAM,CAAC,yBAAyB,EAAE,oBAAoB,CAAC,CAAC", "sourcesContent": ["import { RgbStringBase } from './lib/entrypoints/rgb-string.js';\n\n/**\n * A color picker custom element that uses RGB string format.\n *\n * @element rgb-string-color-picker\n *\n * @prop {string} color - Selected color in RGB string format.\n * @attr {string} color - Selected color in RGB string format.\n *\n * @fires color-changed - Event fired when color property changes.\n *\n * @csspart hue - A hue selector container.\n * @csspart saturation - A saturation selector container\n * @csspart hue-pointer - A hue pointer element.\n * @csspart saturation-pointer - A saturation pointer element.\n */\nexport class RgbStringColorPicker extends RgbStringBase {}\n\ncustomElements.define('rgb-string-color-picker', RgbStringColorPicker);\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'rgb-string-color-picker': RgbStringColorPicker;\n  }\n}\n"]}