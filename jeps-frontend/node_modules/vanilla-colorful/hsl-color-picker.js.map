{"version": 3, "file": "hsl-color-picker.js", "sourceRoot": "", "sources": ["src/hsl-color-picker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,0BAA0B,CAAC;AAGnD;;;;;;;;;;;;;GAaG;AACH,MAAM,OAAO,cAAe,SAAQ,OAAO;CAAG;AAE9C,cAAc,CAAC,MAAM,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC", "sourcesContent": ["import { HslBase } from './lib/entrypoints/hsl.js';\nexport type { HslColor } from './lib/types';\n\n/**\n * A color picker custom element that uses HSL object format.\n *\n * @element hsl-color-picker\n *\n * @prop {HslColor} color - Selected color in HSL object format.\n *\n * @fires color-changed - Event fired when color property changes.\n *\n * @csspart hue - A hue selector container.\n * @csspart saturation - A saturation selector container\n * @csspart hue-pointer - A hue pointer element.\n * @csspart saturation-pointer - A saturation pointer element.\n */\nexport class HslColorPicker extends HslBase {}\n\ncustomElements.define('hsl-color-picker', HslColorPicker);\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'hsl-color-picker': HslColorPicker;\n  }\n}\n"]}