{"version": 3, "file": "hsv-string-color-picker.js", "sourceRoot": "", "sources": ["src/hsv-string-color-picker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,iCAAiC,CAAC;AAEhE;;;;;;;;;;;;;;GAcG;AACH,MAAM,OAAO,oBAAqB,SAAQ,aAAa;CAAG;AAE1D,cAAc,CAAC,MAAM,CAAC,yBAAyB,EAAE,oBAAoB,CAAC,CAAC", "sourcesContent": ["import { HsvStringBase } from './lib/entrypoints/hsv-string.js';\n\n/**\n * A color picker custom element that uses HSV string format.\n *\n * @element hsv-string-color-picker\n *\n * @prop {string} color - Selected color in HSV string format.\n * @attr {string} color - Selected color in HSV string format.\n *\n * @fires color-changed - Event fired when color property changes.\n *\n * @csspart hue - A hue selector container.\n * @csspart saturation - A saturation selector container\n * @csspart hue-pointer - A hue pointer element.\n * @csspart saturation-pointer - A saturation pointer element.\n */\nexport class HsvStringColorPicker extends HsvStringBase {}\n\ncustomElements.define('hsv-string-color-picker', HsvStringColorPicker);\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'hsv-string-color-picker': HsvStringColorPicker;\n  }\n}\n"]}