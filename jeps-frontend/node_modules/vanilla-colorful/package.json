{"name": "vanilla-colorful", "version": "0.7.2", "description": "A tiny framework agnostic color picker element for modern web apps", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/web-padawan/vanilla-colorful.git"}, "author": "<PERSON><PERSON><PERSON> <iamku<PERSON><PERSON>@gmail.com>", "homepage": "https://web-padawan.github.io/vanilla-colorful/", "bugs": {"url": "https://github.com/web-padawan/vanilla-colorful/issues"}, "main": "hex-color-picker.js", "module": "hex-color-picker.js", "type": "module", "exports": {".": "./hex-color-picker.js", "./hex-alpha-color-picker.js": "./hex-alpha-color-picker.js", "./hex-color-picker.js": "./hex-color-picker.js", "./hex-input.js": "./hex-input.js", "./hsl-color-picker.js": "./hsl-color-picker.js", "./hsl-string-color-picker.js": "./hsl-string-color-picker.js", "./hsla-color-picker.js": "./hsla-color-picker.js", "./hsla-string-color-picker.js": "./hsla-string-color-picker.js", "./hsv-color-picker.js": "./hsv-color-picker.js", "./hsv-string-color-picker.js": "./hsv-string-color-picker.js", "./hsva-color-picker.js": "./hsva-color-picker.js", "./hsva-string-color-picker.js": "./hsva-string-color-picker.js", "./rgb-color-picker.js": "./rgb-color-picker.js", "./rgb-string-color-picker.js": "./rgb-string-color-picker.js", "./rgba-color-picker.js": "./rgba-color-picker.js", "./rgba-string-color-picker.js": "./rgba-string-color-picker.js", "./lib/entrypoints/*": "./lib/entrypoints/*.js", "./package.json": "./package.json"}, "scripts": {"analyze": "cem analyze --globs '*.js' 'lib/components/*.js' 'lib/entrypoints/*.js' && node ./scripts/build-web-types.cjs", "build": "npm run styles && tsc", "deploy": "npm run dist && gh-pages -d dist", "dev": "npm run watch & npm run serve", "dist": "npm run build && rimraf dist && rollup -c rollup.config.cjs", "lint": "eslint src --ext .ts", "prepublishOnly": "npm run build && npm run analyze", "release": "standard-version", "serve": "web-dev-server --node-resolve --open", "size": "npm run build && size-limit", "start": "web-dev-server --app-index dist/index.html --open", "styles": "node ./scripts/build-styles.cjs", "test": "wtr src/test/*.ts --coverage", "test:update": "UPDATE_REFS=true wtr src/test/visual/*.ts", "test:visual": "wtr src/test/visual/*.ts", "watch": "tsc-watch"}, "files": ["*-color-picker.js", "*.d.ts", "*.d.ts.map", "*.js.map", "/lib", "ACKNOWLEDGMENTS", "custom-elements.json", "hex-input.js", "web-types.json", "web-types.lit.json"], "keywords": ["webcomponents", "web-components", "webcomponent", "web-component", "custom-element", "customelement", "colorpicker", "hex", "color", "color-picker", "accessible", "accessibility", "aria", "a11y", "wai-aria"], "devDependencies": {"@custom-elements-manifest/analyzer": "^0.6.4", "@esm-bundle/chai": "^4.3.4", "@open-wc/testing-helpers": "~2.1.3", "@rollup/plugin-node-resolve": "^13.3.0", "@size-limit/preset-small-lib": "^8.1.0", "@types/sinon": "^10.0.13", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "@web/dev-server": "^0.1.35", "@web/dev-server-esbuild": "^0.3.3", "@web/rollup-plugin-html": "^1.11.0", "@web/test-runner": "^0.15.0", "@web/test-runner-commands": "^0.6.5", "@web/test-runner-visual-regression": "^0.7.0", "csso": "^5.0.5", "eslint": "^8.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "gh-pages": "^4.0.0", "glob": "^8.0.3", "lint-staged": "^13.0.3", "lit-html": "^2.4.0", "prettier": "^2.7.1", "prettier-plugin-package": "^1.3.0", "rimraf": "^3.0.2", "rollup": "^2.77.2", "rollup-plugin-terser": "^7.0.2", "simple-git-hooks": "^2.8.1", "sinon": "^14.0.1", "size-limit": "^8.1.0", "standard-version": "^9.5.0", "throttle-debounce": "^5.0.0", "tsc-watch": "^5.0.3", "typescript": "^4.8.4"}, "types": "hex-color-picker.d.ts", "customElements": "custom-elements.json", "lint-staged": {"*.ts": ["eslint --fix", "prettier --write"]}, "sideEffects": ["hex-alpha-color-picker.js", "hex-color-picker.js", "hex-input.js", "hsl-color-picker.js", "hsl-string-color-picker.js", "hsla-color-picker.js", "hsla-string-color-picker.js", "hsv-color-picker.js", "hsv-string-color-picker.js", "hsva-color-picker.js", "hsva-string-color-picker.js", "rgb-color-picker.js", "rgb-string-color-picker.js", "rgba-color-picker.js", "rgba-string-color-picker.js"], "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "size-limit": [{"path": "hex-alpha-color-picker.js", "limit": "3.15 KB"}, {"path": "hex-color-picker.js", "limit": "2.8 KB"}, {"path": "hex-input.js", "limit": "1.1 KB"}, {"path": "hsl-color-picker.js", "limit": "2.5 KB"}, {"path": "hsl-string-color-picker.js", "limit": "2.7 KB"}, {"path": "hsla-color-picker.js", "limit": "2.8 KB"}, {"path": "hsla-string-color-picker.js", "limit": "2.95 KB"}, {"path": "hsv-color-picker.js", "limit": "2.5 KB"}, {"path": "hsv-string-color-picker.js", "limit": "2.7 KB"}, {"path": "hsva-color-picker.js", "limit": "2.75 KB"}, {"path": "hsva-string-color-picker.js", "limit": "2.95 KB"}, {"path": "rgb-color-picker.js", "limit": "2.6 KB"}, {"path": "rgb-string-color-picker.js", "limit": "2.8 KB"}, {"path": "rgba-color-picker.js", "limit": "2.95 KB"}, {"path": "rgba-string-color-picker.js", "limit": "3.1 KB"}], "web-types": ["web-types.json", "web-types.lit.json"]}