{"version": 3, "file": "hsva-string-color-picker.js", "sourceRoot": "", "sources": ["src/hsva-string-color-picker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,kCAAkC,CAAC;AAElE;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,OAAO,qBAAsB,SAAQ,cAAc;CAAG;AAE5D,cAAc,CAAC,MAAM,CAAC,0BAA0B,EAAE,qBAAqB,CAAC,CAAC", "sourcesContent": ["import { HsvaStringBase } from './lib/entrypoints/hsva-string.js';\n\n/**\n * A color picker custom element that uses HSVA string format.\n *\n * @element hsva-string-color-picker\n *\n * @prop {string} color - Selected color in HSVA string format.\n * @attr {string} color - Selected color in HSVA string format.\n *\n * @fires color-changed - Event fired when color property changes.\n *\n * @csspart hue - A hue selector container.\n * @csspart saturation - A saturation selector container\n * @csspart alpha - An alpha selector container.\n * @csspart hue-pointer - A hue pointer element.\n * @csspart saturation-pointer - A saturation pointer element.\n * @csspart alpha-pointer - An alpha pointer element.\n */\nexport class HsvaStringColorPicker extends HsvaStringBase {}\n\ncustomElements.define('hsva-string-color-picker', HsvaStringColorPicker);\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'hsva-string-color-picker': HsvaStringColorPicker;\n  }\n}\n"]}