{"version": 3, "file": "rgb-color-picker.js", "sourceRoot": "", "sources": ["src/rgb-color-picker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,0BAA0B,CAAC;AAGnD;;;;;;;;;;;;;GAaG;AACH,MAAM,OAAO,cAAe,SAAQ,OAAO;CAAG;AAE9C,cAAc,CAAC,MAAM,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC", "sourcesContent": ["import { RgbBase } from './lib/entrypoints/rgb.js';\nexport type { RgbColor } from './lib/types';\n\n/**\n * A color picker custom element that uses RGB object format.\n *\n * @element rgb-color-picker\n *\n * @prop {RgbColor} color - Selected color in RGB object format.\n *\n * @fires color-changed - Event fired when color property changes.\n *\n * @csspart hue - A hue selector container.\n * @csspart saturation - A saturation selector container\n * @csspart hue-pointer - A hue pointer element.\n * @csspart saturation-pointer - A saturation pointer element.\n */\nexport class RgbColorPicker extends RgbBase {}\n\ncustomElements.define('rgb-color-picker', RgbColorPicker);\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'rgb-color-picker': RgbColorPicker;\n  }\n}\n"]}