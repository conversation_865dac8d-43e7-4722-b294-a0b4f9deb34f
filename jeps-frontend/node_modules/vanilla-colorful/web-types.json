{"$schema": "https://json.schemastore.org/web-types", "name": "vanilla-colorful", "version": "0.7.2", "description-markup": "markdown", "contributions": {"html": {"elements": [{"name": "hex-alpha-color-picker", "description": "A color picker custom element that uses HEX format with alpha.", "attributes": [{"name": "color", "description": "Selected color in HEX format.", "value": {"type": ["string"]}}], "js": {"properties": [{"name": "color", "description": "Selected color in HEX format.", "value": {"type": ["string"]}}], "events": [{"name": "color-changed", "description": "Event fired when color property changes."}]}}, {"name": "hex-color-picker", "description": "A color picker custom element that uses HEX format.", "attributes": [{"name": "color", "description": "Selected color in HEX format.", "value": {"type": ["string"]}}], "js": {"properties": [{"name": "color", "description": "Selected color in HEX format.", "value": {"type": ["string"]}}], "events": [{"name": "color-changed", "description": "Event fired when color property changes."}]}}, {"name": "hex-input", "description": "A custom element for entering color in HEX format.", "attributes": [{"name": "color", "description": "Selected color in HEX format.", "value": {"type": ["string"]}}, {"name": "alpha", "description": "Allows `#rgba` and `#rrggbbaa` color formats.", "value": {"type": ["boolean"]}}, {"name": "prefixed", "description": "Enables `#` prefix displaying.", "value": {"type": ["boolean"]}}], "js": {"properties": [{"name": "color", "description": "Color in HEX format.", "value": {"type": ["string"]}}, {"name": "alpha", "description": "When true, `#rgba` and `#rrggbbaa` color formats are allowed.", "value": {"type": ["boolean"]}}, {"name": "prefixed", "description": "When true, `#` prefix is displayed in the input.", "value": {"type": ["boolean"]}}], "events": [{"name": "color-changed", "description": "Event fired when color is changed."}]}}, {"name": "hsl-color-picker", "description": "A color picker custom element that uses HSL object format.", "attributes": [], "js": {"properties": [{"name": "color", "description": "Selected color in HSL object format.", "value": {"type": ["HslColor"]}}], "events": [{"name": "color-changed", "description": "Event fired when color property changes."}]}}, {"name": "hsl-string-color-picker", "description": "A color picker custom element that uses HSL string format.", "attributes": [{"name": "color", "description": "Selected color in HSL string format.", "value": {"type": ["string"]}}], "js": {"properties": [{"name": "color", "description": "Selected color in HSL string format.", "value": {"type": ["string"]}}], "events": [{"name": "color-changed", "description": "Event fired when color property changes."}]}}, {"name": "hsla-color-picker", "description": "A color picker custom element that uses HSLA object format.", "attributes": [], "js": {"properties": [{"name": "color", "description": "Selected color in HSLA object format.", "value": {"type": ["HslaColor"]}}], "events": [{"name": "color-changed", "description": "Event fired when color property changes."}]}}, {"name": "hsla-string-color-picker", "description": "A color picker custom element that uses HSLA string format.", "attributes": [{"name": "color", "description": "Selected color in HSLA string format.", "value": {"type": ["string"]}}], "js": {"properties": [{"name": "color", "description": "Selected color in HSLA string format.", "value": {"type": ["string"]}}], "events": [{"name": "color-changed", "description": "Event fired when color property changes."}]}}, {"name": "hsv-color-picker", "description": "A color picker custom element that uses HSV object format.", "attributes": [], "js": {"properties": [{"name": "color", "description": "Selected color in HSV object format.", "value": {"type": ["HsvColor"]}}], "events": [{"name": "color-changed", "description": "Event fired when color property changes."}]}}, {"name": "hsv-string-color-picker", "description": "A color picker custom element that uses HSV string format.", "attributes": [{"name": "color", "description": "Selected color in HSV string format.", "value": {"type": ["string"]}}], "js": {"properties": [{"name": "color", "description": "Selected color in HSV string format.", "value": {"type": ["string"]}}], "events": [{"name": "color-changed", "description": "Event fired when color property changes."}]}}, {"name": "hsva-color-picker", "description": "A color picker custom element that uses HSVA object format.", "attributes": [], "js": {"properties": [{"name": "color", "description": "Selected color in HSVA object format.", "value": {"type": ["HsvaColor"]}}], "events": [{"name": "color-changed", "description": "Event fired when color property changes."}]}}, {"name": "hsva-string-color-picker", "description": "A color picker custom element that uses HSVA string format.", "attributes": [{"name": "color", "description": "Selected color in HSVA string format.", "value": {"type": ["string"]}}], "js": {"properties": [{"name": "color", "description": "Selected color in HSVA string format.", "value": {"type": ["string"]}}], "events": [{"name": "color-changed", "description": "Event fired when color property changes."}]}}, {"name": "rgb-color-picker", "description": "A color picker custom element that uses RGB object format.", "attributes": [], "js": {"properties": [{"name": "color", "description": "Selected color in RGB object format.", "value": {"type": ["RgbColor"]}}], "events": [{"name": "color-changed", "description": "Event fired when color property changes."}]}}, {"name": "rgb-string-color-picker", "description": "A color picker custom element that uses RGB string format.", "attributes": [{"name": "color", "description": "Selected color in RGB string format.", "value": {"type": ["string"]}}], "js": {"properties": [{"name": "color", "description": "Selected color in RGB string format.", "value": {"type": ["string"]}}], "events": [{"name": "color-changed", "description": "Event fired when color property changes."}]}}, {"name": "rgba-color-picker", "description": "A color picker custom element that uses RGBA object format.", "attributes": [], "js": {"properties": [{"name": "color", "description": "Selected color in RGBA object format.", "value": {"type": ["RgbaColor"]}}], "events": [{"name": "color-changed", "description": "Event fired when color property changes."}]}}, {"name": "rgba-string-color-picker", "description": "A color picker custom element that uses RGBA string format.", "attributes": [{"name": "color", "description": "Selected color in RGBA string format.", "value": {"type": ["string"]}}], "js": {"properties": [{"name": "color", "description": "Selected color in RGBA string format.", "value": {"type": ["string"]}}], "events": [{"name": "color-changed", "description": "Event fired when color property changes."}]}}]}}}