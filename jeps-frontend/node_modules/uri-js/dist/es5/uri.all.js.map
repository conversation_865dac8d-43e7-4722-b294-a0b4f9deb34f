{"version": 3, "file": "uri.all.js", "sources": ["../../src/index.ts", "../../src/schemes/urn-uuid.ts", "../../src/schemes/urn.ts", "../../src/schemes/mailto.ts", "../../src/schemes/wss.ts", "../../src/schemes/ws.ts", "../../src/schemes/https.ts", "../../src/schemes/http.ts", "../../src/uri.ts", "../../node_modules/punycode/punycode.es6.js", "../../src/regexps-iri.ts", "../../src/regexps-uri.ts", "../../src/util.ts"], "sourcesContent": ["import { SCHEMES } from \"./uri\";\n\nimport http from \"./schemes/http\";\nSCHEMES[http.scheme] = http;\n\nimport https from \"./schemes/https\";\nSCHEMES[https.scheme] = https;\n\nimport ws from \"./schemes/ws\";\nSCHEMES[ws.scheme] = ws;\n\nimport wss from \"./schemes/wss\";\nSCHEMES[wss.scheme] = wss;\n\nimport mailto from \"./schemes/mailto\";\nSCHEMES[mailto.scheme] = mailto;\n\nimport urn from \"./schemes/urn\";\nSCHEMES[urn.scheme] = urn;\n\nimport uuid from \"./schemes/urn-uuid\";\nSCHEMES[uuid.scheme] = uuid;\n\nexport * from \"./uri\";\n", "import { URISchemeHandler, URIComponents, URIOptions } from \"../uri\";\nimport { URNComponents } from \"./urn\";\nimport { SCHEMES } from \"../uri\";\n\nexport interface UUIDComponents extends URNComponents {\n\tuuid?: string;\n}\n\nconst UUID = /^[0-9A-Fa-f]{8}(?:\\-[0-9A-Fa-f]{4}){3}\\-[0-9A-Fa-f]{12}$/;\nconst UUID_PARSE = /^[0-9A-Fa-f\\-]{36}/;\n\n//RFC 4122\nconst handler:URISchemeHandler<UUIDComponents, URIOptions, URNComponents> = {\n\tscheme : \"urn:uuid\",\n\n\tparse : function (urnComponents:URNComponents, options:URIOptions):UUIDComponents {\n\t\tconst uuidComponents = urnComponents as UUIDComponents;\n\t\tuuidComponents.uuid = uuidComponents.nss;\n\t\tuuidComponents.nss = undefined;\n\n\t\tif (!options.tolerant && (!uuidComponents.uuid || !uuidComponents.uuid.match(UUID))) {\n\t\t\tuuidComponents.error = uuidComponents.error || \"UUID is not valid.\";\n\t\t}\n\n\t\treturn uuidComponents;\n\t},\n\n\tserialize : function (uuidComponents:UUIDComponents, options:URIOptions):URNComponents {\n\t\tconst urnComponents = uuidComponents as URNComponents;\n\t\t//normalize UUID\n\t\turnComponents.nss = (uuidComponents.uuid || \"\").toLowerCase();\n\t\treturn urnComponents;\n\t},\n};\n\nexport default handler;", "import { URIScheme<PERSON><PERSON><PERSON>, URICom<PERSON>, URIOptions } from \"../uri\";\nimport { pct<PERSON><PERSON><PERSON><PERSON>, SCHEMES } from \"../uri\";\n\nexport interface URNComponents extends URIComponents {\n\tnid?:string;\n\tnss?:string;\n}\n\nexport interface URNOptions extends URIOptions {\n\tnid?:string;\n}\n\nconst NID$ = \"(?:[0-9A-Za-z][0-9A-Za-z\\\\-]{1,31})\";\nconst PCT_ENCODED$ = \"(?:\\\\%[0-9A-Fa-f]{2})\";\nconst TRANS$$ = \"[0-9A-Za-z\\\\(\\\\)\\\\+\\\\,\\\\-\\\\.\\\\:\\\\=\\\\@\\\\;\\\\$\\\\_\\\\!\\\\*\\\\'\\\\/\\\\?\\\\#]\";\nconst NSS$ = \"(?:(?:\" + PCT_ENCODED$ + \"|\" + TRANS$$ + \")+)\";\nconst URN_SCHEME = new RegExp(\"^urn\\\\:(\" + NID$ + \")$\");\nconst URN_PATH = new RegExp(\"^(\" + NID$ + \")\\\\:(\" + NSS$ + \")$\");\nconst URN_PARSE = /^([^\\:]+)\\:(.*)/;\nconst URN_EXCLUDED = /[\\x00-\\x20\\\\\\\"\\&\\<\\>\\[\\]\\^\\`\\{\\|\\}\\~\\x7F-\\xFF]/g;\n\n//RFC 2141\nconst handler:URISchemeHandler<URNComponents,URNOptions> = {\n\tscheme : \"urn\",\n\n\tparse : function (components:URIComponents, options:URNOptions):URNComponents {\n\t\tconst matches = components.path && components.path.match(URN_PARSE);\n\t\tlet urnComponents = components as URNComponents;\n\n\t\tif (matches) {\n\t\t\tconst scheme = options.scheme || urnComponents.scheme || \"urn\";\n\t\t\tconst nid = matches[1].toLowerCase();\n\t\t\tconst nss = matches[2];\n\t\t\tconst urnScheme = `${scheme}:${options.nid || nid}`;\n\t\t\tconst schemeHandler = SCHEMES[urnScheme];\n\n\t\t\turnComponents.nid = nid;\n\t\t\turnComponents.nss = nss;\n\t\t\turnComponents.path = undefined;\n\n\t\t\tif (schemeHandler) {\n\t\t\t\turnComponents = schemeHandler.parse(urnComponents, options) as URNComponents;\n\t\t\t}\n\t\t} else {\n\t\t\turnComponents.error = urnComponents.error || \"URN can not be parsed.\";\n\t\t}\n\n\t\treturn urnComponents;\n\t},\n\n\tserialize : function (urnComponents:URNComponents, options:URNOptions):URIComponents {\n\t\tconst scheme = options.scheme || urnComponents.scheme || \"urn\";\n\t\tconst nid = urnComponents.nid;\n\t\tconst urnScheme = `${scheme}:${options.nid || nid}`;\n\t\tconst schemeHandler = SCHEMES[urnScheme];\n\n\t\tif (schemeHandler) {\n\t\t\turnComponents = schemeHandler.serialize(urnComponents, options) as URNComponents;\n\t\t}\n\n\t\tconst uriComponents = urnComponents as URIComponents;\n\t\tconst nss = urnComponents.nss;\n\t\turiComponents.path = `${nid || options.nid}:${nss}`;\n\n\t\treturn uriComponents;\n\t},\n};\n\nexport default handler;", "import { URI<PERSON>cheme<PERSON><PERSON><PERSON>, URIComponents, URIOptions } from \"../uri\";\nimport { pctEnc<PERSON>har, pctDec<PERSON>hars, unescapeComponent } from \"../uri\";\nimport punycode from \"punycode\";\nimport { merge, subexp, toUpperCase, toArray } from \"../util\";\n\nexport interface MailtoHeaders {\n\t[hfname:string]:string\n}\n\nexport interface MailtoComponents extends URIComponents {\n\tto:Array<string>,\n\theaders?:MailtoHeaders,\n\tsubject?:string,\n\tbody?:string\n}\n\nconst O:MailtoHeaders = {};\nconst isIRI = true;\n\n//RFC 3986\nconst UNRESERVED$$ = \"[A-Za-z0-9\\\\-\\\\.\\\\_\\\\~\" + (isIRI ? \"\\\\xA0-\\\\u200D\\\\u2010-\\\\u2029\\\\u202F-\\\\uD7FF\\\\uF900-\\\\uFDCF\\\\uFDF0-\\\\uFFEF\" : \"\") + \"]\";\nconst HEXDIG$$ = \"[0-9A-Fa-f]\";  //case-insensitive\nconst PCT_ENCODED$ = subexp(subexp(\"%[EFef]\" + HEXDIG$$ + \"%\" + HEXDIG$$ + HEXDIG$$ + \"%\" + HEXDIG$$ + HEXDIG$$) + \"|\" + subexp(\"%[89A-Fa-f]\" + HEXDIG$$ + \"%\" + HEXDIG$$ + HEXDIG$$) + \"|\" + subexp(\"%\" + HEXDIG$$ + HEXDIG$$));  //expanded\n\n//RFC 5322, except these symbols as per RFC 6068: @ : / ? # [ ] & ; =\n//const ATEXT$$ = \"[A-Za-z0-9\\\\!\\\\#\\\\$\\\\%\\\\&\\\\'\\\\*\\\\+\\\\-\\\\/\\\\=\\\\?\\\\^\\\\_\\\\`\\\\{\\\\|\\\\}\\\\~]\";\n//const WSP$$ = \"[\\\\x20\\\\x09]\";\n//const OBS_QTEXT$$ = \"[\\\\x01-\\\\x08\\\\x0B\\\\x0C\\\\x0E-\\\\x1F\\\\x7F]\";  //(%d1-8 / %d11-12 / %d14-31 / %d127)\n//const QTEXT$$ = merge(\"[\\\\x21\\\\x23-\\\\x5B\\\\x5D-\\\\x7E]\", OBS_QTEXT$$);  //%d33 / %d35-91 / %d93-126 / obs-qtext\n//const VCHAR$$ = \"[\\\\x21-\\\\x7E]\";\n//const WSP$$ = \"[\\\\x20\\\\x09]\";\n//const OBS_QP$ = subexp(\"\\\\\\\\\" + merge(\"[\\\\x00\\\\x0D\\\\x0A]\", OBS_QTEXT$$));  //%d0 / CR / LF / obs-qtext\n//const FWS$ = subexp(subexp(WSP$$ + \"*\" + \"\\\\x0D\\\\x0A\") + \"?\" + WSP$$ + \"+\");\n//const QUOTED_PAIR$ = subexp(subexp(\"\\\\\\\\\" + subexp(VCHAR$$ + \"|\" + WSP$$)) + \"|\" + OBS_QP$);\n//const QUOTED_STRING$ = subexp('\\\\\"' + subexp(FWS$ + \"?\" + QCONTENT$) + \"*\" + FWS$ + \"?\" + '\\\\\"');\nconst ATEXT$$ = \"[A-Za-z0-9\\\\!\\\\$\\\\%\\\\'\\\\*\\\\+\\\\-\\\\^\\\\_\\\\`\\\\{\\\\|\\\\}\\\\~]\";\nconst QTEXT$$ = \"[\\\\!\\\\$\\\\%\\\\'\\\\(\\\\)\\\\*\\\\+\\\\,\\\\-\\\\.0-9\\\\<\\\\>A-Z\\\\x5E-\\\\x7E]\";\nconst VCHAR$$ = merge(QTEXT$$, \"[\\\\\\\"\\\\\\\\]\");\nconst DOT_ATOM_TEXT$ = subexp(ATEXT$$ + \"+\" + subexp(\"\\\\.\" + ATEXT$$ + \"+\") + \"*\");\nconst QUOTED_PAIR$ = subexp(\"\\\\\\\\\" + VCHAR$$);\nconst QCONTENT$ = subexp(QTEXT$$ + \"|\" + QUOTED_PAIR$);\nconst QUOTED_STRING$ = subexp('\\\\\"' + QCONTENT$ + \"*\" + '\\\\\"');\n\n//RFC 6068\nconst DTEXT_NO_OBS$$ = \"[\\\\x21-\\\\x5A\\\\x5E-\\\\x7E]\";  //%d33-90 / %d94-126\nconst SOME_DELIMS$$ = \"[\\\\!\\\\$\\\\'\\\\(\\\\)\\\\*\\\\+\\\\,\\\\;\\\\:\\\\@]\";\nconst QCHAR$ = subexp(UNRESERVED$$ + \"|\" + PCT_ENCODED$ + \"|\" + SOME_DELIMS$$);\nconst DOMAIN$ = subexp(DOT_ATOM_TEXT$ + \"|\" + \"\\\\[\" + DTEXT_NO_OBS$$ + \"*\" + \"\\\\]\");\nconst LOCAL_PART$ = subexp(DOT_ATOM_TEXT$ + \"|\" + QUOTED_STRING$);\nconst ADDR_SPEC$ = subexp(LOCAL_PART$ + \"\\\\@\" + DOMAIN$);\nconst TO$ = subexp(ADDR_SPEC$ + subexp(\"\\\\,\" + ADDR_SPEC$) + \"*\");\nconst HFNAME$ = subexp(QCHAR$ + \"*\");\nconst HFVALUE$ = HFNAME$;\nconst HFIELD$ = subexp(HFNAME$ + \"\\\\=\" + HFVALUE$);\nconst HFIELDS2$ = subexp(HFIELD$ + subexp(\"\\\\&\" + HFIELD$) + \"*\");\nconst HFIELDS$ = subexp(\"\\\\?\" + HFIELDS2$);\nconst MAILTO_URI = new RegExp(\"^mailto\\\\:\" + TO$ + \"?\" + HFIELDS$ + \"?$\");\n\nconst UNRESERVED = new RegExp(UNRESERVED$$, \"g\");\nconst PCT_ENCODED = new RegExp(PCT_ENCODED$, \"g\");\nconst NOT_LOCAL_PART = new RegExp(merge(\"[^]\", ATEXT$$, \"[\\\\.]\", '[\\\\\"]', VCHAR$$), \"g\");\nconst NOT_DOMAIN = new RegExp(merge(\"[^]\", ATEXT$$, \"[\\\\.]\", \"[\\\\[]\", DTEXT_NO_OBS$$, \"[\\\\]]\"), \"g\");\nconst NOT_HFNAME = new RegExp(merge(\"[^]\", UNRESERVED$$, SOME_DELIMS$$), \"g\");\nconst NOT_HFVALUE = NOT_HFNAME;\nconst TO = new RegExp(\"^\" + TO$ + \"$\");\nconst HFIELDS = new RegExp(\"^\" + HFIELDS2$ + \"$\");\n\nfunction decodeUnreserved(str:string):string {\n\tconst decStr = pctDecChars(str);\n\treturn (!decStr.match(UNRESERVED) ? str : decStr);\n}\n\nconst handler:URISchemeHandler<MailtoComponents> =  {\n\tscheme : \"mailto\",\n\n\tparse : function (components:URIComponents, options:URIOptions):MailtoComponents {\n\t\tconst mailtoComponents = components as MailtoComponents;\n\t\tconst to = mailtoComponents.to = (mailtoComponents.path ? mailtoComponents.path.split(\",\") : []);\n\t\tmailtoComponents.path = undefined;\n\n\t\tif (mailtoComponents.query) {\n\t\t\tlet unknownHeaders = false\n\t\t\tconst headers:MailtoHeaders = {};\n\t\t\tconst hfields = mailtoComponents.query.split(\"&\");\n\n\t\t\tfor (let x = 0, xl = hfields.length; x < xl; ++x) {\n\t\t\t\tconst hfield = hfields[x].split(\"=\");\n\n\t\t\t\tswitch (hfield[0]) {\n\t\t\t\t\tcase \"to\":\n\t\t\t\t\t\tconst toAddrs = hfield[1].split(\",\");\n\t\t\t\t\t\tfor (let x = 0, xl = toAddrs.length; x < xl; ++x) {\n\t\t\t\t\t\t\tto.push(toAddrs[x]);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"subject\":\n\t\t\t\t\t\tmailtoComponents.subject = unescapeComponent(hfield[1], options);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"body\":\n\t\t\t\t\t\tmailtoComponents.body = unescapeComponent(hfield[1], options);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tunknownHeaders = true;\n\t\t\t\t\t\theaders[unescapeComponent(hfield[0], options)] = unescapeComponent(hfield[1], options);\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (unknownHeaders) mailtoComponents.headers = headers;\n\t\t}\n\n\t\tmailtoComponents.query = undefined;\n\n\t\tfor (let x = 0, xl = to.length; x < xl; ++x) {\n\t\t\tconst addr = to[x].split(\"@\");\n\n\t\t\taddr[0] = unescapeComponent(addr[0]);\n\n\t\t\tif (!options.unicodeSupport) {\n\t\t\t\t//convert Unicode IDN -> ASCII IDN\n\t\t\t\ttry {\n\t\t\t\t\taddr[1] = punycode.toASCII(unescapeComponent(addr[1], options).toLowerCase());\n\t\t\t\t} catch (e) {\n\t\t\t\t\tmailtoComponents.error = mailtoComponents.error || \"Email address's domain name can not be converted to ASCII via punycode: \" + e;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\taddr[1] = unescapeComponent(addr[1], options).toLowerCase();\n\t\t\t}\n\n\t\t\tto[x] = addr.join(\"@\");\n\t\t}\n\n\t\treturn mailtoComponents;\n\t},\n\n\tserialize : function (mailtoComponents:MailtoComponents, options:URIOptions):URIComponents {\n\t\tconst components = mailtoComponents as URIComponents;\n\t\tconst to = toArray(mailtoComponents.to);\n\t\tif (to) {\n\t\t\tfor (let x = 0, xl = to.length; x < xl; ++x) {\n\t\t\t\tconst toAddr = String(to[x]);\n\t\t\t\tconst atIdx = toAddr.lastIndexOf(\"@\");\n\t\t\t\tconst localPart = (toAddr.slice(0, atIdx)).replace(PCT_ENCODED, decodeUnreserved).replace(PCT_ENCODED, toUpperCase).replace(NOT_LOCAL_PART, pctEncChar);\n\t\t\t\tlet domain = toAddr.slice(atIdx + 1);\n\n\t\t\t\t//convert IDN via punycode\n\t\t\t\ttry {\n\t\t\t\t\tdomain = (!options.iri ? punycode.toASCII(unescapeComponent(domain, options).toLowerCase()) : punycode.toUnicode(domain));\n\t\t\t\t} catch (e) {\n\t\t\t\t\tcomponents.error = components.error || \"Email address's domain name can not be converted to \" + (!options.iri ? \"ASCII\" : \"Unicode\") + \" via punycode: \" + e;\n\t\t\t\t}\n\n\t\t\t\tto[x] = localPart + \"@\" + domain;\n\t\t\t}\n\n\t\t\tcomponents.path = to.join(\",\");\n\t\t}\n\n\t\tconst headers = mailtoComponents.headers = mailtoComponents.headers || {};\n\n\t\tif (mailtoComponents.subject) headers[\"subject\"] = mailtoComponents.subject;\n\t\tif (mailtoComponents.body) headers[\"body\"] = mailtoComponents.body;\n\n\t\tconst fields = [];\n\t\tfor (const name in headers) {\n\t\t\tif (headers[name] !== O[name]) {\n\t\t\t\tfields.push(\n\t\t\t\t\tname.replace(PCT_ENCODED, decodeUnreserved).replace(PCT_ENCODED, toUpperCase).replace(NOT_HFNAME, pctEncChar) +\n\t\t\t\t\t\"=\" +\n\t\t\t\t\theaders[name].replace(PCT_ENCODED, decodeUnreserved).replace(PCT_ENCODED, toUpperCase).replace(NOT_HFVALUE, pctEncChar)\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t\tif (fields.length) {\n\t\t\tcomponents.query = fields.join(\"&\");\n\t\t}\n\n\t\treturn components;\n\t}\n}\n\nexport default handler;", "import { URISchemeHandler, URIComponents, URIOptions } from \"../uri\";\nimport ws from \"./ws\";\n\nconst handler:URISchemeHandler = {\n\tscheme : \"wss\",\n\tdomainHost : ws.domainHost,\n\tparse : ws.parse,\n\tserialize : ws.serialize\n}\n\nexport default handler;", "import { URISchemeHand<PERSON>, URIComponents, URIOptions } from \"../uri\";\n\nexport interface WSComponents extends URIComponents {\n\tresourceName?: string;\n\tsecure?: boolean;\n}\n\nfunction isSecure(wsComponents:WSComponents):boolean {\n\treturn typeof wsComponents.secure === 'boolean' ? wsComponents.secure : String(wsComponents.scheme).toLowerCase() === \"wss\";\n}\n\n//RFC 6455\nconst handler:URISchemeHandler = {\n\tscheme : \"ws\",\n\n\tdomainHost : true,\n\n\tparse : function (components:URIComponents, options:URIOptions):WSComponents {\n\t\tconst wsComponents = components as WSComponents;\n\n\t\t//indicate if the secure flag is set\n\t\twsComponents.secure = isSecure(wsComponents);\n\n\t\t//construct resouce name\n\t\twsComponents.resourceName = (wsComponents.path || '/') + (wsComponents.query ? '?' + wsComponents.query : '');\n\t\twsComponents.path = undefined;\n\t\twsComponents.query = undefined;\n\n\t\treturn wsComponents;\n\t},\n\n\tserialize : function (wsComponents:WSComponents, options:URIOptions):URIComponents {\n\t\t//normalize the default port\n\t\tif (wsComponents.port === (isSecure(wsComponents) ? 443 : 80) || wsComponents.port === \"\") {\n\t\t\twsComponents.port = undefined;\n\t\t}\n\n\t\t//ensure scheme matches secure flag\n\t\tif (typeof wsComponents.secure === 'boolean') {\n\t\t\twsComponents.scheme = (wsComponents.secure ? 'wss' : 'ws');\n\t\t\twsComponents.secure = undefined;\n\t\t}\n\n\t\t//reconstruct path from resource name\n\t\tif (wsComponents.resourceName) {\n\t\t\tconst [path, query] = wsComponents.resourceName.split('?');\n\t\t\twsComponents.path = (path && path !== '/' ? path : undefined);\n\t\t\twsComponents.query = query;\n\t\t\twsComponents.resourceName = undefined;\n\t\t}\n\n\t\t//forbid fragment component\n\t\twsComponents.fragment = undefined;\n\n\t\treturn wsComponents;\n\t}\n};\n\nexport default handler;", "import { URISchemeHandler, URIComponents, URIOptions } from \"../uri\";\nimport http from \"./http\";\n\nconst handler:URISchemeHandler = {\n\tscheme : \"https\",\n\tdomainHost : http.domainHost,\n\tparse : http.parse,\n\tserialize : http.serialize\n}\n\nexport default handler;", "import { URISchemeHandler, URIComponents, URIOptions } from \"../uri\";\n\nconst handler:URISchemeHandler = {\n\tscheme : \"http\",\n\n\tdomainHost : true,\n\n\tparse : function (components:URIComponents, options:URIOptions):URIComponents {\n\t\t//report missing host\n\t\tif (!components.host) {\n\t\t\tcomponents.error = components.error || \"HTTP URIs must have a host.\";\n\t\t}\n\n\t\treturn components;\n\t},\n\n\tserialize : function (components:URIComponents, options:URIOptions):URIComponents {\n\t\tconst secure = String(components.scheme).toLowerCase() === \"https\";\n\n\t\t//normalize the default port\n\t\tif (components.port === (secure ? 443 : 80) || components.port === \"\") {\n\t\t\tcomponents.port = undefined;\n\t\t}\n\t\t\n\t\t//normalize the empty path\n\t\tif (!components.path) {\n\t\t\tcomponents.path = \"/\";\n\t\t}\n\n\t\t//NOTE: We do not parse query strings for HTTP URIs\n\t\t//as WWW Form Url Encoded query strings are part of the HTML4+ spec,\n\t\t//and not the HTTP spec.\n\n\t\treturn components;\n\t}\n};\n\nexport default handler;", "/**\n * URI.js\n *\n * @fileoverview An RFC 3986 compliant, scheme extendable URI parsing/validating/resolving library for JavaScript.\n * <AUTHOR> href=\"mailto:<EMAIL>\"><PERSON></a>\n * @see http://github.com/garycourt/uri-js\n */\n\n/**\n * Copyright 2011 Gary Court. All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification, are\n * permitted provided that the following conditions are met:\n *\n *    1. Redistributions of source code must retain the above copyright notice, this list of\n *       conditions and the following disclaimer.\n *\n *    2. Redistributions in binary form must reproduce the above copyright notice, this list\n *       of conditions and the following disclaimer in the documentation and/or other materials\n *       provided with the distribution.\n *\n * THIS SOFTWARE IS PROVIDED BY GARY COURT ``AS IS'' AND ANY EXPRESS OR IMPLIED\n * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND\n * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL GARY COURT OR\n * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\n * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON\n * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING\n * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF\n * ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n *\n * The views and conclusions contained in the software and documentation are those of the\n * authors and should not be interpreted as representing official policies, either expressed\n * or implied, of Gary Court.\n */\n\nimport URI_PROTOCOL from \"./regexps-uri\";\nimport IRI_PROTOCOL from \"./regexps-iri\";\nimport punycode from \"punycode\";\nimport { toUpperCase, typeOf, assign } from \"./util\";\n\nexport interface URIComponents {\n\tscheme?:string;\n\tuserinfo?:string;\n\thost?:string;\n\tport?:number|string;\n\tpath?:string;\n\tquery?:string;\n\tfragment?:string;\n\treference?:string;\n\terror?:string;\n}\n\nexport interface URIOptions {\n\tscheme?:string;\n\treference?:string;\n\ttolerant?:boolean;\n\tabsolutePath?:boolean;\n\tiri?:boolean;\n\tunicodeSupport?:boolean;\n\tdomainHost?:boolean;\n}\n\nexport interface URISchemeHandler<Components extends URIComponents = URIComponents, Options extends URIOptions = URIOptions, ParentComponents extends URIComponents = URIComponents> {\n\tscheme:string;\n\tparse(components:ParentComponents, options:Options):Components;\n\tserialize(components:Components, options:Options):ParentComponents;\n\tunicodeSupport?:boolean;\n\tdomainHost?:boolean;\n\tabsolutePath?:boolean;\n}\n\nexport interface URIRegExps {\n\tNOT_SCHEME : RegExp,\n\tNOT_USERINFO : RegExp,\n\tNOT_HOST : RegExp,\n\tNOT_PATH : RegExp,\n\tNOT_PATH_NOSCHEME : RegExp,\n\tNOT_QUERY : RegExp,\n\tNOT_FRAGMENT : RegExp,\n\tESCAPE : RegExp,\n\tUNRESERVED : RegExp,\n\tOTHER_CHARS : RegExp,\n\tPCT_ENCODED : RegExp,\n\tIPV4ADDRESS : RegExp,\n\tIPV6ADDRESS : RegExp,\n}\n\nexport const SCHEMES:{[scheme:string]:URISchemeHandler} = {};\n\nexport function pctEncChar(chr:string):string {\n\tconst c = chr.charCodeAt(0);\n\tlet e:string;\n\n\tif (c < 16) e = \"%0\" + c.toString(16).toUpperCase();\n\telse if (c < 128) e = \"%\" + c.toString(16).toUpperCase();\n\telse if (c < 2048) e = \"%\" + ((c >> 6) | 192).toString(16).toUpperCase() + \"%\" + ((c & 63) | 128).toString(16).toUpperCase();\n\telse e = \"%\" + ((c >> 12) | 224).toString(16).toUpperCase() + \"%\" + (((c >> 6) & 63) | 128).toString(16).toUpperCase() + \"%\" + ((c & 63) | 128).toString(16).toUpperCase();\n\n\treturn e;\n}\n\nexport function pctDecChars(str:string):string {\n\tlet newStr = \"\";\n\tlet i = 0;\n\tconst il = str.length;\n\n\twhile (i < il) {\n\t\tconst c = parseInt(str.substr(i + 1, 2), 16);\n\n\t\tif (c < 128) {\n\t\t\tnewStr += String.fromCharCode(c);\n\t\t\ti += 3;\n\t\t}\n\t\telse if (c >= 194 && c < 224) {\n\t\t\tif ((il - i) >= 6) {\n\t\t\t\tconst c2 = parseInt(str.substr(i + 4, 2), 16);\n\t\t\t\tnewStr += String.fromCharCode(((c & 31) << 6) | (c2 & 63));\n\t\t\t} else {\n\t\t\t\tnewStr += str.substr(i, 6);\n\t\t\t}\n\t\t\ti += 6;\n\t\t}\n\t\telse if (c >= 224) {\n\t\t\tif ((il - i) >= 9) {\n\t\t\t\tconst c2 = parseInt(str.substr(i + 4, 2), 16);\n\t\t\t\tconst c3 = parseInt(str.substr(i + 7, 2), 16);\n\t\t\t\tnewStr += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));\n\t\t\t} else {\n\t\t\t\tnewStr += str.substr(i, 9);\n\t\t\t}\n\t\t\ti += 9;\n\t\t}\n\t\telse {\n\t\t\tnewStr += str.substr(i, 3);\n\t\t\ti += 3;\n\t\t}\n\t}\n\n\treturn newStr;\n}\n\nfunction _normalizeComponentEncoding(components:URIComponents, protocol:URIRegExps) {\n\tfunction decodeUnreserved(str:string):string {\n\t\tconst decStr = pctDecChars(str);\n\t\treturn (!decStr.match(protocol.UNRESERVED) ? str : decStr);\n\t}\n\n\tif (components.scheme) components.scheme = String(components.scheme).replace(protocol.PCT_ENCODED, decodeUnreserved).toLowerCase().replace(protocol.NOT_SCHEME, \"\");\n\tif (components.userinfo !== undefined) components.userinfo = String(components.userinfo).replace(protocol.PCT_ENCODED, decodeUnreserved).replace(protocol.NOT_USERINFO, pctEncChar).replace(protocol.PCT_ENCODED, toUpperCase);\n\tif (components.host !== undefined) components.host = String(components.host).replace(protocol.PCT_ENCODED, decodeUnreserved).toLowerCase().replace(protocol.NOT_HOST, pctEncChar).replace(protocol.PCT_ENCODED, toUpperCase);\n\tif (components.path !== undefined) components.path = String(components.path).replace(protocol.PCT_ENCODED, decodeUnreserved).replace((components.scheme ? protocol.NOT_PATH : protocol.NOT_PATH_NOSCHEME), pctEncChar).replace(protocol.PCT_ENCODED, toUpperCase);\n\tif (components.query !== undefined) components.query = String(components.query).replace(protocol.PCT_ENCODED, decodeUnreserved).replace(protocol.NOT_QUERY, pctEncChar).replace(protocol.PCT_ENCODED, toUpperCase);\n\tif (components.fragment !== undefined) components.fragment = String(components.fragment).replace(protocol.PCT_ENCODED, decodeUnreserved).replace(protocol.NOT_FRAGMENT, pctEncChar).replace(protocol.PCT_ENCODED, toUpperCase);\n\n\treturn components;\n};\n\nfunction _stripLeadingZeros(str:string):string {\n\treturn str.replace(/^0*(.*)/, \"$1\") || \"0\";\n}\n\nfunction _normalizeIPv4(host:string, protocol:URIRegExps):string {\n\tconst matches = host.match(protocol.IPV4ADDRESS) || [];\n\tconst [, address] = matches;\n\t\n\tif (address) {\n\t\treturn address.split(\".\").map(_stripLeadingZeros).join(\".\");\n\t} else {\n\t\treturn host;\n\t}\n}\n\nfunction _normalizeIPv6(host:string, protocol:URIRegExps):string {\n\tconst matches = host.match(protocol.IPV6ADDRESS) || [];\n\tconst [, address, zone] = matches;\n\n\tif (address) {\n\t\tconst [last, first] = address.toLowerCase().split('::').reverse();\n\t\tconst firstFields = first ? first.split(\":\").map(_stripLeadingZeros) : [];\n\t\tconst lastFields = last.split(\":\").map(_stripLeadingZeros);\n\t\tconst isLastFieldIPv4Address = protocol.IPV4ADDRESS.test(lastFields[lastFields.length - 1]);\n\t\tconst fieldCount = isLastFieldIPv4Address ? 7 : 8;\n\t\tconst lastFieldsStart = lastFields.length - fieldCount;\n\t\tconst fields = Array<string>(fieldCount);\n\n\t\tfor (let x = 0; x < fieldCount; ++x) {\n\t\t\tfields[x] = firstFields[x] || lastFields[lastFieldsStart + x] || '';\n\t\t}\n\n\t\tif (isLastFieldIPv4Address) {\n\t\t\tfields[fieldCount - 1] = _normalizeIPv4(fields[fieldCount - 1], protocol);\n\t\t}\n\n\t\tconst allZeroFields = fields.reduce<Array<{index:number,length:number}>>((acc, field, index) => {\n\t\t\tif (!field || field === \"0\") {\n\t\t\t\tconst lastLongest = acc[acc.length - 1];\n\t\t\t\tif (lastLongest && lastLongest.index + lastLongest.length === index) {\n\t\t\t\t\tlastLongest.length++;\n\t\t\t\t} else {\n\t\t\t\t\tacc.push({ index, length : 1 });\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn acc;\n\t\t}, []);\n\n\t\tconst longestZeroFields = allZeroFields.sort((a, b) => b.length - a.length)[0];\n\n\t\tlet newHost:string;\n\t\tif (longestZeroFields && longestZeroFields.length > 1) {\n\t\t\tconst newFirst = fields.slice(0, longestZeroFields.index) ;\n\t\t\tconst newLast = fields.slice(longestZeroFields.index + longestZeroFields.length);\n\t\t\tnewHost = newFirst.join(\":\") + \"::\" + newLast.join(\":\");\n\t\t} else {\n\t\t\tnewHost = fields.join(\":\");\n\t\t}\n\n\t\tif (zone) {\n\t\t\tnewHost += \"%\" + zone;\n\t\t}\n\n\t\treturn newHost;\n\t} else {\n\t\treturn host;\n\t}\n}\n\nconst URI_PARSE = /^(?:([^:\\/?#]+):)?(?:\\/\\/((?:([^\\/?#@]*)@)?(\\[[^\\/?#\\]]+\\]|[^\\/?#:]*)(?:\\:(\\d*))?))?([^?#]*)(?:\\?([^#]*))?(?:#((?:.|\\n|\\r)*))?/i;\nconst NO_MATCH_IS_UNDEFINED = (<RegExpMatchArray>(\"\").match(/(){0}/))[1] === undefined;\n\nexport function parse(uriString:string, options:URIOptions = {}):URIComponents {\n\tconst components:URIComponents = {};\n\tconst protocol = (options.iri !== false ? IRI_PROTOCOL : URI_PROTOCOL);\n\n\tif (options.reference === \"suffix\") uriString = (options.scheme ? options.scheme + \":\" : \"\") + \"//\" + uriString;\n\n\tconst matches = uriString.match(URI_PARSE);\n\n\tif (matches) {\n\t\tif (NO_MATCH_IS_UNDEFINED) {\n\t\t\t//store each component\n\t\t\tcomponents.scheme = matches[1];\n\t\t\tcomponents.userinfo = matches[3];\n\t\t\tcomponents.host = matches[4];\n\t\t\tcomponents.port = parseInt(matches[5], 10);\n\t\t\tcomponents.path = matches[6] || \"\";\n\t\t\tcomponents.query = matches[7];\n\t\t\tcomponents.fragment = matches[8];\n\n\t\t\t//fix port number\n\t\t\tif (isNaN(components.port)) {\n\t\t\t\tcomponents.port = matches[5];\n\t\t\t}\n\t\t} else {  //IE FIX for improper RegExp matching\n\t\t\t//store each component\n\t\t\tcomponents.scheme = matches[1] || undefined;\n\t\t\tcomponents.userinfo = (uriString.indexOf(\"@\") !== -1 ? matches[3] : undefined);\n\t\t\tcomponents.host = (uriString.indexOf(\"//\") !== -1 ? matches[4] : undefined);\n\t\t\tcomponents.port = parseInt(matches[5], 10);\n\t\t\tcomponents.path = matches[6] || \"\";\n\t\t\tcomponents.query = (uriString.indexOf(\"?\") !== -1 ? matches[7] : undefined);\n\t\t\tcomponents.fragment = (uriString.indexOf(\"#\") !== -1 ? matches[8] : undefined);\n\n\t\t\t//fix port number\n\t\t\tif (isNaN(components.port)) {\n\t\t\t\tcomponents.port = (uriString.match(/\\/\\/(?:.|\\n)*\\:(?:\\/|\\?|\\#|$)/) ? matches[4] : undefined);\n\t\t\t}\n\t\t}\n\n\t\tif (components.host) {\n\t\t\t//normalize IP hosts\n\t\t\tcomponents.host = _normalizeIPv6(_normalizeIPv4(components.host, protocol), protocol);\n\t\t}\n\n\t\t//determine reference type\n\t\tif (components.scheme === undefined && components.userinfo === undefined && components.host === undefined && components.port === undefined && !components.path && components.query === undefined) {\n\t\t\tcomponents.reference = \"same-document\";\n\t\t} else if (components.scheme === undefined) {\n\t\t\tcomponents.reference = \"relative\";\n\t\t} else if (components.fragment === undefined) {\n\t\t\tcomponents.reference = \"absolute\";\n\t\t} else {\n\t\t\tcomponents.reference = \"uri\";\n\t\t}\n\n\t\t//check for reference errors\n\t\tif (options.reference && options.reference !== \"suffix\" && options.reference !== components.reference) {\n\t\t\tcomponents.error = components.error || \"URI is not a \" + options.reference + \" reference.\";\n\t\t}\n\n\t\t//find scheme handler\n\t\tconst schemeHandler = SCHEMES[(options.scheme || components.scheme || \"\").toLowerCase()];\n\n\t\t//check if scheme can't handle IRIs\n\t\tif (!options.unicodeSupport && (!schemeHandler || !schemeHandler.unicodeSupport)) {\n\t\t\t//if host component is a domain name\n\t\t\tif (components.host && (options.domainHost || (schemeHandler && schemeHandler.domainHost))) {\n\t\t\t\t//convert Unicode IDN -> ASCII IDN\n\t\t\t\ttry {\n\t\t\t\t\tcomponents.host = punycode.toASCII(components.host.replace(protocol.PCT_ENCODED, pctDecChars).toLowerCase());\n\t\t\t\t} catch (e) {\n\t\t\t\t\tcomponents.error = components.error || \"Host's domain name can not be converted to ASCII via punycode: \" + e;\n\t\t\t\t}\n\t\t\t}\n\t\t\t//convert IRI -> URI\n\t\t\t_normalizeComponentEncoding(components, URI_PROTOCOL);\n\t\t} else {\n\t\t\t//normalize encodings\n\t\t\t_normalizeComponentEncoding(components, protocol);\n\t\t}\n\n\t\t//perform scheme specific parsing\n\t\tif (schemeHandler && schemeHandler.parse) {\n\t\t\tschemeHandler.parse(components, options);\n\t\t}\n\t} else {\n\t\tcomponents.error = components.error || \"URI can not be parsed.\";\n\t}\n\n\treturn components;\n};\n\nfunction _recomposeAuthority(components:URIComponents, options:URIOptions):string|undefined {\n\tconst protocol = (options.iri !== false ? IRI_PROTOCOL : URI_PROTOCOL);\n\tconst uriTokens:Array<string> = [];\n\n\tif (components.userinfo !== undefined) {\n\t\turiTokens.push(components.userinfo);\n\t\turiTokens.push(\"@\");\n\t}\n\n\tif (components.host !== undefined) {\n\t\t//normalize IP hosts, add brackets and escape zone separator for IPv6\n\t\turiTokens.push(_normalizeIPv6(_normalizeIPv4(String(components.host), protocol), protocol).replace(protocol.IPV6ADDRESS, (_, $1, $2) => \"[\" + $1 + ($2 ? \"%25\" + $2 : \"\") + \"]\"));\n\t}\n\n\tif (typeof components.port === \"number\" || typeof components.port === \"string\") {\n\t\turiTokens.push(\":\");\n\t\turiTokens.push(String(components.port));\n\t}\n\n\treturn uriTokens.length ? uriTokens.join(\"\") : undefined;\n};\n\nconst RDS1 = /^\\.\\.?\\//;\nconst RDS2 = /^\\/\\.(\\/|$)/;\nconst RDS3 = /^\\/\\.\\.(\\/|$)/;\nconst RDS4 = /^\\.\\.?$/;\nconst RDS5 = /^\\/?(?:.|\\n)*?(?=\\/|$)/;\n\nexport function removeDotSegments(input:string):string {\n\tconst output:Array<string> = [];\n\n\twhile (input.length) {\n\t\tif (input.match(RDS1)) {\n\t\t\tinput = input.replace(RDS1, \"\");\n\t\t} else if (input.match(RDS2)) {\n\t\t\tinput = input.replace(RDS2, \"/\");\n\t\t} else if (input.match(RDS3)) {\n\t\t\tinput = input.replace(RDS3, \"/\");\n\t\t\toutput.pop();\n\t\t} else if (input === \".\" || input === \"..\") {\n\t\t\tinput = \"\";\n\t\t} else {\n\t\t\tconst im = input.match(RDS5);\n\t\t\tif (im) {\n\t\t\t\tconst s = im[0];\n\t\t\t\tinput = input.slice(s.length);\n\t\t\t\toutput.push(s);\n\t\t\t} else {\n\t\t\t\tthrow new Error(\"Unexpected dot segment condition\");\n\t\t\t}\n\t\t}\n\t}\n\n\treturn output.join(\"\");\n};\n\nexport function serialize(components:URIComponents, options:URIOptions = {}):string {\n\tconst protocol = (options.iri ? IRI_PROTOCOL : URI_PROTOCOL);\n\tconst uriTokens:Array<string> = [];\n\n\t//find scheme handler\n\tconst schemeHandler = SCHEMES[(options.scheme || components.scheme || \"\").toLowerCase()];\n\n\t//perform scheme specific serialization\n\tif (schemeHandler && schemeHandler.serialize) schemeHandler.serialize(components, options);\n\n\tif (components.host) {\n\t\t//if host component is an IPv6 address\n\t\tif (protocol.IPV6ADDRESS.test(components.host)) {\n\t\t\t//TODO: normalize IPv6 address as per RFC 5952\n\t\t}\n\n\t\t//if host component is a domain name\n\t\telse if (options.domainHost || (schemeHandler && schemeHandler.domainHost)) {\n\t\t\t//convert IDN via punycode\n\t\t\ttry {\n\t\t\t\tcomponents.host = (!options.iri ? punycode.toASCII(components.host.replace(protocol.PCT_ENCODED, pctDecChars).toLowerCase()) : punycode.toUnicode(components.host));\n\t\t\t} catch (e) {\n\t\t\t\tcomponents.error = components.error || \"Host's domain name can not be converted to \" + (!options.iri ? \"ASCII\" : \"Unicode\") + \" via punycode: \" + e;\n\t\t\t}\n\t\t}\n\t}\n\n\t//normalize encoding\n\t_normalizeComponentEncoding(components, protocol);\n\n\tif (options.reference !== \"suffix\" && components.scheme) {\n\t\turiTokens.push(components.scheme);\n\t\turiTokens.push(\":\");\n\t}\n\n\tconst authority = _recomposeAuthority(components, options);\n\tif (authority !== undefined) {\n\t\tif (options.reference !== \"suffix\") {\n\t\t\turiTokens.push(\"//\");\n\t\t}\n\n\t\turiTokens.push(authority);\n\n\t\tif (components.path && components.path.charAt(0) !== \"/\") {\n\t\t\turiTokens.push(\"/\");\n\t\t}\n\t}\n\n\tif (components.path !== undefined) {\n\t\tlet s = components.path;\n\n\t\tif (!options.absolutePath && (!schemeHandler || !schemeHandler.absolutePath)) {\n\t\t\ts = removeDotSegments(s);\n\t\t}\n\n\t\tif (authority === undefined) {\n\t\t\ts = s.replace(/^\\/\\//, \"/%2F\");  //don't allow the path to start with \"//\"\n\t\t}\n\n\t\turiTokens.push(s);\n\t}\n\n\tif (components.query !== undefined) {\n\t\turiTokens.push(\"?\");\n\t\turiTokens.push(components.query);\n\t}\n\n\tif (components.fragment !== undefined) {\n\t\turiTokens.push(\"#\");\n\t\turiTokens.push(components.fragment);\n\t}\n\n\treturn uriTokens.join(\"\");  //merge tokens into a string\n};\n\nexport function resolveComponents(base:URIComponents, relative:URIComponents, options:URIOptions = {}, skipNormalization?:boolean):URIComponents {\n\tconst target:URIComponents = {};\n\n\tif (!skipNormalization) {\n\t\tbase = parse(serialize(base, options), options);  //normalize base components\n\t\trelative = parse(serialize(relative, options), options);  //normalize relative components\n\t}\n\toptions = options || {};\n\n\tif (!options.tolerant && relative.scheme) {\n\t\ttarget.scheme = relative.scheme;\n\t\t//target.authority = relative.authority;\n\t\ttarget.userinfo = relative.userinfo;\n\t\ttarget.host = relative.host;\n\t\ttarget.port = relative.port;\n\t\ttarget.path = removeDotSegments(relative.path || \"\");\n\t\ttarget.query = relative.query;\n\t} else {\n\t\tif (relative.userinfo !== undefined || relative.host !== undefined || relative.port !== undefined) {\n\t\t\t//target.authority = relative.authority;\n\t\t\ttarget.userinfo = relative.userinfo;\n\t\t\ttarget.host = relative.host;\n\t\t\ttarget.port = relative.port;\n\t\t\ttarget.path = removeDotSegments(relative.path || \"\");\n\t\t\ttarget.query = relative.query;\n\t\t} else {\n\t\t\tif (!relative.path) {\n\t\t\t\ttarget.path = base.path;\n\t\t\t\tif (relative.query !== undefined) {\n\t\t\t\t\ttarget.query = relative.query;\n\t\t\t\t} else {\n\t\t\t\t\ttarget.query = base.query;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (relative.path.charAt(0) === \"/\") {\n\t\t\t\t\ttarget.path = removeDotSegments(relative.path);\n\t\t\t\t} else {\n\t\t\t\t\tif ((base.userinfo !== undefined || base.host !== undefined || base.port !== undefined) && !base.path) {\n\t\t\t\t\t\ttarget.path = \"/\" + relative.path;\n\t\t\t\t\t} else if (!base.path) {\n\t\t\t\t\t\ttarget.path = relative.path;\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttarget.path = base.path.slice(0, base.path.lastIndexOf(\"/\") + 1) + relative.path;\n\t\t\t\t\t}\n\t\t\t\t\ttarget.path = removeDotSegments(target.path);\n\t\t\t\t}\n\t\t\t\ttarget.query = relative.query;\n\t\t\t}\n\t\t\t//target.authority = base.authority;\n\t\t\ttarget.userinfo = base.userinfo;\n\t\t\ttarget.host = base.host;\n\t\t\ttarget.port = base.port;\n\t\t}\n\t\ttarget.scheme = base.scheme;\n\t}\n\n\ttarget.fragment = relative.fragment;\n\n\treturn target;\n};\n\nexport function resolve(baseURI:string, relativeURI:string, options?:URIOptions):string {\n\tconst schemelessOptions = assign({ scheme : 'null' }, options);\n\treturn serialize(resolveComponents(parse(baseURI, schemelessOptions), parse(relativeURI, schemelessOptions), schemelessOptions, true), schemelessOptions);\n};\n\nexport function normalize(uri:string, options?:URIOptions):string;\nexport function normalize(uri:URIComponents, options?:URIOptions):URIComponents;\nexport function normalize(uri:any, options?:URIOptions):any {\n\tif (typeof uri === \"string\") {\n\t\turi = serialize(parse(uri, options), options);\n\t} else if (typeOf(uri) === \"object\") {\n\t\turi = parse(serialize(<URIComponents>uri, options), options);\n\t}\n\n\treturn uri;\n};\n\nexport function equal(uriA:string, uriB:string, options?: URIOptions):boolean;\nexport function equal(uriA:URIComponents, uriB:URIComponents, options?:URIOptions):boolean;\nexport function equal(uriA:any, uriB:any, options?:URIOptions):boolean {\n\tif (typeof uriA === \"string\") {\n\t\turiA = serialize(parse(uriA, options), options);\n\t} else if (typeOf(uriA) === \"object\") {\n\t\turiA = serialize(<URIComponents>uriA, options);\n\t}\n\n\tif (typeof uriB === \"string\") {\n\t\turiB = serialize(parse(uriB, options), options);\n\t} else if (typeOf(uriB) === \"object\") {\n\t\turiB = serialize(<URIComponents>uriB, options);\n\t}\n\n\treturn uriA === uriB;\n};\n\nexport function escapeComponent(str:string, options?:URIOptions):string {\n\treturn str && str.toString().replace((!options || !options.iri ? URI_PROTOCOL.ESCAPE : IRI_PROTOCOL.ESCAPE), pctEncChar);\n};\n\nexport function unescapeComponent(str:string, options?:URIOptions):string {\n\treturn str && str.toString().replace((!options || !options.iri ? URI_PROTOCOL.PCT_ENCODED : IRI_PROTOCOL.PCT_ENCODED), pctDecChars);\n};\n", "'use strict';\n\n/** Highest positive signed 32-bit float value */\nconst maxInt = 2147483647; // aka. 0x7FFFFFFF or 2^31-1\n\n/** Bootstring parameters */\nconst base = 36;\nconst tMin = 1;\nconst tMax = 26;\nconst skew = 38;\nconst damp = 700;\nconst initialBias = 72;\nconst initialN = 128; // 0x80\nconst delimiter = '-'; // '\\x2D'\n\n/** Regular expressions */\nconst regexPunycode = /^xn--/;\nconst regexNonASCII = /[^\\0-\\x7E]/; // non-ASCII chars\nconst regexSeparators = /[\\x2E\\u3002\\uFF0E\\uFF61]/g; // RFC 3490 separators\n\n/** Error messages */\nconst errors = {\n\t'overflow': 'Overflow: input needs wider integers to process',\n\t'not-basic': 'Illegal input >= 0x80 (not a basic code point)',\n\t'invalid-input': 'Invalid input'\n};\n\n/** Convenience shortcuts */\nconst baseMinusTMin = base - tMin;\nconst floor = Math.floor;\nconst stringFromCharCode = String.fromCharCode;\n\n/*--------------------------------------------------------------------------*/\n\n/**\n * A generic error utility function.\n * @private\n * @param {String} type The error type.\n * @returns {Error} Throws a `RangeError` with the applicable error message.\n */\nfunction error(type) {\n\tthrow new RangeError(errors[type]);\n}\n\n/**\n * A generic `Array#map` utility function.\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} callback The function that gets called for every array\n * item.\n * @returns {Array} A new array of values returned by the callback function.\n */\nfunction map(array, fn) {\n\tconst result = [];\n\tlet length = array.length;\n\twhile (length--) {\n\t\tresult[length] = fn(array[length]);\n\t}\n\treturn result;\n}\n\n/**\n * A simple `Array#map`-like wrapper to work with domain name strings or email\n * addresses.\n * @private\n * @param {String} domain The domain name or email address.\n * @param {Function} callback The function that gets called for every\n * character.\n * @returns {Array} A new string of characters returned by the callback\n * function.\n */\nfunction mapDomain(string, fn) {\n\tconst parts = string.split('@');\n\tlet result = '';\n\tif (parts.length > 1) {\n\t\t// In email addresses, only the domain name should be punycoded. Leave\n\t\t// the local part (i.e. everything up to `@`) intact.\n\t\tresult = parts[0] + '@';\n\t\tstring = parts[1];\n\t}\n\t// Avoid `split(regex)` for IE8 compatibility. See #17.\n\tstring = string.replace(regexSeparators, '\\x2E');\n\tconst labels = string.split('.');\n\tconst encoded = map(labels, fn).join('.');\n\treturn result + encoded;\n}\n\n/**\n * Creates an array containing the numeric code points of each Unicode\n * character in the string. While JavaScript uses UCS-2 internally,\n * this function will convert a pair of surrogate halves (each of which\n * UCS-2 exposes as separate characters) into a single code point,\n * matching UTF-16.\n * @see `punycode.ucs2.encode`\n * @see <https://mathiasbynens.be/notes/javascript-encoding>\n * @memberOf punycode.ucs2\n * @name decode\n * @param {String} string The Unicode input string (UCS-2).\n * @returns {Array} The new array of code points.\n */\nfunction ucs2decode(string) {\n\tconst output = [];\n\tlet counter = 0;\n\tconst length = string.length;\n\twhile (counter < length) {\n\t\tconst value = string.charCodeAt(counter++);\n\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t// It's a high surrogate, and there is a next character.\n\t\t\tconst extra = string.charCodeAt(counter++);\n\t\t\tif ((extra & 0xFC00) == 0xDC00) { // Low surrogate.\n\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t} else {\n\t\t\t\t// It's an unmatched surrogate; only append this code unit, in case the\n\t\t\t\t// next code unit is the high surrogate of a surrogate pair.\n\t\t\t\toutput.push(value);\n\t\t\t\tcounter--;\n\t\t\t}\n\t\t} else {\n\t\t\toutput.push(value);\n\t\t}\n\t}\n\treturn output;\n}\n\n/**\n * Creates a string based on an array of numeric code points.\n * @see `punycode.ucs2.decode`\n * @memberOf punycode.ucs2\n * @name encode\n * @param {Array} codePoints The array of numeric code points.\n * @returns {String} The new Unicode string (UCS-2).\n */\nconst ucs2encode = array => String.fromCodePoint(...array);\n\n/**\n * Converts a basic code point into a digit/integer.\n * @see `digitToBasic()`\n * @private\n * @param {Number} codePoint The basic numeric code point value.\n * @returns {Number} The numeric value of a basic code point (for use in\n * representing integers) in the range `0` to `base - 1`, or `base` if\n * the code point does not represent a value.\n */\nconst basicToDigit = function(codePoint) {\n\tif (codePoint - 0x30 < 0x0A) {\n\t\treturn codePoint - 0x16;\n\t}\n\tif (codePoint - 0x41 < 0x1A) {\n\t\treturn codePoint - 0x41;\n\t}\n\tif (codePoint - 0x61 < 0x1A) {\n\t\treturn codePoint - 0x61;\n\t}\n\treturn base;\n};\n\n/**\n * Converts a digit/integer into a basic code point.\n * @see `basicToDigit()`\n * @private\n * @param {Number} digit The numeric value of a basic code point.\n * @returns {Number} The basic code point whose value (when used for\n * representing integers) is `digit`, which needs to be in the range\n * `0` to `base - 1`. If `flag` is non-zero, the uppercase form is\n * used; else, the lowercase form is used. The behavior is undefined\n * if `flag` is non-zero and `digit` has no uppercase form.\n */\nconst digitToBasic = function(digit, flag) {\n\t//  0..25 map to ASCII a..z or A..Z\n\t// 26..35 map to ASCII 0..9\n\treturn digit + 22 + 75 * (digit < 26) - ((flag != 0) << 5);\n};\n\n/**\n * Bias adaptation function as per section 3.4 of RFC 3492.\n * https://tools.ietf.org/html/rfc3492#section-3.4\n * @private\n */\nconst adapt = function(delta, numPoints, firstTime) {\n\tlet k = 0;\n\tdelta = firstTime ? floor(delta / damp) : delta >> 1;\n\tdelta += floor(delta / numPoints);\n\tfor (/* no initialization */; delta > baseMinusTMin * tMax >> 1; k += base) {\n\t\tdelta = floor(delta / baseMinusTMin);\n\t}\n\treturn floor(k + (baseMinusTMin + 1) * delta / (delta + skew));\n};\n\n/**\n * Converts a Punycode string of ASCII-only symbols to a string of Unicode\n * symbols.\n * @memberOf punycode\n * @param {String} input The Punycode string of ASCII-only symbols.\n * @returns {String} The resulting string of Unicode symbols.\n */\nconst decode = function(input) {\n\t// Don't use UCS-2.\n\tconst output = [];\n\tconst inputLength = input.length;\n\tlet i = 0;\n\tlet n = initialN;\n\tlet bias = initialBias;\n\n\t// Handle the basic code points: let `basic` be the number of input code\n\t// points before the last delimiter, or `0` if there is none, then copy\n\t// the first basic code points to the output.\n\n\tlet basic = input.lastIndexOf(delimiter);\n\tif (basic < 0) {\n\t\tbasic = 0;\n\t}\n\n\tfor (let j = 0; j < basic; ++j) {\n\t\t// if it's not a basic code point\n\t\tif (input.charCodeAt(j) >= 0x80) {\n\t\t\terror('not-basic');\n\t\t}\n\t\toutput.push(input.charCodeAt(j));\n\t}\n\n\t// Main decoding loop: start just after the last delimiter if any basic code\n\t// points were copied; start at the beginning otherwise.\n\n\tfor (let index = basic > 0 ? basic + 1 : 0; index < inputLength; /* no final expression */) {\n\n\t\t// `index` is the index of the next character to be consumed.\n\t\t// Decode a generalized variable-length integer into `delta`,\n\t\t// which gets added to `i`. The overflow checking is easier\n\t\t// if we increase `i` as we go, then subtract off its starting\n\t\t// value at the end to obtain `delta`.\n\t\tlet oldi = i;\n\t\tfor (let w = 1, k = base; /* no condition */; k += base) {\n\n\t\t\tif (index >= inputLength) {\n\t\t\t\terror('invalid-input');\n\t\t\t}\n\n\t\t\tconst digit = basicToDigit(input.charCodeAt(index++));\n\n\t\t\tif (digit >= base || digit > floor((maxInt - i) / w)) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\n\t\t\ti += digit * w;\n\t\t\tconst t = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);\n\n\t\t\tif (digit < t) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tconst baseMinusT = base - t;\n\t\t\tif (w > floor(maxInt / baseMinusT)) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\n\t\t\tw *= baseMinusT;\n\n\t\t}\n\n\t\tconst out = output.length + 1;\n\t\tbias = adapt(i - oldi, out, oldi == 0);\n\n\t\t// `i` was supposed to wrap around from `out` to `0`,\n\t\t// incrementing `n` each time, so we'll fix that now:\n\t\tif (floor(i / out) > maxInt - n) {\n\t\t\terror('overflow');\n\t\t}\n\n\t\tn += floor(i / out);\n\t\ti %= out;\n\n\t\t// Insert `n` at position `i` of the output.\n\t\toutput.splice(i++, 0, n);\n\n\t}\n\n\treturn String.fromCodePoint(...output);\n};\n\n/**\n * Converts a string of Unicode symbols (e.g. a domain name label) to a\n * Punycode string of ASCII-only symbols.\n * @memberOf punycode\n * @param {String} input The string of Unicode symbols.\n * @returns {String} The resulting Punycode string of ASCII-only symbols.\n */\nconst encode = function(input) {\n\tconst output = [];\n\n\t// Convert the input in UCS-2 to an array of Unicode code points.\n\tinput = ucs2decode(input);\n\n\t// Cache the length.\n\tlet inputLength = input.length;\n\n\t// Initialize the state.\n\tlet n = initialN;\n\tlet delta = 0;\n\tlet bias = initialBias;\n\n\t// Handle the basic code points.\n\tfor (const currentValue of input) {\n\t\tif (currentValue < 0x80) {\n\t\t\toutput.push(stringFromCharCode(currentValue));\n\t\t}\n\t}\n\n\tlet basicLength = output.length;\n\tlet handledCPCount = basicLength;\n\n\t// `handledCPCount` is the number of code points that have been handled;\n\t// `basicLength` is the number of basic code points.\n\n\t// Finish the basic string with a delimiter unless it's empty.\n\tif (basicLength) {\n\t\toutput.push(delimiter);\n\t}\n\n\t// Main encoding loop:\n\twhile (handledCPCount < inputLength) {\n\n\t\t// All non-basic code points < n have been handled already. Find the next\n\t\t// larger one:\n\t\tlet m = maxInt;\n\t\tfor (const currentValue of input) {\n\t\t\tif (currentValue >= n && currentValue < m) {\n\t\t\t\tm = currentValue;\n\t\t\t}\n\t\t}\n\n\t\t// Increase `delta` enough to advance the decoder's <n,i> state to <m,0>,\n\t\t// but guard against overflow.\n\t\tconst handledCPCountPlusOne = handledCPCount + 1;\n\t\tif (m - n > floor((maxInt - delta) / handledCPCountPlusOne)) {\n\t\t\terror('overflow');\n\t\t}\n\n\t\tdelta += (m - n) * handledCPCountPlusOne;\n\t\tn = m;\n\n\t\tfor (const currentValue of input) {\n\t\t\tif (currentValue < n && ++delta > maxInt) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\t\t\tif (currentValue == n) {\n\t\t\t\t// Represent delta as a generalized variable-length integer.\n\t\t\t\tlet q = delta;\n\t\t\t\tfor (let k = base; /* no condition */; k += base) {\n\t\t\t\t\tconst t = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);\n\t\t\t\t\tif (q < t) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tconst qMinusT = q - t;\n\t\t\t\t\tconst baseMinusT = base - t;\n\t\t\t\t\toutput.push(\n\t\t\t\t\t\tstringFromCharCode(digitToBasic(t + qMinusT % baseMinusT, 0))\n\t\t\t\t\t);\n\t\t\t\t\tq = floor(qMinusT / baseMinusT);\n\t\t\t\t}\n\n\t\t\t\toutput.push(stringFromCharCode(digitToBasic(q, 0)));\n\t\t\t\tbias = adapt(delta, handledCPCountPlusOne, handledCPCount == basicLength);\n\t\t\t\tdelta = 0;\n\t\t\t\t++handledCPCount;\n\t\t\t}\n\t\t}\n\n\t\t++delta;\n\t\t++n;\n\n\t}\n\treturn output.join('');\n};\n\n/**\n * Converts a Punycode string representing a domain name or an email address\n * to Unicode. Only the Punycoded parts of the input will be converted, i.e.\n * it doesn't matter if you call it on a string that has already been\n * converted to Unicode.\n * @memberOf punycode\n * @param {String} input The Punycoded domain name or email address to\n * convert to Unicode.\n * @returns {String} The Unicode representation of the given Punycode\n * string.\n */\nconst toUnicode = function(input) {\n\treturn mapDomain(input, function(string) {\n\t\treturn regexPunycode.test(string)\n\t\t\t? decode(string.slice(4).toLowerCase())\n\t\t\t: string;\n\t});\n};\n\n/**\n * Converts a Unicode string representing a domain name or an email address to\n * Punycode. Only the non-ASCII parts of the domain name will be converted,\n * i.e. it doesn't matter if you call it with a domain that's already in\n * ASCII.\n * @memberOf punycode\n * @param {String} input The domain name or email address to convert, as a\n * Unicode string.\n * @returns {String} The Punycode representation of the given domain name or\n * email address.\n */\nconst toASCII = function(input) {\n\treturn mapDomain(input, function(string) {\n\t\treturn regexNonASCII.test(string)\n\t\t\t? 'xn--' + encode(string)\n\t\t\t: string;\n\t});\n};\n\n/*--------------------------------------------------------------------------*/\n\n/** Define the public API */\nconst punycode = {\n\t/**\n\t * A string representing the current Punycode.js version number.\n\t * @memberOf punycode\n\t * @type String\n\t */\n\t'version': '2.1.0',\n\t/**\n\t * An object of methods to convert from JavaScript's internal character\n\t * representation (UCS-2) to Unicode code points, and back.\n\t * @see <https://mathiasbynens.be/notes/javascript-encoding>\n\t * @memberOf punycode\n\t * @type Object\n\t */\n\t'ucs2': {\n\t\t'decode': ucs2decode,\n\t\t'encode': ucs2encode\n\t},\n\t'decode': decode,\n\t'encode': encode,\n\t'toASCII': toASCII,\n\t'toUnicode': toUnicode\n};\n\nexport default punycode;\n", "import { URIRegExps } from \"./uri\";\nimport { buildExps } from \"./regexps-uri\";\n\nexport default buildExps(true);\n", "import { URIRegExps } from \"./uri\";\nimport { merge, subexp } from \"./util\";\n\nexport function buildExps(isIRI:boolean):URIRegExps {\n\tconst\n\t\tALPHA$$ = \"[A-Za-z]\",\n\t\tCR$ = \"[\\\\x0D]\",\n\t\tDIGIT$$ = \"[0-9]\",\n\t\tDQUOTE$$ = \"[\\\\x22]\",\n\t\tHEXDIG$$ = merge(DIGIT$$, \"[A-Fa-f]\"),  //case-insensitive\n\t\tLF$$ = \"[\\\\x0A]\",\n\t\tSP$$ = \"[\\\\x20]\",\n\t\tPCT_ENCODED$ = subexp(subexp(\"%[EFef]\" + HEXDIG$$ + \"%\" + HEXDIG$$ + HEXDIG$$ + \"%\" + HEXDIG$$ + HEXDIG$$) + \"|\" + subexp(\"%[89A-Fa-f]\" + HEXDIG$$ + \"%\" + HEXDIG$$ + HEXDIG$$) + \"|\" + subexp(\"%\" + HEXDIG$$ + HEXDIG$$)),  //expanded\n\t\tGEN_DELIMS$$ = \"[\\\\:\\\\/\\\\?\\\\#\\\\[\\\\]\\\\@]\",\n\t\tSUB_DELIMS$$ = \"[\\\\!\\\\$\\\\&\\\\'\\\\(\\\\)\\\\*\\\\+\\\\,\\\\;\\\\=]\",\n\t\tRESERVED$$ = merge(GEN_DELIMS$$, SUB_DELIMS$$),\n\t\tUCSCHAR$$ = isIRI ? \"[\\\\xA0-\\\\u200D\\\\u2010-\\\\u2029\\\\u202F-\\\\uD7FF\\\\uF900-\\\\uFDCF\\\\uFDF0-\\\\uFFEF]\" : \"[]\",  //subset, excludes bidi control characters\n\t\tIPRIVATE$$ = isIRI ? \"[\\\\uE000-\\\\uF8FF]\" : \"[]\",  //subset\n\t\tUNRESERVED$$ = merge(ALPHA$$, DIGIT$$, \"[\\\\-\\\\.\\\\_\\\\~]\", UCSCHAR$$),\n\t\tSCHEME$ = subexp(ALPHA$$ + merge(ALPHA$$, DIGIT$$, \"[\\\\+\\\\-\\\\.]\") + \"*\"),\n\t\tUSERINFO$ = subexp(subexp(PCT_ENCODED$ + \"|\" + merge(UNRESERVED$$, SUB_DELIMS$$, \"[\\\\:]\")) + \"*\"),\n\t\tDEC_OCTET$ = subexp(subexp(\"25[0-5]\") + \"|\" + subexp(\"2[0-4]\" + DIGIT$$) + \"|\" + subexp(\"1\" + DIGIT$$ + DIGIT$$) + \"|\" + subexp(\"[1-9]\" + DIGIT$$) + \"|\" + DIGIT$$),\n\t\tDEC_OCTET_RELAXED$ = subexp(subexp(\"25[0-5]\") + \"|\" + subexp(\"2[0-4]\" + DIGIT$$) + \"|\" + subexp(\"1\" + DIGIT$$ + DIGIT$$) + \"|\" + subexp(\"0?[1-9]\" + DIGIT$$) + \"|0?0?\" + DIGIT$$),  //relaxed parsing rules\n\t\tIPV4ADDRESS$ = subexp(DEC_OCTET_RELAXED$ + \"\\\\.\" + DEC_OCTET_RELAXED$ + \"\\\\.\" + DEC_OCTET_RELAXED$ + \"\\\\.\" + DEC_OCTET_RELAXED$),\n\t\tH16$ = subexp(HEXDIG$$ + \"{1,4}\"),\n\t\tLS32$ = subexp(subexp(H16$ + \"\\\\:\" + H16$) + \"|\" + IPV4ADDRESS$),\n\t\tIPV6ADDRESS1$ = subexp(                                                            subexp(H16$ + \"\\\\:\") + \"{6}\" + LS32$), //                           6( h16 \":\" ) ls32\n\t\tIPV6ADDRESS2$ = subexp(                                                 \"\\\\:\\\\:\" + subexp(H16$ + \"\\\\:\") + \"{5}\" + LS32$), //                      \"::\" 5( h16 \":\" ) ls32\n\t\tIPV6ADDRESS3$ = subexp(subexp(                                 H16$) + \"?\\\\:\\\\:\" + subexp(H16$ + \"\\\\:\") + \"{4}\" + LS32$), //[               h16 ] \"::\" 4( h16 \":\" ) ls32\n\t\tIPV6ADDRESS4$ = subexp(subexp(subexp(H16$ + \"\\\\:\") + \"{0,1}\" + H16$) + \"?\\\\:\\\\:\" + subexp(H16$ + \"\\\\:\") + \"{3}\" + LS32$), //[ *1( h16 \":\" ) h16 ] \"::\" 3( h16 \":\" ) ls32\n\t\tIPV6ADDRESS5$ = subexp(subexp(subexp(H16$ + \"\\\\:\") + \"{0,2}\" + H16$) + \"?\\\\:\\\\:\" + subexp(H16$ + \"\\\\:\") + \"{2}\" + LS32$), //[ *2( h16 \":\" ) h16 ] \"::\" 2( h16 \":\" ) ls32\n\t\tIPV6ADDRESS6$ = subexp(subexp(subexp(H16$ + \"\\\\:\") + \"{0,3}\" + H16$) + \"?\\\\:\\\\:\" +        H16$ + \"\\\\:\"          + LS32$), //[ *3( h16 \":\" ) h16 ] \"::\"    h16 \":\"   ls32\n\t\tIPV6ADDRESS7$ = subexp(subexp(subexp(H16$ + \"\\\\:\") + \"{0,4}\" + H16$) + \"?\\\\:\\\\:\"                                + LS32$), //[ *4( h16 \":\" ) h16 ] \"::\"              ls32\n\t\tIPV6ADDRESS8$ = subexp(subexp(subexp(H16$ + \"\\\\:\") + \"{0,5}\" + H16$) + \"?\\\\:\\\\:\"                                + H16$ ), //[ *5( h16 \":\" ) h16 ] \"::\"              h16\n\t\tIPV6ADDRESS9$ = subexp(subexp(subexp(H16$ + \"\\\\:\") + \"{0,6}\" + H16$) + \"?\\\\:\\\\:\"                                       ), //[ *6( h16 \":\" ) h16 ] \"::\"\n\t\tIPV6ADDRESS$ = subexp([IPV6ADDRESS1$, IPV6ADDRESS2$, IPV6ADDRESS3$, IPV6ADDRESS4$, IPV6ADDRESS5$, IPV6ADDRESS6$, IPV6ADDRESS7$, IPV6ADDRESS8$, IPV6ADDRESS9$].join(\"|\")),\n\t\tZONEID$ = subexp(subexp(UNRESERVED$$ + \"|\" + PCT_ENCODED$) + \"+\"),  //RFC 6874\n\t\tIPV6ADDRZ$ = subexp(IPV6ADDRESS$ + \"\\\\%25\" + ZONEID$),  //RFC 6874\n\t\tIPV6ADDRZ_RELAXED$ = subexp(IPV6ADDRESS$ + subexp(\"\\\\%25|\\\\%(?!\" + HEXDIG$$ + \"{2})\") + ZONEID$),  //RFC 6874, with relaxed parsing rules\n\t\tIPVFUTURE$ = subexp(\"[vV]\" + HEXDIG$$ + \"+\\\\.\" + merge(UNRESERVED$$, SUB_DELIMS$$, \"[\\\\:]\") + \"+\"),\n\t\tIP_LITERAL$ = subexp(\"\\\\[\" + subexp(IPV6ADDRZ_RELAXED$ + \"|\" + IPV6ADDRESS$ + \"|\" + IPVFUTURE$) + \"\\\\]\"),  //RFC 6874\n\t\tREG_NAME$ = subexp(subexp(PCT_ENCODED$ + \"|\" + merge(UNRESERVED$$, SUB_DELIMS$$)) + \"*\"),\n\t\tHOST$ = subexp(IP_LITERAL$ + \"|\" + IPV4ADDRESS$ + \"(?!\" + REG_NAME$ + \")\" + \"|\" + REG_NAME$),\n\t\tPORT$ = subexp(DIGIT$$ + \"*\"),\n\t\tAUTHORITY$ = subexp(subexp(USERINFO$ + \"@\") + \"?\" + HOST$ + subexp(\"\\\\:\" + PORT$) + \"?\"),\n\t\tPCHAR$ = subexp(PCT_ENCODED$ + \"|\" + merge(UNRESERVED$$, SUB_DELIMS$$, \"[\\\\:\\\\@]\")),\n\t\tSEGMENT$ = subexp(PCHAR$ + \"*\"),\n\t\tSEGMENT_NZ$ = subexp(PCHAR$ + \"+\"),\n\t\tSEGMENT_NZ_NC$ = subexp(subexp(PCT_ENCODED$ + \"|\" + merge(UNRESERVED$$, SUB_DELIMS$$, \"[\\\\@]\")) + \"+\"),\n\t\tPATH_ABEMPTY$ = subexp(subexp(\"\\\\/\" + SEGMENT$) + \"*\"),\n\t\tPATH_ABSOLUTE$ = subexp(\"\\\\/\" + subexp(SEGMENT_NZ$ + PATH_ABEMPTY$) + \"?\"),  //simplified\n\t\tPATH_NOSCHEME$ = subexp(SEGMENT_NZ_NC$ + PATH_ABEMPTY$),  //simplified\n\t\tPATH_ROOTLESS$ = subexp(SEGMENT_NZ$ + PATH_ABEMPTY$),  //simplified\n\t\tPATH_EMPTY$ = \"(?!\" + PCHAR$ + \")\",\n\t\tPATH$ = subexp(PATH_ABEMPTY$ + \"|\" + PATH_ABSOLUTE$ + \"|\" + PATH_NOSCHEME$ + \"|\" + PATH_ROOTLESS$ + \"|\" + PATH_EMPTY$),\n\t\tQUERY$ = subexp(subexp(PCHAR$ + \"|\" + merge(\"[\\\\/\\\\?]\", IPRIVATE$$)) + \"*\"),\n\t\tFRAGMENT$ = subexp(subexp(PCHAR$ + \"|[\\\\/\\\\?]\") + \"*\"),\n\t\tHIER_PART$ = subexp(subexp(\"\\\\/\\\\/\" + AUTHORITY$ + PATH_ABEMPTY$) + \"|\" + PATH_ABSOLUTE$ + \"|\" + PATH_ROOTLESS$ + \"|\" + PATH_EMPTY$),\n\t\tURI$ = subexp(SCHEME$ + \"\\\\:\" + HIER_PART$ + subexp(\"\\\\?\" + QUERY$) + \"?\" + subexp(\"\\\\#\" + FRAGMENT$) + \"?\"),\n\t\tRELATIVE_PART$ = subexp(subexp(\"\\\\/\\\\/\" + AUTHORITY$ + PATH_ABEMPTY$) + \"|\" + PATH_ABSOLUTE$ + \"|\" + PATH_NOSCHEME$ + \"|\" + PATH_EMPTY$),\n\t\tRELATIVE$ = subexp(RELATIVE_PART$ + subexp(\"\\\\?\" + QUERY$) + \"?\" + subexp(\"\\\\#\" + FRAGMENT$) + \"?\"),\n\t\tURI_REFERENCE$ = subexp(URI$ + \"|\" + RELATIVE$),\n\t\tABSOLUTE_URI$ = subexp(SCHEME$ + \"\\\\:\" + HIER_PART$ + subexp(\"\\\\?\" + QUERY$) + \"?\"),\n\n\t\tGENERIC_REF$ = \"^(\" + SCHEME$ + \")\\\\:\" + subexp(subexp(\"\\\\/\\\\/(\" + subexp(\"(\" + USERINFO$ + \")@\") + \"?(\" + HOST$ + \")\" + subexp(\"\\\\:(\" + PORT$ + \")\") + \"?)\") + \"?(\" + PATH_ABEMPTY$ + \"|\" + PATH_ABSOLUTE$ + \"|\" + PATH_ROOTLESS$ + \"|\" + PATH_EMPTY$ + \")\") + subexp(\"\\\\?(\" + QUERY$ + \")\") + \"?\" + subexp(\"\\\\#(\" + FRAGMENT$ + \")\") + \"?$\",\n\t\tRELATIVE_REF$ = \"^(){0}\" + subexp(subexp(\"\\\\/\\\\/(\" + subexp(\"(\" + USERINFO$ + \")@\") + \"?(\" + HOST$ + \")\" + subexp(\"\\\\:(\" + PORT$ + \")\") + \"?)\") + \"?(\" + PATH_ABEMPTY$ + \"|\" + PATH_ABSOLUTE$ + \"|\" + PATH_NOSCHEME$ + \"|\" + PATH_EMPTY$ + \")\") + subexp(\"\\\\?(\" + QUERY$ + \")\") + \"?\" + subexp(\"\\\\#(\" + FRAGMENT$ + \")\") + \"?$\",\n\t\tABSOLUTE_REF$ = \"^(\" + SCHEME$ + \")\\\\:\" + subexp(subexp(\"\\\\/\\\\/(\" + subexp(\"(\" + USERINFO$ + \")@\") + \"?(\" + HOST$ + \")\" + subexp(\"\\\\:(\" + PORT$ + \")\") + \"?)\") + \"?(\" + PATH_ABEMPTY$ + \"|\" + PATH_ABSOLUTE$ + \"|\" + PATH_ROOTLESS$ + \"|\" + PATH_EMPTY$ + \")\") + subexp(\"\\\\?(\" + QUERY$ + \")\") + \"?$\",\n\t\tSAMEDOC_REF$ = \"^\" + subexp(\"\\\\#(\" + FRAGMENT$ + \")\") + \"?$\",\n\t\tAUTHORITY_REF$ = \"^\" + subexp(\"(\" + USERINFO$ + \")@\") + \"?(\" + HOST$ + \")\" + subexp(\"\\\\:(\" + PORT$ + \")\") + \"?$\"\n\t;\n\n\treturn {\n\t\tNOT_SCHEME : new RegExp(merge(\"[^]\", ALPHA$$, DIGIT$$, \"[\\\\+\\\\-\\\\.]\"), \"g\"),\n\t\tNOT_USERINFO : new RegExp(merge(\"[^\\\\%\\\\:]\", UNRESERVED$$, SUB_DELIMS$$), \"g\"),\n\t\tNOT_HOST : new RegExp(merge(\"[^\\\\%\\\\[\\\\]\\\\:]\", UNRESERVED$$, SUB_DELIMS$$), \"g\"),\n\t\tNOT_PATH : new RegExp(merge(\"[^\\\\%\\\\/\\\\:\\\\@]\", UNRESERVED$$, SUB_DELIMS$$), \"g\"),\n\t\tNOT_PATH_NOSCHEME : new RegExp(merge(\"[^\\\\%\\\\/\\\\@]\", UNRESERVED$$, SUB_DELIMS$$), \"g\"),\n\t\tNOT_QUERY : new RegExp(merge(\"[^\\\\%]\", UNRESERVED$$, SUB_DELIMS$$, \"[\\\\:\\\\@\\\\/\\\\?]\", IPRIVATE$$), \"g\"),\n\t\tNOT_FRAGMENT : new RegExp(merge(\"[^\\\\%]\", UNRESERVED$$, SUB_DELIMS$$, \"[\\\\:\\\\@\\\\/\\\\?]\"), \"g\"),\n\t\tESCAPE : new RegExp(merge(\"[^]\", UNRESERVED$$, SUB_DELIMS$$), \"g\"),\n\t\tUNRESERVED : new RegExp(UNRESERVED$$, \"g\"),\n\t\tOTHER_CHARS : new RegExp(merge(\"[^\\\\%]\", UNRESERVED$$, RESERVED$$), \"g\"),\n\t\tPCT_ENCODED : new RegExp(PCT_ENCODED$, \"g\"),\n\t\tIPV4ADDRESS : new RegExp(\"^(\" + IPV4ADDRESS$ + \")$\"),\n\t\tIPV6ADDRESS : new RegExp(\"^\\\\[?(\" + IPV6ADDRESS$ + \")\" + subexp(subexp(\"\\\\%25|\\\\%(?!\" + HEXDIG$$ + \"{2})\") + \"(\" + ZONEID$ + \")\") + \"?\\\\]?$\")  //RFC 6874, with relaxed parsing rules\n\t};\n}\n\nexport default buildExps(false);\n", "export function merge(...sets:Array<string>):string {\n\tif (sets.length > 1) {\n\t\tsets[0] = sets[0].slice(0, -1);\n\t\tconst xl = sets.length - 1;\n\t\tfor (let x = 1; x < xl; ++x) {\n\t\t\tsets[x] = sets[x].slice(1, -1);\n\t\t}\n\t\tsets[xl] = sets[xl].slice(1);\n\t\treturn sets.join('');\n\t} else {\n\t\treturn sets[0];\n\t}\n}\n\nexport function subexp(str:string):string {\n\treturn \"(?:\" + str + \")\";\n}\n\nexport function typeOf(o:any):string {\n\treturn o === undefined ? \"undefined\" : (o === null ? \"null\" : Object.prototype.toString.call(o).split(\" \").pop().split(\"]\").shift().toLowerCase());\n}\n\nexport function toUpperCase(str:string):string {\n\treturn str.toUpperCase();\n}\n\nexport function toArray(obj:any):Array<any> {\n\treturn obj !== undefined && obj !== null ? (obj instanceof Array ? obj : (typeof obj.length !== \"number\" || obj.split || obj.setInterval || obj.call ? [obj] : Array.prototype.slice.call(obj))) : [];\n}\n\n\nexport function assign(target: object, source: any): any {\n\tconst obj = target as any;\n\tif (source) {\n\t\tfor (const key in source) {\n\t\t\tobj[key] = source[key];\n\t\t}\n\t}\n\treturn obj;\n}"], "names": ["SCHEMES", "uuid", "scheme", "urn", "mailto", "wss", "ws", "https", "http", "urnComponents", "nss", "uuidComponents", "toLowerCase", "options", "error", "tolerant", "match", "UUID", "undefined", "handler", "uriComponents", "path", "nid", "<PERSON><PERSON><PERSON><PERSON>", "serialize", "urnScheme", "parse", "matches", "components", "URN_PARSE", "query", "fields", "join", "length", "push", "name", "replace", "PCT_ENCODED", "decodeUnreserved", "toUpperCase", "NOT_HFNAME", "pctEncChar", "headers", "NOT_HFVALUE", "O", "mailtoComponents", "body", "subject", "to", "x", "localPart", "domain", "iri", "e", "punycode", "toASCII", "unescapeComponent", "toUnicode", "toAddr", "slice", "atIdx", "NOT_LOCAL_PART", "lastIndexOf", "String", "xl", "toArray", "addr", "unicodeSupport", "split", "unknownHeaders", "hfield", "toAddrs", "hfields", "decStr", "UNRESERVED", "str", "pctDecChars", "RegExp", "merge", "UNRESERVED$$", "SOME_DELIMS$$", "ATEXT$$", "VCHAR$$", "PCT_ENCODED$", "QTEXT$$", "subexp", "HEXDIG$$", "isIRI", "domainHost", "wsComponents", "fragment", "resourceName", "secure", "port", "isSecure", "host", "toString", "URI_PROTOCOL", "IRI_PROTOCOL", "ESCAPE", "escapeComponent", "uriA", "uriB", "typeOf", "equal", "uri", "normalize", "resolveComponents", "baseURI", "schemelessOptions", "relativeURI", "assign", "resolve", "target", "relative", "base", "userinfo", "removeDotSegments", "char<PERSON>t", "skipNormalization", "uri<PERSON><PERSON>s", "s", "authority", "absolutePath", "reference", "_recomposeAuthority", "protocol", "IPV6ADDRESS", "test", "output", "Error", "input", "im", "RDS5", "pop", "RDS3", "RDS2", "RDS1", "$1", "$2", "_normalizeIPv6", "_normalizeIPv4", "_", "uriString", "isNaN", "indexOf", "parseInt", "NO_MATCH_IS_UNDEFINED", "URI_PARSE", "newHost", "zone", "newFirst", "newLast", "longestZeroFields", "index", "b", "a", "allZeroFields", "sort", "acc", "lastLongest", "field", "reduce", "fieldCount", "isLastFieldIPv4Address", "firstFields", "lastFields", "lastFieldsStart", "Array", "IPV4ADDRESS", "last", "map", "_stripLeadingZeros", "first", "address", "reverse", "NOT_FRAGMENT", "NOT_QUERY", "NOT_PATH", "NOT_PATH_NOSCHEME", "NOT_HOST", "NOT_USERINFO", "NOT_SCHEME", "_normalizeComponentEncoding", "newStr", "substr", "i", "fromCharCode", "c", "c2", "c3", "il", "chr", "charCodeAt", "encode", "decode", "ucs2encode", "ucs2decode", "regexNonASCII", "string", "mapDomain", "regexPunycode", "n", "delta", "handledCPCount", "adapt", "handledCPCountPlusOne", "basicLength", "stringFromCharCode", "digitToBasic", "q", "floor", "qMinusT", "baseMinusT", "t", "k", "bias", "tMin", "tMax", "currentValue", "maxInt", "m", "inputLength", "delimiter", "initialBias", "initialN", "fromCodePoint", "splice", "out", "oldi", "w", "digit", "basicToDigit", "basic", "j", "baseMinusTMin", "skew", "numPoints", "firstTime", "damp", "flag", "codePoint", "array", "value", "extra", "counter", "result", "encoded", "labels", "fn", "regexSeparators", "parts", "RangeError", "errors", "type", "Math", "buildExps", "IPV6ADDRESS$", "ZONEID$", "IPV4ADDRESS$", "RESERVED$$", "SUB_DELIMS$$", "IPRIVATE$$", "ALPHA$$", "DIGIT$$", "AUTHORITY_REF$", "USERINFO$", "HOST$", "PORT$", "SAMEDOC_REF$", "FRAGMENT$", "ABSOLUTE_REF$", "SCHEME$", "PATH_ABEMPTY$", "PATH_ABSOLUTE$", "PATH_ROOTLESS$", "PATH_EMPTY$", "QUERY$", "RELATIVE_REF$", "PATH_NOSCHEME$", "GENERIC_REF$", "ABSOLUTE_URI$", "HIER_PART$", "URI_REFERENCE$", "URI$", "RELATIVE$", "RELATIVE_PART$", "AUTHORITY$", "PCHAR$", "PATH$", "SEGMENT_NZ$", "SEGMENT_NZ_NC$", "SEGMENT$", "IP_LITERAL$", "REG_NAME$", "IPV6ADDRZ_RELAXED$", "IPVFUTURE$", "IPV6ADDRESS1$", "IPV6ADDRESS2$", "IPV6ADDRESS3$", "IPV6ADDRESS4$", "IPV6ADDRESS5$", "IPV6ADDRESS6$", "IPV6ADDRESS7$", "IPV6ADDRESS8$", "IPV6ADDRESS9$", "H16$", "LS32$", "DEC_OCTET_RELAXED$", "DEC_OCTET$", "UCSCHAR$$", "GEN_DELIMS$$", "SP$$", "DQUOTE$$", "CR$", "obj", "key", "source", "setInterval", "call", "prototype", "o", "Object", "shift", "sets"], "mappings": ";;;;;;;AYAA,SAAA8E,KAAA,GAAA;sCAAyBsP,IAAzB;YAAA;;;QACKA,KAAKnS,MAAL,GAAc,CAAlB,EAAqB;aACf,CAAL,IAAUmS,KAAK,CAAL,EAAQzQ,KAAR,CAAc,CAAd,EAAiB,CAAC,CAAlB,CAAV;YACMK,KAAKoQ,KAAKnS,MAAL,GAAc,CAAzB;aACK,IAAIgB,IAAI,CAAb,EAAgBA,IAAIe,EAApB,EAAwB,EAAEf,CAA1B,EAA6B;iBACvBA,CAAL,IAAUmR,KAAKnR,CAAL,EAAQU,KAAR,CAAc,CAAd,EAAiB,CAAC,CAAlB,CAAV;;aAEIK,EAAL,IAAWoQ,KAAKpQ,EAAL,EAASL,KAAT,CAAe,CAAf,CAAX;eACOyQ,KAAKpS,IAAL,CAAU,EAAV,CAAP;KAPD,MAQO;eACCoS,KAAK,CAAL,CAAP;;;AAIF,AAAA,SAAA/O,MAAA,CAAuBV,GAAvB,EAAA;WACQ,QAAQA,GAAR,GAAc,GAArB;;AAGD,AAAA,SAAA4B,MAAA,CAAuB0N,CAAvB,EAAA;WACQA,MAAM/S,SAAN,GAAkB,WAAlB,GAAiC+S,MAAM,IAAN,GAAa,MAAb,GAAsBC,OAAOF,SAAP,CAAiBhO,QAAjB,CAA0B+N,IAA1B,CAA+BE,CAA/B,EAAkC7P,KAAlC,CAAwC,GAAxC,EAA6CkE,GAA7C,GAAmDlE,KAAnD,CAAyD,GAAzD,EAA8D+P,KAA9D,GAAsEvT,WAAtE,EAA9D;;AAGD,AAAA,SAAA2B,WAAA,CAA4BoC,GAA5B,EAAA;WACQA,IAAIpC,WAAJ,EAAP;;AAGD,AAAA,SAAA0B,OAAA,CAAwB0P,GAAxB,EAAA;WACQA,QAAQzS,SAAR,IAAqByS,QAAQ,IAA7B,GAAqCA,eAAenJ,KAAf,GAAuBmJ,GAAvB,GAA8B,OAAOA,IAAI1R,MAAX,KAAsB,QAAtB,IAAkC0R,IAAIvP,KAAtC,IAA+CuP,IAAIG,WAAnD,IAAkEH,IAAII,IAAtE,GAA6E,CAACJ,GAAD,CAA7E,GAAqFnJ,MAAMwJ,SAAN,CAAgBrQ,KAAhB,CAAsBoQ,IAAtB,CAA2BJ,GAA3B,CAAxJ,GAA4L,EAAnM;;AAID,AAAA,SAAA5M,MAAA,CAAuBE,MAAvB,EAAuC4M,MAAvC,EAAA;QACOF,MAAM1M,MAAZ;QACI4M,MAAJ,EAAY;aACN,IAAMD,GAAX,IAAkBC,MAAlB,EAA0B;gBACrBD,GAAJ,IAAWC,OAAOD,GAAP,CAAX;;;WAGKD,GAAP;;;ADnCD,SAAA3D,SAAA,CAA0BzK,KAA1B,EAAA;QAEEgL,UAAU,UADX;QAECmD,MAAM,SAFP;QAGClD,UAAU,OAHX;QAICiD,WAAW,SAJZ;QAKCnO,WAAWR,MAAM0L,OAAN,EAAe,UAAf,CALZ;;WAMQ,SANR;QAOCgD,OAAO,SAPR;QAQCrO,eAAeE,OAAOA,OAAO,YAAYC,QAAZ,GAAuB,GAAvB,GAA6BA,QAA7B,GAAwCA,QAAxC,GAAmD,GAAnD,GAAyDA,QAAzD,GAAoEA,QAA3E,IAAuF,GAAvF,GAA6FD,OAAO,gBAAgBC,QAAhB,GAA2B,GAA3B,GAAiCA,QAAjC,GAA4CA,QAAnD,CAA7F,GAA4J,GAA5J,GAAkKD,OAAO,MAAMC,QAAN,GAAiBA,QAAxB,CAAzK,CARhB;;mBASgB,yBAThB;QAUC+K,eAAe,qCAVhB;QAWCD,aAAatL,MAAMyO,YAAN,EAAoBlD,YAApB,CAXd;QAYCiD,YAAY/N,QAAQ,6EAAR,GAAwF,IAZrG;;iBAacA,QAAQ,mBAAR,GAA8B,IAb5C;;mBAcgBT,MAAMyL,OAAN,EAAeC,OAAf,EAAwB,gBAAxB,EAA0C8C,SAA1C,CAdhB;QAeCtC,UAAU3L,OAAOkL,UAAUzL,MAAMyL,OAAN,EAAeC,OAAf,EAAwB,aAAxB,CAAV,GAAmD,GAA1D,CAfX;QAgBCE,YAAYrL,OAAOA,OAAOF,eAAe,GAAf,GAAqBL,MAAMC,YAAN,EAAoBsL,YAApB,EAAkC,OAAlC,CAA5B,IAA0E,GAAjF,CAhBb;QAiBCgD,aAAahO,OAAOA,OAAO,SAAP,IAAoB,GAApB,GAA0BA,OAAO,WAAWmL,OAAlB,CAA1B,GAAuD,GAAvD,GAA6DnL,OAAO,MAAMmL,OAAN,GAAgBA,OAAvB,CAA7D,GAA+F,GAA/F,GAAqGnL,OAAO,UAAUmL,OAAjB,CAArG,GAAiI,GAAjI,GAAuIA,OAA9I,CAjBd;QAkBC4C,qBAAqB/N,OAAOA,OAAO,SAAP,IAAoB,GAApB,GAA0BA,OAAO,WAAWmL,OAAlB,CAA1B,GAAuD,GAAvD,GAA6DnL,OAAO,MAAMmL,OAAN,GAAgBA,OAAvB,CAA7D,GAA+F,GAA/F,GAAqGnL,OAAO,YAAYmL,OAAnB,CAArG,GAAmI,OAAnI,GAA6IA,OAApJ,CAlBtB;;mBAmBgBnL,OAAO+N,qBAAqB,KAArB,GAA6BA,kBAA7B,GAAkD,KAAlD,GAA0DA,kBAA1D,GAA+E,KAA/E,GAAuFA,kBAA9F,CAnBhB;QAoBCF,OAAO7N,OAAOC,WAAW,OAAlB,CApBR;QAqBC6N,QAAQ9N,OAAOA,OAAO6N,OAAO,KAAP,GAAeA,IAAtB,IAA8B,GAA9B,GAAoC/C,YAA3C,CArBT;QAsBCsC,gBAAgBpN,OAAmEA,OAAO6N,OAAO,KAAd,IAAuB,KAAvB,GAA+BC,KAAlG,CAtBjB;;oBAuBiB9N,OAAwD,WAAWA,OAAO6N,OAAO,KAAd,CAAX,GAAkC,KAAlC,GAA0CC,KAAlG,CAvBjB;;oBAwBiB9N,OAAOA,OAAwC6N,IAAxC,IAAgD,SAAhD,GAA4D7N,OAAO6N,OAAO,KAAd,CAA5D,GAAmF,KAAnF,GAA2FC,KAAlG,CAxBjB;;oBAyBiB9N,OAAOA,OAAOA,OAAO6N,OAAO,KAAd,IAAuB,OAAvB,GAAiCA,IAAxC,IAAgD,SAAhD,GAA4D7N,OAAO6N,OAAO,KAAd,CAA5D,GAAmF,KAAnF,GAA2FC,KAAlG,CAzBjB;;oBA0BiB9N,OAAOA,OAAOA,OAAO6N,OAAO,KAAd,IAAuB,OAAvB,GAAiCA,IAAxC,IAAgD,SAAhD,GAA4D7N,OAAO6N,OAAO,KAAd,CAA5D,GAAmF,KAAnF,GAA2FC,KAAlG,CA1BjB;;oBA2BiB9N,OAAOA,OAAOA,OAAO6N,OAAO,KAAd,IAAuB,OAAvB,GAAiCA,IAAxC,IAAgD,SAAhD,GAAmEA,IAAnE,GAA0E,KAA1E,GAA2FC,KAAlG,CA3BjB;;oBA4BiB9N,OAAOA,OAAOA,OAAO6N,OAAO,KAAd,IAAuB,OAAvB,GAAiCA,IAAxC,IAAgD,SAAhD,GAA2FC,KAAlG,CA5BjB;;oBA6BiB9N,OAAOA,OAAOA,OAAO6N,OAAO,KAAd,IAAuB,OAAvB,GAAiCA,IAAxC,IAAgD,SAAhD,GAA2FA,IAAlG,CA7BjB;;oBA8BiB7N,OAAOA,OAAOA,OAAO6N,OAAO,KAAd,IAAuB,OAAvB,GAAiCA,IAAxC,IAAgD,SAAvD,CA9BjB;;mBA+BgB7N,OAAO,CAACoN,aAAD,EAAgBC,aAAhB,EAA+BC,aAA/B,EAA8CC,aAA9C,EAA6DC,aAA7D,EAA4EC,aAA5E,EAA2FC,aAA3F,EAA0GC,aAA1G,EAAyHC,aAAzH,EAAwIjR,IAAxI,CAA6I,GAA7I,CAAP,CA/BhB;QAgCCkO,UAAU7K,OAAOA,OAAON,eAAe,GAAf,GAAqBI,YAA5B,IAA4C,GAAnD,CAhCX;;iBAiCcE,OAAO4K,eAAe,OAAf,GAAyBC,OAAhC,CAjCd;;yBAkCsB7K,OAAO4K,eAAe5K,OAAO,iBAAiBC,QAAjB,GAA4B,MAAnC,CAAf,GAA4D4K,OAAnE,CAlCtB;;iBAmCc7K,OAAO,SAASC,QAAT,GAAoB,MAApB,GAA6BR,MAAMC,YAAN,EAAoBsL,YAApB,EAAkC,OAAlC,CAA7B,GAA0E,GAAjF,CAnCd;QAoCCgC,cAAchN,OAAO,QAAQA,OAAOkN,qBAAqB,GAArB,GAA2BtC,YAA3B,GAA0C,GAA1C,GAAgDuC,UAAvD,CAAR,GAA6E,KAApF,CApCf;;gBAqCanN,OAAOA,OAAOF,eAAe,GAAf,GAAqBL,MAAMC,YAAN,EAAoBsL,YAApB,CAA5B,IAAiE,GAAxE,CArCb;QAsCCM,QAAQtL,OAAOgN,cAAc,GAAd,GAAoBlC,YAApB,GAAmC,KAAnC,GAA2CmC,SAA3C,GAAuD,GAAvD,GAA6D,GAA7D,GAAmEA,SAA1E,CAtCT;QAuCC1B,QAAQvL,OAAOmL,UAAU,GAAjB,CAvCT;QAwCCuB,aAAa1M,OAAOA,OAAOqL,YAAY,GAAnB,IAA0B,GAA1B,GAAgCC,KAAhC,GAAwCtL,OAAO,QAAQuL,KAAf,CAAxC,GAAgE,GAAvE,CAxCd;QAyCCoB,SAAS3M,OAAOF,eAAe,GAAf,GAAqBL,MAAMC,YAAN,EAAoBsL,YAApB,EAAkC,UAAlC,CAA5B,CAzCV;QA0CC+B,WAAW/M,OAAO2M,SAAS,GAAhB,CA1CZ;QA2CCE,cAAc7M,OAAO2M,SAAS,GAAhB,CA3Cf;QA4CCG,iBAAiB9M,OAAOA,OAAOF,eAAe,GAAf,GAAqBL,MAAMC,YAAN,EAAoBsL,YAApB,EAAkC,OAAlC,CAA5B,IAA0E,GAAjF,CA5ClB;QA6CCY,gBAAgB5L,OAAOA,OAAO,QAAQ+M,QAAf,IAA2B,GAAlC,CA7CjB;QA8CClB,iBAAiB7L,OAAO,QAAQA,OAAO6M,cAAcjB,aAArB,CAAR,GAA8C,GAArD,CA9ClB;;qBA+CkB5L,OAAO8M,iBAAiBlB,aAAxB,CA/ClB;;qBAgDkB5L,OAAO6M,cAAcjB,aAArB,CAhDlB;;kBAiDe,QAAQe,MAAR,GAAiB,GAjDhC;QAkDCC,QAAQ5M,OAAO4L,gBAAgB,GAAhB,GAAsBC,cAAtB,GAAuC,GAAvC,GAA6CK,cAA7C,GAA8D,GAA9D,GAAoEJ,cAApE,GAAqF,GAArF,GAA2FC,WAAlG,CAlDT;QAmDCC,SAAShM,OAAOA,OAAO2M,SAAS,GAAT,GAAelN,MAAM,UAAN,EAAkBwL,UAAlB,CAAtB,IAAuD,GAA9D,CAnDV;QAoDCQ,YAAYzL,OAAOA,OAAO2M,SAAS,WAAhB,IAA+B,GAAtC,CApDb;QAqDCN,aAAarM,OAAOA,OAAO,WAAW0M,UAAX,GAAwBd,aAA/B,IAAgD,GAAhD,GAAsDC,cAAtD,GAAuE,GAAvE,GAA6EC,cAA7E,GAA8F,GAA9F,GAAoGC,WAA3G,CArDd;QAsDCQ,OAAOvM,OAAO2L,UAAU,KAAV,GAAkBU,UAAlB,GAA+BrM,OAAO,QAAQgM,MAAf,CAA/B,GAAwD,GAAxD,GAA8DhM,OAAO,QAAQyL,SAAf,CAA9D,GAA0F,GAAjG,CAtDR;QAuDCgB,iBAAiBzM,OAAOA,OAAO,WAAW0M,UAAX,GAAwBd,aAA/B,IAAgD,GAAhD,GAAsDC,cAAtD,GAAuE,GAAvE,GAA6EK,cAA7E,GAA8F,GAA9F,GAAoGH,WAA3G,CAvDlB;QAwDCS,YAAYxM,OAAOyM,iBAAiBzM,OAAO,QAAQgM,MAAf,CAAjB,GAA0C,GAA1C,GAAgDhM,OAAO,QAAQyL,SAAf,CAAhD,GAA4E,GAAnF,CAxDb;QAyDCa,iBAAiBtM,OAAOuM,OAAO,GAAP,GAAaC,SAApB,CAzDlB;QA0DCJ,gBAAgBpM,OAAO2L,UAAU,KAAV,GAAkBU,UAAlB,GAA+BrM,OAAO,QAAQgM,MAAf,CAA/B,GAAwD,GAA/D,CA1DjB;QA4DCG,eAAe,OAAOR,OAAP,GAAiB,MAAjB,GAA0B3L,OAAOA,OAAO,YAAYA,OAAO,MAAMqL,SAAN,GAAkB,IAAzB,CAAZ,GAA6C,IAA7C,GAAoDC,KAApD,GAA4D,GAA5D,GAAkEtL,OAAO,SAASuL,KAAT,GAAiB,GAAxB,CAAlE,GAAiG,IAAxG,IAAgH,IAAhH,GAAuHK,aAAvH,GAAuI,GAAvI,GAA6IC,cAA7I,GAA8J,GAA9J,GAAoKC,cAApK,GAAqL,GAArL,GAA2LC,WAA3L,GAAyM,GAAhN,CAA1B,GAAiP/L,OAAO,SAASgM,MAAT,GAAkB,GAAzB,CAAjP,GAAiR,GAAjR,GAAuRhM,OAAO,SAASyL,SAAT,GAAqB,GAA5B,CAAvR,GAA0T,IA5D1U;QA6DCQ,gBAAgB,WAAWjM,OAAOA,OAAO,YAAYA,OAAO,MAAMqL,SAAN,GAAkB,IAAzB,CAAZ,GAA6C,IAA7C,GAAoDC,KAApD,GAA4D,GAA5D,GAAkEtL,OAAO,SAASuL,KAAT,GAAiB,GAAxB,CAAlE,GAAiG,IAAxG,IAAgH,IAAhH,GAAuHK,aAAvH,GAAuI,GAAvI,GAA6IC,cAA7I,GAA8J,GAA9J,GAAoKK,cAApK,GAAqL,GAArL,GAA2LH,WAA3L,GAAyM,GAAhN,CAAX,GAAkO/L,OAAO,SAASgM,MAAT,GAAkB,GAAzB,CAAlO,GAAkQ,GAAlQ,GAAwQhM,OAAO,SAASyL,SAAT,GAAqB,GAA5B,CAAxQ,GAA2S,IA7D5T;QA8DCC,gBAAgB,OAAOC,OAAP,GAAiB,MAAjB,GAA0B3L,OAAOA,OAAO,YAAYA,OAAO,MAAMqL,SAAN,GAAkB,IAAzB,CAAZ,GAA6C,IAA7C,GAAoDC,KAApD,GAA4D,GAA5D,GAAkEtL,OAAO,SAASuL,KAAT,GAAiB,GAAxB,CAAlE,GAAiG,IAAxG,IAAgH,IAAhH,GAAuHK,aAAvH,GAAuI,GAAvI,GAA6IC,cAA7I,GAA8J,GAA9J,GAAoKC,cAApK,GAAqL,GAArL,GAA2LC,WAA3L,GAAyM,GAAhN,CAA1B,GAAiP/L,OAAO,SAASgM,MAAT,GAAkB,GAAzB,CAAjP,GAAiR,IA9DlS;QA+DCR,eAAe,MAAMxL,OAAO,SAASyL,SAAT,GAAqB,GAA5B,CAAN,GAAyC,IA/DzD;QAgECL,iBAAiB,MAAMpL,OAAO,MAAMqL,SAAN,GAAkB,IAAzB,CAAN,GAAuC,IAAvC,GAA8CC,KAA9C,GAAsD,GAAtD,GAA4DtL,OAAO,SAASuL,KAAT,GAAiB,GAAxB,CAA5D,GAA2F,IAhE7G;WAmEO;oBACO,IAAI/L,MAAJ,CAAWC,MAAM,KAAN,EAAayL,OAAb,EAAsBC,OAAtB,EAA+B,aAA/B,CAAX,EAA0D,GAA1D,CADP;sBAES,IAAI3L,MAAJ,CAAWC,MAAM,WAAN,EAAmBC,YAAnB,EAAiCsL,YAAjC,CAAX,EAA2D,GAA3D,CAFT;kBAGK,IAAIxL,MAAJ,CAAWC,MAAM,iBAAN,EAAyBC,YAAzB,EAAuCsL,YAAvC,CAAX,EAAiE,GAAjE,CAHL;kBAIK,IAAIxL,MAAJ,CAAWC,MAAM,iBAAN,EAAyBC,YAAzB,EAAuCsL,YAAvC,CAAX,EAAiE,GAAjE,CAJL;2BAKc,IAAIxL,MAAJ,CAAWC,MAAM,cAAN,EAAsBC,YAAtB,EAAoCsL,YAApC,CAAX,EAA8D,GAA9D,CALd;mBAMM,IAAIxL,MAAJ,CAAWC,MAAM,QAAN,EAAgBC,YAAhB,EAA8BsL,YAA9B,EAA4C,gBAA5C,EAA8DC,UAA9D,CAAX,EAAsF,GAAtF,CANN;sBAOS,IAAIzL,MAAJ,CAAWC,MAAM,QAAN,EAAgBC,YAAhB,EAA8BsL,YAA9B,EAA4C,gBAA5C,CAAX,EAA0E,GAA1E,CAPT;gBAQG,IAAIxL,MAAJ,CAAWC,MAAM,KAAN,EAAaC,YAAb,EAA2BsL,YAA3B,CAAX,EAAqD,GAArD,CARH;oBASO,IAAIxL,MAAJ,CAAWE,YAAX,EAAyB,GAAzB,CATP;qBAUQ,IAAIF,MAAJ,CAAWC,MAAM,QAAN,EAAgBC,YAAhB,EAA8BqL,UAA9B,CAAX,EAAsD,GAAtD,CAVR;qBAWQ,IAAIvL,MAAJ,CAAWM,YAAX,EAAyB,GAAzB,CAXR;qBAYQ,IAAIN,MAAJ,CAAW,OAAOsL,YAAP,GAAsB,IAAjC,CAZR;qBAaQ,IAAItL,MAAJ,CAAW,WAAWoL,YAAX,GAA0B,GAA1B,GAAgC5K,OAAOA,OAAO,iBAAiBC,QAAjB,GAA4B,MAAnC,IAA6C,GAA7C,GAAmD4K,OAAnD,GAA6D,GAApE,CAAhC,GAA2G,QAAtH,CAbR;KAAP;;AAiBD,mBAAeF,UAAU,KAAV,CAAf;;ADrFA,mBAAeA,UAAU,IAAV,CAAf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ADDA;;AACA,IAAMpC,SAAS,UAAf;;;AAGA,IAAMzG,OAAO,EAAb;AACA,IAAMsG,OAAO,CAAb;AACA,IAAMC,OAAO,EAAb;AACA,IAAMkB,OAAO,EAAb;AACA,IAAMG,OAAO,GAAb;AACA,IAAMf,cAAc,EAApB;AACA,IAAMC,WAAW,GAAjB;AACA,IAAMF,YAAY,GAAlB;;;AAGA,IAAMtB,gBAAgB,OAAtB;AACA,IAAMH,gBAAgB,YAAtB;AACA,IAAMoD,kBAAkB,2BAAxB;;;AAGA,IAAMG,SAAS;aACF,iDADE;cAED,gDAFC;kBAGG;CAHlB;;;AAOA,IAAMlB,gBAAgBxH,OAAOsG,IAA7B;AACA,IAAMN,QAAQ4C,KAAK5C,KAAnB;AACA,IAAMH,qBAAqBjJ,OAAO4H,YAAlC;;;;;;;;;;AAUA,SAAS7K,OAAT,CAAegP,IAAf,EAAqB;OACd,IAAIF,UAAJ,CAAeC,OAAOC,IAAP,CAAf,CAAN;;;;;;;;;;;AAWD,SAASnF,GAAT,CAAauE,KAAb,EAAoBO,EAApB,EAAwB;KACjBH,SAAS,EAAf;KACIrN,SAASiN,MAAMjN,MAAnB;QACOA,QAAP,EAAiB;SACTA,MAAP,IAAiBwN,GAAGP,MAAMjN,MAAN,CAAH,CAAjB;;QAEMqN,MAAP;;;;;;;;;;;;;AAaD,SAAS9C,SAAT,CAAmBD,MAAnB,EAA2BkD,EAA3B,EAA+B;KACxBE,QAAQpD,OAAOnI,KAAP,CAAa,GAAb,CAAd;KACIkL,SAAS,EAAb;KACIK,MAAM1N,MAAN,GAAe,CAAnB,EAAsB;;;WAGZ0N,MAAM,CAAN,IAAW,GAApB;WACSA,MAAM,CAAN,CAAT;;;UAGQpD,OAAOnK,OAAP,CAAesN,eAAf,EAAgC,MAAhC,CAAT;KACMF,SAASjD,OAAOnI,KAAP,CAAa,GAAb,CAAf;KACMmL,UAAU5E,IAAI6E,MAAJ,EAAYC,EAAZ,EAAgBzN,IAAhB,CAAqB,GAArB,CAAhB;QACOsN,SAASC,OAAhB;;;;;;;;;;;;;;;;AAgBD,SAASlD,UAAT,CAAoBE,MAApB,EAA4B;KACrBtE,SAAS,EAAf;KACIoH,UAAU,CAAd;KACMpN,SAASsK,OAAOtK,MAAtB;QACOoN,UAAUpN,MAAjB,EAAyB;MAClBkN,QAAQ5C,OAAON,UAAP,CAAkBoD,SAAlB,CAAd;MACIF,SAAS,MAAT,IAAmBA,SAAS,MAA5B,IAAsCE,UAAUpN,MAApD,EAA4D;;OAErDmN,QAAQ7C,OAAON,UAAP,CAAkBoD,SAAlB,CAAd;OACI,CAACD,QAAQ,MAAT,KAAoB,MAAxB,EAAgC;;WACxBlN,IAAP,CAAY,CAAC,CAACiN,QAAQ,KAAT,KAAmB,EAApB,KAA2BC,QAAQ,KAAnC,IAA4C,OAAxD;IADD,MAEO;;;WAGClN,IAAP,CAAYiN,KAAZ;;;GARF,MAWO;UACCjN,IAAP,CAAYiN,KAAZ;;;QAGKlH,MAAP;;;;;;;;;;;AAWD,IAAMmE,aAAa,SAAbA,UAAa;QAASrI,OAAOmK,aAAP,iCAAwBgB,KAAxB,EAAT;CAAnB;;;;;;;;;;;AAWA,IAAMV,eAAe,SAAfA,YAAe,CAASS,SAAT,EAAoB;KACpCA,YAAY,IAAZ,GAAmB,IAAvB,EAA6B;SACrBA,YAAY,IAAnB;;KAEGA,YAAY,IAAZ,GAAmB,IAAvB,EAA6B;SACrBA,YAAY,IAAnB;;KAEGA,YAAY,IAAZ,GAAmB,IAAvB,EAA6B;SACrBA,YAAY,IAAnB;;QAEM9H,IAAP;CAVD;;;;;;;;;;;;;AAwBA,IAAM8F,eAAe,SAAfA,YAAe,CAASsB,KAAT,EAAgBS,IAAhB,EAAsB;;;QAGnCT,QAAQ,EAAR,GAAa,MAAMA,QAAQ,EAAd,CAAb,IAAkC,CAACS,QAAQ,CAAT,KAAe,CAAjD,CAAP;CAHD;;;;;;;AAWA,IAAMnC,QAAQ,SAARA,KAAQ,CAASF,KAAT,EAAgBkC,SAAhB,EAA2BC,SAA3B,EAAsC;KAC/CvB,IAAI,CAAR;SACQuB,YAAY3B,MAAMR,QAAQoC,IAAd,CAAZ,GAAkCpC,SAAS,CAAnD;UACSQ,MAAMR,QAAQkC,SAAd,CAAT;+BAC8BlC,QAAQgC,gBAAgBjB,IAAhB,IAAwB,CAA9D,EAAiEH,KAAKpG,IAAtE,EAA4E;UACnEgG,MAAMR,QAAQgC,aAAd,CAAR;;QAEMxB,MAAMI,IAAI,CAACoB,gBAAgB,CAAjB,IAAsBhC,KAAtB,IAA+BA,QAAQiC,IAAvC,CAAV,CAAP;CAPD;;;;;;;;;AAiBA,IAAMzC,SAAS,SAATA,MAAS,CAAShE,KAAT,EAAgB;;KAExBF,SAAS,EAAf;KACM6F,cAAc3F,MAAMlG,MAA1B;KACIyJ,IAAI,CAAR;KACIgB,IAAIuB,QAAR;KACIT,OAAOQ,WAAX;;;;;;KAMIS,QAAQtG,MAAMrE,WAAN,CAAkBiK,SAAlB,CAAZ;KACIU,QAAQ,CAAZ,EAAe;UACN,CAAR;;;MAGI,IAAIC,IAAI,CAAb,EAAgBA,IAAID,KAApB,EAA2B,EAAEC,CAA7B,EAAgC;;MAE3BvG,MAAM8D,UAAN,CAAiByC,CAAjB,KAAuB,IAA3B,EAAiC;WAC1B,WAAN;;SAEMxM,IAAP,CAAYiG,MAAM8D,UAAN,CAAiByC,CAAjB,CAAZ;;;;;;MAMI,IAAIhF,QAAQ+E,QAAQ,CAAR,GAAYA,QAAQ,CAApB,GAAwB,CAAzC,EAA4C/E,QAAQoE,WAApD,4BAA4F;;;;;;;MAOvFO,OAAO3C,CAAX;OACK,IAAI4C,IAAI,CAAR,EAAWf,IAAIpG,IAApB,qBAA8CoG,KAAKpG,IAAnD,EAAyD;;OAEpDuC,SAASoE,WAAb,EAA0B;YACnB,eAAN;;;OAGKS,QAAQC,aAAarG,MAAM8D,UAAN,CAAiBvC,OAAjB,CAAb,CAAd;;OAEI6E,SAASpH,IAAT,IAAiBoH,QAAQpB,MAAM,CAACS,SAASlC,CAAV,IAAe4C,CAArB,CAA7B,EAAsD;YAC/C,UAAN;;;QAGIC,QAAQD,CAAb;OACMhB,IAAIC,KAAKC,IAAL,GAAYC,IAAZ,GAAoBF,KAAKC,OAAOE,IAAZ,GAAmBA,IAAnB,GAA0BH,IAAIC,IAA5D;;OAEIe,QAAQjB,CAAZ,EAAe;;;;OAITD,aAAalG,OAAOmG,CAA1B;OACIgB,IAAInB,MAAMS,SAASP,UAAf,CAAR,EAAoC;YAC7B,UAAN;;;QAGIA,UAAL;;;MAIKe,MAAMnG,OAAOhG,MAAP,GAAgB,CAA5B;SACO4K,MAAMnB,IAAI2C,IAAV,EAAgBD,GAAhB,EAAqBC,QAAQ,CAA7B,CAAP;;;;MAIIlB,MAAMzB,IAAI0C,GAAV,IAAiBR,SAASlB,CAA9B,EAAiC;WAC1B,UAAN;;;OAGIS,MAAMzB,IAAI0C,GAAV,CAAL;OACKA,GAAL;;;SAGOD,MAAP,CAAczC,GAAd,EAAmB,CAAnB,EAAsBgB,CAAtB;;;QAIM3I,OAAOmK,aAAP,eAAwBjG,MAAxB,CAAP;CAjFD;;;;;;;;;AA2FA,IAAMiE,SAAS,SAATA,MAAS,CAAS/D,KAAT,EAAgB;KACxBF,SAAS,EAAf;;;SAGQoE,WAAWlE,KAAX,CAAR;;;KAGI2F,cAAc3F,MAAMlG,MAAxB;;;KAGIyK,IAAIuB,QAAR;KACItB,QAAQ,CAAZ;KACIa,OAAOQ,WAAX;;;;;;;;uBAG2B7F,KAA3B,8HAAkC;OAAvBwF,cAAuB;;OAC7BA,iBAAe,IAAnB,EAAyB;WACjBzL,IAAP,CAAY8K,mBAAmBW,cAAnB,CAAZ;;;;;;;;;;;;;;;;;;KAIEZ,cAAc9E,OAAOhG,MAAzB;KACI2K,iBAAiBG,WAArB;;;;;;KAMIA,WAAJ,EAAiB;SACT7K,IAAP,CAAY6L,SAAZ;;;;QAIMnB,iBAAiBkB,WAAxB,EAAqC;;;;MAIhCD,IAAID,MAAR;;;;;;yBAC2BzF,KAA3B,mIAAkC;QAAvBwF,YAAuB;;QAC7BA,gBAAgBjB,CAAhB,IAAqBiB,eAAeE,CAAxC,EAA2C;SACtCF,YAAJ;;;;;;;;;;;;;;;;;;;;;MAMIb,wBAAwBF,iBAAiB,CAA/C;MACIiB,IAAInB,CAAJ,GAAQS,MAAM,CAACS,SAASjB,KAAV,IAAmBG,qBAAzB,CAAZ,EAA6D;WACtD,UAAN;;;WAGQ,CAACe,IAAInB,CAAL,IAAUI,qBAAnB;MACIe,CAAJ;;;;;;;yBAE2B1F,KAA3B,mIAAkC;QAAvBwF,aAAuB;;QAC7BA,gBAAejB,CAAf,IAAoB,EAAEC,KAAF,GAAUiB,MAAlC,EAA0C;aACnC,UAAN;;QAEGD,iBAAgBjB,CAApB,EAAuB;;SAElBQ,IAAIP,KAAR;UACK,IAAIY,IAAIpG,IAAb,qBAAuCoG,KAAKpG,IAA5C,EAAkD;UAC3CmG,IAAIC,KAAKC,IAAL,GAAYC,IAAZ,GAAoBF,KAAKC,OAAOE,IAAZ,GAAmBA,IAAnB,GAA0BH,IAAIC,IAA5D;UACIN,IAAII,CAAR,EAAW;;;UAGLF,UAAUF,IAAII,CAApB;UACMD,aAAalG,OAAOmG,CAA1B;aACOpL,IAAP,CACC8K,mBAAmBC,aAAaK,IAAIF,UAAUC,UAA3B,EAAuC,CAAvC,CAAnB,CADD;UAGIF,MAAMC,UAAUC,UAAhB,CAAJ;;;YAGMnL,IAAP,CAAY8K,mBAAmBC,aAAaC,CAAb,EAAgB,CAAhB,CAAnB,CAAZ;YACOL,MAAMF,KAAN,EAAaG,qBAAb,EAAoCF,kBAAkBG,WAAtD,CAAP;aACQ,CAAR;OACEH,cAAF;;;;;;;;;;;;;;;;;;IAIAD,KAAF;IACED,CAAF;;QAGMzE,OAAOjG,IAAP,CAAY,EAAZ,CAAP;CArFD;;;;;;;;;;;;;AAmGA,IAAMyB,YAAY,SAAZA,SAAY,CAAS0E,KAAT,EAAgB;QAC1BqE,UAAUrE,KAAV,EAAiB,UAASoE,MAAT,EAAiB;SACjCE,cAAczE,IAAd,CAAmBuE,MAAnB,IACJJ,OAAOI,OAAO5I,KAAP,CAAa,CAAb,EAAgB/C,WAAhB,EAAP,CADI,GAEJ2L,MAFH;EADM,CAAP;CADD;;;;;;;;;;;;;AAmBA,IAAMhJ,UAAU,SAAVA,OAAU,CAAS4E,KAAT,EAAgB;QACxBqE,UAAUrE,KAAV,EAAiB,UAASoE,MAAT,EAAiB;SACjCD,cAActE,IAAd,CAAmBuE,MAAnB,IACJ,SAASL,OAAOK,MAAP,CADL,GAEJA,MAFH;EADM,CAAP;CADD;;;;;AAWA,IAAMjJ,WAAW;;;;;;YAML,OANK;;;;;;;;SAcR;YACG+I,UADH;YAEGD;EAhBK;WAkBND,MAlBM;WAmBND,MAnBM;YAoBL3I,OApBK;cAqBHE;CArBd,CAwBA;;ADvbA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA,AACA,AACA,AACA,AAiDA,AAAO,IAAMzD,UAA6C,EAAnD;AAEP,AAAA,SAAAyC,UAAA,CAA2BuJ,GAA3B,EAAA;QACOJ,IAAII,IAAIC,UAAJ,CAAe,CAAf,CAAV;QACI5I,UAAJ;QAEIuI,IAAI,EAAR,EAAYvI,IAAI,OAAOuI,EAAE5F,QAAF,CAAW,EAAX,EAAezD,WAAf,EAAX,CAAZ,KACK,IAAIqJ,IAAI,GAAR,EAAavI,IAAI,MAAMuI,EAAE5F,QAAF,CAAW,EAAX,EAAezD,WAAf,EAAV,CAAb,KACA,IAAIqJ,IAAI,IAAR,EAAcvI,IAAI,MAAM,CAAEuI,KAAK,CAAN,GAAW,GAAZ,EAAiB5F,QAAjB,CAA0B,EAA1B,EAA8BzD,WAA9B,EAAN,GAAoD,GAApD,GAA0D,CAAEqJ,IAAI,EAAL,GAAW,GAAZ,EAAiB5F,QAAjB,CAA0B,EAA1B,EAA8BzD,WAA9B,EAA9D,CAAd,KACAc,IAAI,MAAM,CAAEuI,KAAK,EAAN,GAAY,GAAb,EAAkB5F,QAAlB,CAA2B,EAA3B,EAA+BzD,WAA/B,EAAN,GAAqD,GAArD,GAA2D,CAAGqJ,KAAK,CAAN,GAAW,EAAZ,GAAkB,GAAnB,EAAwB5F,QAAxB,CAAiC,EAAjC,EAAqCzD,WAArC,EAA3D,GAAgH,GAAhH,GAAsH,CAAEqJ,IAAI,EAAL,GAAW,GAAZ,EAAiB5F,QAAjB,CAA0B,EAA1B,EAA8BzD,WAA9B,EAA1H;WAEEc,CAAP;;AAGD,AAAA,SAAAuB,WAAA,CAA4BD,GAA5B,EAAA;QACK6G,SAAS,EAAb;QACIE,IAAI,CAAR;QACMK,KAAKpH,IAAI1C,MAAf;WAEOyJ,IAAIK,EAAX,EAAe;YACRH,IAAI1C,SAASvE,IAAI8G,MAAJ,CAAWC,IAAI,CAAf,EAAkB,CAAlB,CAAT,EAA+B,EAA/B,CAAV;YAEIE,IAAI,GAAR,EAAa;sBACF7H,OAAO4H,YAAP,CAAoBC,CAApB,CAAV;iBACK,CAAL;SAFD,MAIK,IAAIA,KAAK,GAAL,IAAYA,IAAI,GAApB,EAAyB;gBACxBG,KAAKL,CAAN,IAAY,CAAhB,EAAmB;oBACZG,KAAK3C,SAASvE,IAAI8G,MAAJ,CAAWC,IAAI,CAAf,EAAkB,CAAlB,CAAT,EAA+B,EAA/B,CAAX;0BACU3H,OAAO4H,YAAP,CAAqB,CAACC,IAAI,EAAL,KAAY,CAAb,GAAmBC,KAAK,EAA5C,CAAV;aAFD,MAGO;0BACIlH,IAAI8G,MAAJ,CAAWC,CAAX,EAAc,CAAd,CAAV;;iBAEI,CAAL;SAPI,MASA,IAAIE,KAAK,GAAT,EAAc;gBACbG,KAAKL,CAAN,IAAY,CAAhB,EAAmB;oBACZG,KAAK3C,SAASvE,IAAI8G,MAAJ,CAAWC,IAAI,CAAf,EAAkB,CAAlB,CAAT,EAA+B,EAA/B,CAAX;oBACMI,KAAK5C,SAASvE,IAAI8G,MAAJ,CAAWC,IAAI,CAAf,EAAkB,CAAlB,CAAT,EAA+B,EAA/B,CAAX;0BACU3H,OAAO4H,YAAP,CAAqB,CAACC,IAAI,EAAL,KAAY,EAAb,GAAoB,CAACC,KAAK,EAAN,KAAa,CAAjC,GAAuCC,KAAK,EAAhE,CAAV;aAHD,MAIO;0BACInH,IAAI8G,MAAJ,CAAWC,CAAX,EAAc,CAAd,CAAV;;iBAEI,CAAL;SARI,MAUA;sBACM/G,IAAI8G,MAAJ,CAAWC,CAAX,EAAc,CAAd,CAAV;iBACK,CAAL;;;WAIKF,MAAP;;AAGD,SAAAD,2BAAA,CAAqC3J,UAArC,EAA+DkG,QAA/D,EAAA;aACAxF,gBAAC,CAA0BqC,GAA1B,EAAD;YACQF,SAASG,YAAYD,GAAZ,CAAf;eACQ,CAACF,OAAOzD,KAAP,CAAa8G,SAASpD,UAAtB,CAAD,GAAqCC,GAArC,GAA2CF,MAAnD;;QAGG7C,WAAW1B,MAAf,EAAuB0B,WAAW1B,MAAX,GAAoB6D,OAAOnC,WAAW1B,MAAlB,EAA0BkC,OAA1B,CAAkC0F,SAASzF,WAA3C,EAAwDC,gBAAxD,EAA0E1B,WAA1E,GAAwFwB,OAAxF,CAAgG0F,SAASwD,UAAzG,EAAqH,EAArH,CAApB;QACnB1J,WAAWwF,QAAX,KAAwBlG,SAA5B,EAAuCU,WAAWwF,QAAX,GAAsBrD,OAAOnC,WAAWwF,QAAlB,EAA4BhF,OAA5B,CAAoC0F,SAASzF,WAA7C,EAA0DC,gBAA1D,EAA4EF,OAA5E,CAAoF0F,SAASuD,YAA7F,EAA2G5I,UAA3G,EAAuHL,OAAvH,CAA+H0F,SAASzF,WAAxI,EAAqJE,WAArJ,CAAtB;QACnCX,WAAWmE,IAAX,KAAoB7E,SAAxB,EAAmCU,WAAWmE,IAAX,GAAkBhC,OAAOnC,WAAWmE,IAAlB,EAAwB3D,OAAxB,CAAgC0F,SAASzF,WAAzC,EAAsDC,gBAAtD,EAAwE1B,WAAxE,GAAsFwB,OAAtF,CAA8F0F,SAASsD,QAAvG,EAAiH3I,UAAjH,EAA6HL,OAA7H,CAAqI0F,SAASzF,WAA9I,EAA2JE,WAA3J,CAAlB;QAC/BX,WAAWP,IAAX,KAAoBH,SAAxB,EAAmCU,WAAWP,IAAX,GAAkB0C,OAAOnC,WAAWP,IAAlB,EAAwBe,OAAxB,CAAgC0F,SAASzF,WAAzC,EAAsDC,gBAAtD,EAAwEF,OAAxE,CAAiFR,WAAW1B,MAAX,GAAoB4H,SAASoD,QAA7B,GAAwCpD,SAASqD,iBAAlI,EAAsJ1I,UAAtJ,EAAkKL,OAAlK,CAA0K0F,SAASzF,WAAnL,EAAgME,WAAhM,CAAlB;QAC/BX,WAAWE,KAAX,KAAqBZ,SAAzB,EAAoCU,WAAWE,KAAX,GAAmBiC,OAAOnC,WAAWE,KAAlB,EAAyBM,OAAzB,CAAiC0F,SAASzF,WAA1C,EAAuDC,gBAAvD,EAAyEF,OAAzE,CAAiF0F,SAASmD,SAA1F,EAAqGxI,UAArG,EAAiHL,OAAjH,CAAyH0F,SAASzF,WAAlI,EAA+IE,WAA/I,CAAnB;QAChCX,WAAW8D,QAAX,KAAwBxE,SAA5B,EAAuCU,WAAW8D,QAAX,GAAsB3B,OAAOnC,WAAW8D,QAAlB,EAA4BtD,OAA5B,CAAoC0F,SAASzF,WAA7C,EAA0DC,gBAA1D,EAA4EF,OAA5E,CAAoF0F,SAASkD,YAA7F,EAA2GvI,UAA3G,EAAuHL,OAAvH,CAA+H0F,SAASzF,WAAxI,EAAqJE,WAArJ,CAAtB;WAEhCX,UAAP;;AACA;AAED,SAAAgJ,kBAAA,CAA4BjG,GAA5B,EAAA;WACQA,IAAIvC,OAAJ,CAAY,SAAZ,EAAuB,IAAvB,KAAgC,GAAvC;;AAGD,SAAAyG,cAAA,CAAwB9C,IAAxB,EAAqC+B,QAArC,EAAA;QACOnG,UAAUoE,KAAK/E,KAAL,CAAW8G,SAAS2C,WAApB,KAAoC,EAApD;;iCACoB9I,OAFrB;QAEUmJ,OAFV;;QAIKA,OAAJ,EAAa;eACLA,QAAQ1G,KAAR,CAAc,GAAd,EAAmBuG,GAAnB,CAAuBC,kBAAvB,EAA2C5I,IAA3C,CAAgD,GAAhD,CAAP;KADD,MAEO;eACC+D,IAAP;;;AAIF,SAAA6C,cAAA,CAAwB7C,IAAxB,EAAqC+B,QAArC,EAAA;QACOnG,UAAUoE,KAAK/E,KAAL,CAAW8G,SAASC,WAApB,KAAoC,EAApD;;kCAC0BpG,OAF3B;QAEUmJ,OAFV;QAEmBxB,IAFnB;;QAIKwB,OAAJ,EAAa;oCACUA,QAAQlK,WAAR,GAAsBwD,KAAtB,CAA4B,IAA5B,EAAkC2G,OAAlC,EADV;;YACLL,IADK;YACCG,KADD;;YAENR,cAAcQ,QAAQA,MAAMzG,KAAN,CAAY,GAAZ,EAAiBuG,GAAjB,CAAqBC,kBAArB,CAAR,GAAmD,EAAvE;YACMN,aAAaI,KAAKtG,KAAL,CAAW,GAAX,EAAgBuG,GAAhB,CAAoBC,kBAApB,CAAnB;YACMR,yBAAyBtC,SAAS2C,WAAT,CAAqBzC,IAArB,CAA0BsC,WAAWA,WAAWrI,MAAX,GAAoB,CAA/B,CAA1B,CAA/B;YACMkI,aAAaC,yBAAyB,CAAzB,GAA6B,CAAhD;YACMG,kBAAkBD,WAAWrI,MAAX,GAAoBkI,UAA5C;YACMpI,SAASyI,MAAcL,UAAd,CAAf;aAEK,IAAIlH,IAAI,CAAb,EAAgBA,IAAIkH,UAApB,EAAgC,EAAElH,CAAlC,EAAqC;mBAC7BA,CAAP,IAAYoH,YAAYpH,CAAZ,KAAkBqH,WAAWC,kBAAkBtH,CAA7B,CAAlB,IAAqD,EAAjE;;YAGGmH,sBAAJ,EAA4B;mBACpBD,aAAa,CAApB,IAAyBtB,eAAe9G,OAAOoI,aAAa,CAApB,CAAf,EAAuCrC,QAAvC,CAAzB;;YAGK+B,gBAAgB9H,OAAOmI,MAAP,CAAmD,UAACH,GAAD,EAAME,KAAN,EAAaP,KAAb,EAA3E;gBACO,CAACO,KAAD,IAAUA,UAAU,GAAxB,EAA6B;oBACtBD,cAAcD,IAAIA,IAAI9H,MAAJ,GAAa,CAAjB,CAApB;oBACI+H,eAAeA,YAAYN,KAAZ,GAAoBM,YAAY/H,MAAhC,KAA2CyH,KAA9D,EAAqE;gCACxDzH,MAAZ;iBADD,MAEO;wBACFC,IAAJ,CAAS,EAAEwH,YAAF,EAASzH,QAAS,CAAlB,EAAT;;;mBAGK8H,GAAP;SATqB,EAUnB,EAVmB,CAAtB;YAYMN,oBAAoBI,cAAcC,IAAd,CAAmB,UAACF,CAAD,EAAID,CAAJ;mBAAUA,EAAE1H,MAAF,GAAW2H,EAAE3H,MAAvB;SAAnB,EAAkD,CAAlD,CAA1B;YAEIoH,gBAAJ;YACII,qBAAqBA,kBAAkBxH,MAAlB,GAA2B,CAApD,EAAuD;gBAChDsH,WAAWxH,OAAO4B,KAAP,CAAa,CAAb,EAAgB8F,kBAAkBC,KAAlC,CAAjB;gBACMF,UAAUzH,OAAO4B,KAAP,CAAa8F,kBAAkBC,KAAlB,GAA0BD,kBAAkBxH,MAAzD,CAAhB;sBACUsH,SAASvH,IAAT,CAAc,GAAd,IAAqB,IAArB,GAA4BwH,QAAQxH,IAAR,CAAa,GAAb,CAAtC;SAHD,MAIO;sBACID,OAAOC,IAAP,CAAY,GAAZ,CAAV;;YAGGsH,IAAJ,EAAU;uBACE,MAAMA,IAAjB;;eAGMD,OAAP;KA5CD,MA6CO;eACCtD,IAAP;;;AAIF,IAAMqD,YAAY,iIAAlB;AACA,IAAMD,wBAA4C,EAAD,CAAKnI,KAAL,CAAW,OAAX,EAAqB,CAArB,MAA4BE,SAA7E;AAEA,AAAA,SAAAQ,KAAA,CAAsBqH,SAAtB,EAAA;QAAwClI,OAAxC,uEAA6D,EAA7D;;QACOe,aAA2B,EAAjC;QACMkG,WAAYjH,QAAQuC,GAAR,KAAgB,KAAhB,GAAwB8C,YAAxB,GAAuCD,YAAzD;QAEIpF,QAAQ+G,SAAR,KAAsB,QAA1B,EAAoCmB,YAAY,CAAClI,QAAQX,MAAR,GAAiBW,QAAQX,MAAR,GAAiB,GAAlC,GAAwC,EAAzC,IAA+C,IAA/C,GAAsD6I,SAAlE;QAE9BpH,UAAUoH,UAAU/H,KAAV,CAAgBoI,SAAhB,CAAhB;QAEIzH,OAAJ,EAAa;YACRwH,qBAAJ,EAA2B;;uBAEfjJ,MAAX,GAAoByB,QAAQ,CAAR,CAApB;uBACWyF,QAAX,GAAsBzF,QAAQ,CAAR,CAAtB;uBACWoE,IAAX,GAAkBpE,QAAQ,CAAR,CAAlB;uBACWkE,IAAX,GAAkBqD,SAASvH,QAAQ,CAAR,CAAT,EAAqB,EAArB,CAAlB;uBACWN,IAAX,GAAkBM,QAAQ,CAAR,KAAc,EAAhC;uBACWG,KAAX,GAAmBH,QAAQ,CAAR,CAAnB;uBACW+D,QAAX,GAAsB/D,QAAQ,CAAR,CAAtB;;gBAGIqH,MAAMpH,WAAWiE,IAAjB,CAAJ,EAA4B;2BAChBA,IAAX,GAAkBlE,QAAQ,CAAR,CAAlB;;SAZF,MAcO;;;uBAEKzB,MAAX,GAAoByB,QAAQ,CAAR,KAAcT,SAAlC;uBACWkG,QAAX,GAAuB2B,UAAUE,OAAV,CAAkB,GAAlB,MAA2B,CAAC,CAA5B,GAAgCtH,QAAQ,CAAR,CAAhC,GAA6CT,SAApE;uBACW6E,IAAX,GAAmBgD,UAAUE,OAAV,CAAkB,IAAlB,MAA4B,CAAC,CAA7B,GAAiCtH,QAAQ,CAAR,CAAjC,GAA8CT,SAAjE;uBACW2E,IAAX,GAAkBqD,SAASvH,QAAQ,CAAR,CAAT,EAAqB,EAArB,CAAlB;uBACWN,IAAX,GAAkBM,QAAQ,CAAR,KAAc,EAAhC;uBACWG,KAAX,GAAoBiH,UAAUE,OAAV,CAAkB,GAAlB,MAA2B,CAAC,CAA5B,GAAgCtH,QAAQ,CAAR,CAAhC,GAA6CT,SAAjE;uBACWwE,QAAX,GAAuBqD,UAAUE,OAAV,CAAkB,GAAlB,MAA2B,CAAC,CAA5B,GAAgCtH,QAAQ,CAAR,CAAhC,GAA6CT,SAApE;;gBAGI8H,MAAMpH,WAAWiE,IAAjB,CAAJ,EAA4B;2BAChBA,IAAX,GAAmBkD,UAAU/H,KAAV,CAAgB,+BAAhB,IAAmDW,QAAQ,CAAR,CAAnD,GAAgET,SAAnF;;;YAIEU,WAAWmE,IAAf,EAAqB;;uBAETA,IAAX,GAAkB6C,eAAeC,eAAejH,WAAWmE,IAA1B,EAAgC+B,QAAhC,CAAf,EAA0DA,QAA1D,CAAlB;;;YAIGlG,WAAW1B,MAAX,KAAsBgB,SAAtB,IAAmCU,WAAWwF,QAAX,KAAwBlG,SAA3D,IAAwEU,WAAWmE,IAAX,KAAoB7E,SAA5F,IAAyGU,WAAWiE,IAAX,KAAoB3E,SAA7H,IAA0I,CAACU,WAAWP,IAAtJ,IAA8JO,WAAWE,KAAX,KAAqBZ,SAAvL,EAAkM;uBACtL0G,SAAX,GAAuB,eAAvB;SADD,MAEO,IAAIhG,WAAW1B,MAAX,KAAsBgB,SAA1B,EAAqC;uBAChC0G,SAAX,GAAuB,UAAvB;SADM,MAEA,IAAIhG,WAAW8D,QAAX,KAAwBxE,SAA5B,EAAuC;uBAClC0G,SAAX,GAAuB,UAAvB;SADM,MAEA;uBACKA,SAAX,GAAuB,KAAvB;;;YAIG/G,QAAQ+G,SAAR,IAAqB/G,QAAQ+G,SAAR,KAAsB,QAA3C,IAAuD/G,QAAQ+G,SAAR,KAAsBhG,WAAWgG,SAA5F,EAAuG;uBAC3F9G,KAAX,GAAmBc,WAAWd,KAAX,IAAoB,kBAAkBD,QAAQ+G,SAA1B,GAAsC,aAA7E;;;YAIKrG,gBAAgBvB,QAAQ,CAACa,QAAQX,MAAR,IAAkB0B,WAAW1B,MAA7B,IAAuC,EAAxC,EAA4CU,WAA5C,EAAR,CAAtB;;YAGI,CAACC,QAAQsD,cAAT,KAA4B,CAAC5C,aAAD,IAAkB,CAACA,cAAc4C,cAA7D,CAAJ,EAAkF;;gBAE7EvC,WAAWmE,IAAX,KAAoBlF,QAAQ2E,UAAR,IAAuBjE,iBAAiBA,cAAciE,UAA1E,CAAJ,EAA4F;;oBAEvF;+BACQO,IAAX,GAAkBzC,SAASC,OAAT,CAAiB3B,WAAWmE,IAAX,CAAgB3D,OAAhB,CAAwB0F,SAASzF,WAAjC,EAA8CuC,WAA9C,EAA2DhE,WAA3D,EAAjB,CAAlB;iBADD,CAEE,OAAOyC,CAAP,EAAU;+BACAvC,KAAX,GAAmBc,WAAWd,KAAX,IAAoB,oEAAoEuC,CAA3G;;;;wCAI0BzB,UAA5B,EAAwCqE,YAAxC;SAXD,MAYO;;wCAEsBrE,UAA5B,EAAwCkG,QAAxC;;;YAIGvG,iBAAiBA,cAAcG,KAAnC,EAA0C;0BAC3BA,KAAd,CAAoBE,UAApB,EAAgCf,OAAhC;;KA3EF,MA6EO;mBACKC,KAAX,GAAmBc,WAAWd,KAAX,IAAoB,wBAAvC;;WAGMc,UAAP;;AACA;AAED,SAAAiG,mBAAA,CAA6BjG,UAA7B,EAAuDf,OAAvD,EAAA;QACOiH,WAAYjH,QAAQuC,GAAR,KAAgB,KAAhB,GAAwB8C,YAAxB,GAAuCD,YAAzD;QACMuB,YAA0B,EAAhC;QAEI5F,WAAWwF,QAAX,KAAwBlG,SAA5B,EAAuC;kBAC5BgB,IAAV,CAAeN,WAAWwF,QAA1B;kBACUlF,IAAV,CAAe,GAAf;;QAGGN,WAAWmE,IAAX,KAAoB7E,SAAxB,EAAmC;;kBAExBgB,IAAV,CAAe0G,eAAeC,eAAe9E,OAAOnC,WAAWmE,IAAlB,CAAf,EAAwC+B,QAAxC,CAAf,EAAkEA,QAAlE,EAA4E1F,OAA5E,CAAoF0F,SAASC,WAA7F,EAA0G,UAACe,CAAD,EAAIJ,EAAJ,EAAQC,EAAR;mBAAe,MAAMD,EAAN,IAAYC,KAAK,QAAQA,EAAb,GAAkB,EAA9B,IAAoC,GAAnD;SAA1G,CAAf;;QAGG,OAAO/G,WAAWiE,IAAlB,KAA2B,QAA3B,IAAuC,OAAOjE,WAAWiE,IAAlB,KAA2B,QAAtE,EAAgF;kBACrE3D,IAAV,CAAe,GAAf;kBACUA,IAAV,CAAe6B,OAAOnC,WAAWiE,IAAlB,CAAf;;WAGM2B,UAAUvF,MAAV,GAAmBuF,UAAUxF,IAAV,CAAe,EAAf,CAAnB,GAAwCd,SAA/C;;AACA;AAED,IAAMuH,OAAO,UAAb;AACA,IAAMD,OAAO,aAAb;AACA,IAAMD,OAAO,eAAb;AACA,AACA,IAAMF,OAAO,wBAAb;AAEA,AAAA,SAAAhB,iBAAA,CAAkCc,KAAlC,EAAA;QACOF,SAAuB,EAA7B;WAEOE,MAAMlG,MAAb,EAAqB;YAChBkG,MAAMnH,KAAN,CAAYyH,IAAZ,CAAJ,EAAuB;oBACdN,MAAM/F,OAAN,CAAcqG,IAAd,EAAoB,EAApB,CAAR;SADD,MAEO,IAAIN,MAAMnH,KAAN,CAAYwH,IAAZ,CAAJ,EAAuB;oBACrBL,MAAM/F,OAAN,CAAcoG,IAAd,EAAoB,GAApB,CAAR;SADM,MAEA,IAAIL,MAAMnH,KAAN,CAAYuH,IAAZ,CAAJ,EAAuB;oBACrBJ,MAAM/F,OAAN,CAAcmG,IAAd,EAAoB,GAApB,CAAR;mBACOD,GAAP;SAFM,MAGA,IAAIH,UAAU,GAAV,IAAiBA,UAAU,IAA/B,EAAqC;oBACnC,EAAR;SADM,MAEA;gBACAC,KAAKD,MAAMnH,KAAN,CAAYqH,IAAZ,CAAX;gBACID,EAAJ,EAAQ;oBACDX,IAAIW,GAAG,CAAH,CAAV;wBACQD,MAAMxE,KAAN,CAAY8D,EAAExF,MAAd,CAAR;uBACOC,IAAP,CAAYuF,CAAZ;aAHD,MAIO;sBACA,IAAIS,KAAJ,CAAU,kCAAV,CAAN;;;;WAKID,OAAOjG,IAAP,CAAY,EAAZ,CAAP;;AACA;AAED,AAAA,SAAAR,SAAA,CAA0BI,UAA1B,EAAA;QAAoDf,OAApD,uEAAyE,EAAzE;;QACOiH,WAAYjH,QAAQuC,GAAR,GAAc8C,YAAd,GAA6BD,YAA/C;QACMuB,YAA0B,EAAhC;;QAGMjG,gBAAgBvB,QAAQ,CAACa,QAAQX,MAAR,IAAkB0B,WAAW1B,MAA7B,IAAuC,EAAxC,EAA4CU,WAA5C,EAAR,CAAtB;;QAGIW,iBAAiBA,cAAcC,SAAnC,EAA8CD,cAAcC,SAAd,CAAwBI,UAAxB,EAAoCf,OAApC;QAE1Ce,WAAWmE,IAAf,EAAqB;;YAEhB+B,SAASC,WAAT,CAAqBC,IAArB,CAA0BpG,WAAWmE,IAArC,CAAJ,EAAgD;;;;aAK3C,IAAIlF,QAAQ2E,UAAR,IAAuBjE,iBAAiBA,cAAciE,UAA1D,EAAuE;;oBAEvE;+BACQO,IAAX,GAAmB,CAAClF,QAAQuC,GAAT,GAAeE,SAASC,OAAT,CAAiB3B,WAAWmE,IAAX,CAAgB3D,OAAhB,CAAwB0F,SAASzF,WAAjC,EAA8CuC,WAA9C,EAA2DhE,WAA3D,EAAjB,CAAf,GAA4G0C,SAASG,SAAT,CAAmB7B,WAAWmE,IAA9B,CAA/H;iBADD,CAEE,OAAO1C,CAAP,EAAU;+BACAvC,KAAX,GAAmBc,WAAWd,KAAX,IAAoB,iDAAiD,CAACD,QAAQuC,GAAT,GAAe,OAAf,GAAyB,SAA1E,IAAuF,iBAAvF,GAA2GC,CAAlJ;;;;;gCAMyBzB,UAA5B,EAAwCkG,QAAxC;QAEIjH,QAAQ+G,SAAR,KAAsB,QAAtB,IAAkChG,WAAW1B,MAAjD,EAAyD;kBAC9CgC,IAAV,CAAeN,WAAW1B,MAA1B;kBACUgC,IAAV,CAAe,GAAf;;QAGKwF,YAAYG,oBAAoBjG,UAApB,EAAgCf,OAAhC,CAAlB;QACI6G,cAAcxG,SAAlB,EAA6B;YACxBL,QAAQ+G,SAAR,KAAsB,QAA1B,EAAoC;sBACzB1F,IAAV,CAAe,IAAf;;kBAGSA,IAAV,CAAewF,SAAf;YAEI9F,WAAWP,IAAX,IAAmBO,WAAWP,IAAX,CAAgBiG,MAAhB,CAAuB,CAAvB,MAA8B,GAArD,EAA0D;sBAC/CpF,IAAV,CAAe,GAAf;;;QAIEN,WAAWP,IAAX,KAAoBH,SAAxB,EAAmC;YAC9BuG,IAAI7F,WAAWP,IAAnB;YAEI,CAACR,QAAQ8G,YAAT,KAA0B,CAACpG,aAAD,IAAkB,CAACA,cAAcoG,YAA3D,CAAJ,EAA8E;gBACzEN,kBAAkBI,CAAlB,CAAJ;;YAGGC,cAAcxG,SAAlB,EAA6B;gBACxBuG,EAAErF,OAAF,CAAU,OAAV,EAAmB,MAAnB,CAAJ,CAD4B;;kBAInBF,IAAV,CAAeuF,CAAf;;QAGG7F,WAAWE,KAAX,KAAqBZ,SAAzB,EAAoC;kBACzBgB,IAAV,CAAe,GAAf;kBACUA,IAAV,CAAeN,WAAWE,KAA1B;;QAGGF,WAAW8D,QAAX,KAAwBxE,SAA5B,EAAuC;kBAC5BgB,IAAV,CAAe,GAAf;kBACUA,IAAV,CAAeN,WAAW8D,QAA1B;;WAGM8B,UAAUxF,IAAV,CAAe,EAAf,CAAP,CAxED;;AAyEC;AAED,AAAA,SAAA2E,iBAAA,CAAkCQ,IAAlC,EAAsDD,QAAtD,EAAA;QAA8ErG,OAA9E,uEAAmG,EAAnG;QAAuG0G,iBAAvG;;QACON,SAAuB,EAA7B;QAEI,CAACM,iBAAL,EAAwB;eAChB7F,MAAMF,UAAU2F,IAAV,EAAgBtG,OAAhB,CAAN,EAAgCA,OAAhC,CAAP,CADuB;mBAEZa,MAAMF,UAAU0F,QAAV,EAAoBrG,OAApB,CAAN,EAAoCA,OAApC,CAAX,CAFuB;;cAIdA,WAAW,EAArB;QAEI,CAACA,QAAQE,QAAT,IAAqBmG,SAAShH,MAAlC,EAA0C;eAClCA,MAAP,GAAgBgH,SAAShH,MAAzB;;eAEOkH,QAAP,GAAkBF,SAASE,QAA3B;eACOrB,IAAP,GAAcmB,SAASnB,IAAvB;eACOF,IAAP,GAAcqB,SAASrB,IAAvB;eACOxE,IAAP,GAAcgG,kBAAkBH,SAAS7F,IAAT,IAAiB,EAAnC,CAAd;eACOS,KAAP,GAAeoF,SAASpF,KAAxB;KAPD,MAQO;YACFoF,SAASE,QAAT,KAAsBlG,SAAtB,IAAmCgG,SAASnB,IAAT,KAAkB7E,SAArD,IAAkEgG,SAASrB,IAAT,KAAkB3E,SAAxF,EAAmG;;mBAE3FkG,QAAP,GAAkBF,SAASE,QAA3B;mBACOrB,IAAP,GAAcmB,SAASnB,IAAvB;mBACOF,IAAP,GAAcqB,SAASrB,IAAvB;mBACOxE,IAAP,GAAcgG,kBAAkBH,SAAS7F,IAAT,IAAiB,EAAnC,CAAd;mBACOS,KAAP,GAAeoF,SAASpF,KAAxB;SAND,MAOO;gBACF,CAACoF,SAAS7F,IAAd,EAAoB;uBACZA,IAAP,GAAc8F,KAAK9F,IAAnB;oBACI6F,SAASpF,KAAT,KAAmBZ,SAAvB,EAAkC;2BAC1BY,KAAP,GAAeoF,SAASpF,KAAxB;iBADD,MAEO;2BACCA,KAAP,GAAeqF,KAAKrF,KAApB;;aALF,MAOO;oBACFoF,SAAS7F,IAAT,CAAciG,MAAd,CAAqB,CAArB,MAA4B,GAAhC,EAAqC;2BAC7BjG,IAAP,GAAcgG,kBAAkBH,SAAS7F,IAA3B,CAAd;iBADD,MAEO;wBACF,CAAC8F,KAAKC,QAAL,KAAkBlG,SAAlB,IAA+BiG,KAAKpB,IAAL,KAAc7E,SAA7C,IAA0DiG,KAAKtB,IAAL,KAAc3E,SAAzE,KAAuF,CAACiG,KAAK9F,IAAjG,EAAuG;+BAC/FA,IAAP,GAAc,MAAM6F,SAAS7F,IAA7B;qBADD,MAEO,IAAI,CAAC8F,KAAK9F,IAAV,EAAgB;+BACfA,IAAP,GAAc6F,SAAS7F,IAAvB;qBADM,MAEA;+BACCA,IAAP,GAAc8F,KAAK9F,IAAL,CAAUsC,KAAV,CAAgB,CAAhB,EAAmBwD,KAAK9F,IAAL,CAAUyC,WAAV,CAAsB,GAAtB,IAA6B,CAAhD,IAAqDoD,SAAS7F,IAA5E;;2BAEMA,IAAP,GAAcgG,kBAAkBJ,OAAO5F,IAAzB,CAAd;;uBAEMS,KAAP,GAAeoF,SAASpF,KAAxB;;;mBAGMsF,QAAP,GAAkBD,KAAKC,QAAvB;mBACOrB,IAAP,GAAcoB,KAAKpB,IAAnB;mBACOF,IAAP,GAAcsB,KAAKtB,IAAnB;;eAEM3F,MAAP,GAAgBiH,KAAKjH,MAArB;;WAGMwF,QAAP,GAAkBwB,SAASxB,QAA3B;WAEOuB,MAAP;;AACA;AAED,AAAA,SAAAD,OAAA,CAAwBJ,OAAxB,EAAwCE,WAAxC,EAA4DjG,OAA5D,EAAA;QACOgG,oBAAoBE,OAAO,EAAE7G,QAAS,MAAX,EAAP,EAA4BW,OAA5B,CAA1B;WACOW,UAAUmF,kBAAkBjF,MAAMkF,OAAN,EAAeC,iBAAf,CAAlB,EAAqDnF,MAAMoF,WAAN,EAAmBD,iBAAnB,CAArD,EAA4FA,iBAA5F,EAA+G,IAA/G,CAAV,EAAgIA,iBAAhI,CAAP;;AACA;AAID,AAAA,SAAAH,SAAA,CAA0BD,GAA1B,EAAmC5F,OAAnC,EAAA;QACK,OAAO4F,GAAP,KAAe,QAAnB,EAA6B;cACtBjF,UAAUE,MAAM+E,GAAN,EAAW5F,OAAX,CAAV,EAA+BA,OAA/B,CAAN;KADD,MAEO,IAAI0F,OAAOE,GAAP,MAAgB,QAApB,EAA8B;cAC9B/E,MAAMF,UAAyBiF,GAAzB,EAA8B5F,OAA9B,CAAN,EAA8CA,OAA9C,CAAN;;WAGM4F,GAAP;;AACA;AAID,AAAA,SAAAD,KAAA,CAAsBH,IAAtB,EAAgCC,IAAhC,EAA0CzF,OAA1C,EAAA;QACK,OAAOwF,IAAP,KAAgB,QAApB,EAA8B;eACtB7E,UAAUE,MAAM2E,IAAN,EAAYxF,OAAZ,CAAV,EAAgCA,OAAhC,CAAP;KADD,MAEO,IAAI0F,OAAOF,IAAP,MAAiB,QAArB,EAA+B;eAC9B7E,UAAyB6E,IAAzB,EAA+BxF,OAA/B,CAAP;;QAGG,OAAOyF,IAAP,KAAgB,QAApB,EAA8B;eACtB9E,UAAUE,MAAM4E,IAAN,EAAYzF,OAAZ,CAAV,EAAgCA,OAAhC,CAAP;KADD,MAEO,IAAI0F,OAAOD,IAAP,MAAiB,QAArB,EAA+B;eAC9B9E,UAAyB8E,IAAzB,EAA+BzF,OAA/B,CAAP;;WAGMwF,SAASC,IAAhB;;AACA;AAED,AAAA,SAAAF,eAAA,CAAgCzB,GAAhC,EAA4C9D,OAA5C,EAAA;WACQ8D,OAAOA,IAAIqB,QAAJ,GAAe5D,OAAf,CAAwB,CAACvB,OAAD,IAAY,CAACA,QAAQuC,GAArB,GAA2B6C,aAAaE,MAAxC,GAAiDD,aAAaC,MAAtF,EAA+F1D,UAA/F,CAAd;;AACA;AAED,AAAA,SAAAe,iBAAA,CAAkCmB,GAAlC,EAA8C9D,OAA9C,EAAA;WACQ8D,OAAOA,IAAIqB,QAAJ,GAAe5D,OAAf,CAAwB,CAACvB,OAAD,IAAY,CAACA,QAAQuC,GAArB,GAA2B6C,aAAa5D,WAAxC,GAAsD6D,aAAa7D,WAA3F,EAAyGuC,WAAzG,CAAd;CACA;;ADziBD,IAAMzD,UAA2B;YACvB,MADuB;gBAGnB,IAHmB;WAKxB,eAAUS,UAAV,EAAoCf,OAApC,EAAT;;YAEM,CAACe,WAAWmE,IAAhB,EAAsB;uBACVjF,KAAX,GAAmBc,WAAWd,KAAX,IAAoB,6BAAvC;;eAGMc,UAAP;KAX+B;eAcpB,mBAAUA,UAAV,EAAoCf,OAApC,EAAb;YACQ+E,SAAS7B,OAAOnC,WAAW1B,MAAlB,EAA0BU,WAA1B,OAA4C,OAA3D;;YAGIgB,WAAWiE,IAAX,MAAqBD,SAAS,GAAT,GAAe,EAApC,KAA2ChE,WAAWiE,IAAX,KAAoB,EAAnE,EAAuE;uBAC3DA,IAAX,GAAkB3E,SAAlB;;;YAIG,CAACU,WAAWP,IAAhB,EAAsB;uBACVA,IAAX,GAAkB,GAAlB;;;;;eAOMO,UAAP;;CA/BF,CAmCA;;ADlCA,IAAMT,YAA2B;YACvB,OADuB;gBAEnBX,QAAKgF,UAFc;WAGxBhF,QAAKkB,KAHmB;eAIpBlB,QAAKgB;CAJlB,CAOA;;ADHA,SAAAsE,QAAA,CAAkBL,YAAlB,EAAA;WACQ,OAAOA,aAAaG,MAApB,KAA+B,SAA/B,GAA2CH,aAAaG,MAAxD,GAAiE7B,OAAO0B,aAAavF,MAApB,EAA4BU,WAA5B,OAA8C,KAAtH;;;AAID,IAAMO,YAA2B;YACvB,IADuB;gBAGnB,IAHmB;WAKxB,eAAUS,UAAV,EAAoCf,OAApC,EAAT;YACQ4E,eAAe7D,UAArB;;qBAGagE,MAAb,GAAsBE,SAASL,YAAT,CAAtB;;qBAGaE,YAAb,GAA4B,CAACF,aAAapE,IAAb,IAAqB,GAAtB,KAA8BoE,aAAa3D,KAAb,GAAqB,MAAM2D,aAAa3D,KAAxC,GAAgD,EAA9E,CAA5B;qBACaT,IAAb,GAAoBH,SAApB;qBACaY,KAAb,GAAqBZ,SAArB;eAEOuE,YAAP;KAhB+B;eAmBpB,mBAAUA,YAAV,EAAqC5E,OAArC,EAAb;;YAEM4E,aAAaI,IAAb,MAAuBC,SAASL,YAAT,IAAyB,GAAzB,GAA+B,EAAtD,KAA6DA,aAAaI,IAAb,KAAsB,EAAvF,EAA2F;yBAC7EA,IAAb,GAAoB3E,SAApB;;;YAIG,OAAOuE,aAAaG,MAApB,KAA+B,SAAnC,EAA8C;yBAChC1F,MAAb,GAAuBuF,aAAaG,MAAb,GAAsB,KAAtB,GAA8B,IAArD;yBACaA,MAAb,GAAsB1E,SAAtB;;;YAIGuE,aAAaE,YAAjB,EAA+B;wCACRF,aAAaE,YAAb,CAA0BvB,KAA1B,CAAgC,GAAhC,CADQ;;gBACvB/C,IADuB;gBACjBS,KADiB;;yBAEjBT,IAAb,GAAqBA,QAAQA,SAAS,GAAjB,GAAuBA,IAAvB,GAA8BH,SAAnD;yBACaY,KAAb,GAAqBA,KAArB;yBACa6D,YAAb,GAA4BzE,SAA5B;;;qBAIYwE,QAAb,GAAwBxE,SAAxB;eAEOuE,YAAP;;CA1CF,CA8CA;;ADvDA,IAAMtE,YAA2B;YACvB,KADuB;gBAEnBb,UAAGkF,UAFgB;WAGxBlF,UAAGoB,KAHqB;eAIpBpB,UAAGkB;CAJhB,CAOA;;ADMA,IAAMoB,IAAkB,EAAxB;AACA,IAAM2C,QAAQ,IAAd;;AAGA,IAAMR,eAAe,4BAA4BQ,QAAQ,2EAAR,GAAsF,EAAlH,IAAwH,GAA7I;AACA,IAAMD,WAAW,aAAjB;AACA,IAAMH,eAAeE,OAAOA,OAAO,YAAYC,QAAZ,GAAuB,GAAvB,GAA6BA,QAA7B,GAAwCA,QAAxC,GAAmD,GAAnD,GAAyDA,QAAzD,GAAoEA,QAA3E,IAAuF,GAAvF,GAA6FD,OAAO,gBAAgBC,QAAhB,GAA2B,GAA3B,GAAiCA,QAAjC,GAA4CA,QAAnD,CAA7F,GAA4J,GAA5J,GAAkKD,OAAO,MAAMC,QAAN,GAAiBA,QAAxB,CAAzK,CAArB;;;;;;;;;;;;AAaA,IAAML,UAAU,uDAAhB;AACA,IAAMG,UAAU,4DAAhB;AACA,IAAMF,UAAUJ,MAAMM,OAAN,EAAe,YAAf,CAAhB;AACA,AACA,AACA,AACA,AAEA,AAEA,IAAMJ,gBAAgB,qCAAtB;AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AAEA,IAAMN,aAAa,IAAIG,MAAJ,CAAWE,YAAX,EAAyB,GAAzB,CAAnB;AACA,IAAM1C,cAAc,IAAIwC,MAAJ,CAAWM,YAAX,EAAyB,GAAzB,CAApB;AACA,IAAMtB,iBAAiB,IAAIgB,MAAJ,CAAWC,MAAM,KAAN,EAAaG,OAAb,EAAsB,OAAtB,EAA+B,OAA/B,EAAwCC,OAAxC,CAAX,EAA6D,GAA7D,CAAvB;AACA,AACA,IAAM1C,aAAa,IAAIqC,MAAJ,CAAWC,MAAM,KAAN,EAAaC,YAAb,EAA2BC,aAA3B,CAAX,EAAsD,GAAtD,CAAnB;AACA,IAAMrC,cAAcH,UAApB;AACA,AACA,AAEA,SAAAF,gBAAA,CAA0BqC,GAA1B,EAAA;QACOF,SAASG,YAAYD,GAAZ,CAAf;WACQ,CAACF,OAAOzD,KAAP,CAAa0D,UAAb,CAAD,GAA4BC,GAA5B,GAAkCF,MAA1C;;AAGD,IAAMtD,YAA8C;YAC1C,QAD0C;WAG3C,kBAAUS,UAAV,EAAoCf,OAApC,EAAT;YACQgC,mBAAmBjB,UAAzB;YACMoB,KAAKH,iBAAiBG,EAAjB,GAAuBH,iBAAiBxB,IAAjB,GAAwBwB,iBAAiBxB,IAAjB,CAAsB+C,KAAtB,CAA4B,GAA5B,CAAxB,GAA2D,EAA7F;yBACiB/C,IAAjB,GAAwBH,SAAxB;YAEI2B,iBAAiBf,KAArB,EAA4B;gBACvBuC,iBAAiB,KAArB;gBACM3B,UAAwB,EAA9B;gBACM8B,UAAU3B,iBAAiBf,KAAjB,CAAuBsC,KAAvB,CAA6B,GAA7B,CAAhB;iBAEK,IAAInB,IAAI,CAAR,EAAWe,KAAKQ,QAAQvC,MAA7B,EAAqCgB,IAAIe,EAAzC,EAA6C,EAAEf,CAA/C,EAAkD;oBAC3CqB,SAASE,QAAQvB,CAAR,EAAWmB,KAAX,CAAiB,GAAjB,CAAf;wBAEQE,OAAO,CAAP,CAAR;yBACM,IAAL;4BACOC,UAAUD,OAAO,CAAP,EAAUF,KAAV,CAAgB,GAAhB,CAAhB;6BACK,IAAInB,KAAI,CAAR,EAAWe,MAAKO,QAAQtC,MAA7B,EAAqCgB,KAAIe,GAAzC,EAA6C,EAAEf,EAA/C,EAAkD;+BAC9Cf,IAAH,CAAQqC,QAAQtB,EAAR,CAAR;;;yBAGG,SAAL;yCACkBF,OAAjB,GAA2BS,kBAAkBc,OAAO,CAAP,CAAlB,EAA6BzD,OAA7B,CAA3B;;yBAEI,MAAL;yCACkBiC,IAAjB,GAAwBU,kBAAkBc,OAAO,CAAP,CAAlB,EAA6BzD,OAA7B,CAAxB;;;yCAGiB,IAAjB;gCACQ2C,kBAAkBc,OAAO,CAAP,CAAlB,EAA6BzD,OAA7B,CAAR,IAAiD2C,kBAAkBc,OAAO,CAAP,CAAlB,EAA6BzD,OAA7B,CAAjD;;;;gBAKCwD,cAAJ,EAAoBxB,iBAAiBH,OAAjB,GAA2BA,OAA3B;;yBAGJZ,KAAjB,GAAyBZ,SAAzB;aAEK,IAAI+B,MAAI,CAAR,EAAWe,OAAKhB,GAAGf,MAAxB,EAAgCgB,MAAIe,IAApC,EAAwC,EAAEf,GAA1C,EAA6C;gBACtCiB,OAAOlB,GAAGC,GAAH,EAAMmB,KAAN,CAAY,GAAZ,CAAb;iBAEK,CAAL,IAAUZ,kBAAkBU,KAAK,CAAL,CAAlB,CAAV;gBAEI,CAACrD,QAAQsD,cAAb,EAA6B;;oBAExB;yBACE,CAAL,IAAUb,SAASC,OAAT,CAAiBC,kBAAkBU,KAAK,CAAL,CAAlB,EAA2BrD,OAA3B,EAAoCD,WAApC,EAAjB,CAAV;iBADD,CAEE,OAAOyC,CAAP,EAAU;qCACMvC,KAAjB,GAAyB+B,iBAAiB/B,KAAjB,IAA0B,6EAA6EuC,CAAhI;;aALF,MAOO;qBACD,CAAL,IAAUG,kBAAkBU,KAAK,CAAL,CAAlB,EAA2BrD,OAA3B,EAAoCD,WAApC,EAAV;;eAGEqC,GAAH,IAAQiB,KAAKlC,IAAL,CAAU,GAAV,CAAR;;eAGMa,gBAAP;KA5DkD;eA+DvC,sBAAUA,gBAAV,EAA6ChC,OAA7C,EAAb;YACQe,aAAaiB,gBAAnB;YACMG,KAAKiB,QAAQpB,iBAAiBG,EAAzB,CAAX;YACIA,EAAJ,EAAQ;iBACF,IAAIC,IAAI,CAAR,EAAWe,KAAKhB,GAAGf,MAAxB,EAAgCgB,IAAIe,EAApC,EAAwC,EAAEf,CAA1C,EAA6C;oBACtCS,SAASK,OAAOf,GAAGC,CAAH,CAAP,CAAf;oBACMW,QAAQF,OAAOI,WAAP,CAAmB,GAAnB,CAAd;oBACMZ,YAAaQ,OAAOC,KAAP,CAAa,CAAb,EAAgBC,KAAhB,CAAD,CAAyBxB,OAAzB,CAAiCC,WAAjC,EAA8CC,gBAA9C,EAAgEF,OAAhE,CAAwEC,WAAxE,EAAqFE,WAArF,EAAkGH,OAAlG,CAA0GyB,cAA1G,EAA0HpB,UAA1H,CAAlB;oBACIU,SAASO,OAAOC,KAAP,CAAaC,QAAQ,CAArB,CAAb;;oBAGI;6BACO,CAAC/C,QAAQuC,GAAT,GAAeE,SAASC,OAAT,CAAiBC,kBAAkBL,MAAlB,EAA0BtC,OAA1B,EAAmCD,WAAnC,EAAjB,CAAf,GAAoF0C,SAASG,SAAT,CAAmBN,MAAnB,CAA9F;iBADD,CAEE,OAAOE,CAAP,EAAU;+BACAvC,KAAX,GAAmBc,WAAWd,KAAX,IAAoB,0DAA0D,CAACD,QAAQuC,GAAT,GAAe,OAAf,GAAyB,SAAnF,IAAgG,iBAAhG,GAAoHC,CAA3J;;mBAGEJ,CAAH,IAAQC,YAAY,GAAZ,GAAkBC,MAA1B;;uBAGU9B,IAAX,GAAkB2B,GAAGhB,IAAH,CAAQ,GAAR,CAAlB;;YAGKU,UAAUG,iBAAiBH,OAAjB,GAA2BG,iBAAiBH,OAAjB,IAA4B,EAAvE;YAEIG,iBAAiBE,OAArB,EAA8BL,QAAQ,SAAR,IAAqBG,iBAAiBE,OAAtC;YAC1BF,iBAAiBC,IAArB,EAA2BJ,QAAQ,MAAR,IAAkBG,iBAAiBC,IAAnC;YAErBf,SAAS,EAAf;aACK,IAAMI,IAAX,IAAmBO,OAAnB,EAA4B;gBACvBA,QAAQP,IAAR,MAAkBS,EAAET,IAAF,CAAtB,EAA+B;uBACvBD,IAAP,CACCC,KAAKC,OAAL,CAAaC,WAAb,EAA0BC,gBAA1B,EAA4CF,OAA5C,CAAoDC,WAApD,EAAiEE,WAAjE,EAA8EH,OAA9E,CAAsFI,UAAtF,EAAkGC,UAAlG,IACA,GADA,GAEAC,QAAQP,IAAR,EAAcC,OAAd,CAAsBC,WAAtB,EAAmCC,gBAAnC,EAAqDF,OAArD,CAA6DC,WAA7D,EAA0EE,WAA1E,EAAuFH,OAAvF,CAA+FO,WAA/F,EAA4GF,UAA5G,CAHD;;;YAOEV,OAAOE,MAAX,EAAmB;uBACPH,KAAX,GAAmBC,OAAOC,IAAP,CAAY,GAAZ,CAAnB;;eAGMJ,UAAP;;CAzGF,CA6GA;;ADnKA,IAAMC,YAAY,iBAAlB;AACA,AAEA;AACA,IAAMV,YAAqD;YACjD,KADiD;WAGlD,kBAAUS,UAAV,EAAoCf,OAApC,EAAT;YACQc,UAAUC,WAAWP,IAAX,IAAmBO,WAAWP,IAAX,CAAgBL,KAAhB,CAAsBa,SAAtB,CAAnC;YACIpB,gBAAgBmB,UAApB;YAEID,OAAJ,EAAa;gBACNzB,SAASW,QAAQX,MAAR,IAAkBO,cAAcP,MAAhC,IAA0C,KAAzD;gBACMoB,MAAMK,QAAQ,CAAR,EAAWf,WAAX,EAAZ;gBACMF,MAAMiB,QAAQ,CAAR,CAAZ;gBACMF,YAAevB,MAAf,UAAyBW,QAAQS,GAAR,IAAeA,GAAxC,CAAN;gBACMC,gBAAgBvB,QAAQyB,SAAR,CAAtB;0BAEcH,GAAd,GAAoBA,GAApB;0BACcZ,GAAd,GAAoBA,GAApB;0BACcW,IAAd,GAAqBH,SAArB;gBAEIK,aAAJ,EAAmB;gCACFA,cAAcG,KAAd,CAAoBjB,aAApB,EAAmCI,OAAnC,CAAhB;;SAZF,MAcO;0BACQC,KAAd,GAAsBL,cAAcK,KAAd,IAAuB,wBAA7C;;eAGML,aAAP;KAzByD;eA4B9C,sBAAUA,aAAV,EAAuCI,OAAvC,EAAb;YACQX,SAASW,QAAQX,MAAR,IAAkBO,cAAcP,MAAhC,IAA0C,KAAzD;YACMoB,MAAMb,cAAca,GAA1B;YACMG,YAAevB,MAAf,UAAyBW,QAAQS,GAAR,IAAeA,GAAxC,CAAN;YACMC,gBAAgBvB,QAAQyB,SAAR,CAAtB;YAEIF,aAAJ,EAAmB;4BACFA,cAAcC,SAAd,CAAwBf,aAAxB,EAAuCI,OAAvC,CAAhB;;YAGKO,gBAAgBX,aAAtB;YACMC,MAAMD,cAAcC,GAA1B;sBACcW,IAAd,IAAwBC,OAAOT,QAAQS,GAAvC,UAA8CZ,GAA9C;eAEOU,aAAP;;CA1CF,CA8CA;;AD5DA,IAAMH,OAAO,0DAAb;AACA,AAEA;AACA,IAAME,YAAsE;YAClE,UADkE;WAGnE,eAAUV,aAAV,EAAuCI,OAAvC,EAAT;YACQF,iBAAiBF,aAAvB;uBACeR,IAAf,GAAsBU,eAAeD,GAArC;uBACeA,GAAf,GAAqBQ,SAArB;YAEI,CAACL,QAAQE,QAAT,KAAsB,CAACJ,eAAeV,IAAhB,IAAwB,CAACU,eAAeV,IAAf,CAAoBe,KAApB,CAA0BC,IAA1B,CAA/C,CAAJ,EAAqF;2BACrEH,KAAf,GAAuBH,eAAeG,KAAf,IAAwB,oBAA/C;;eAGMH,cAAP;KAZ0E;eAe/D,mBAAUA,cAAV,EAAyCE,OAAzC,EAAb;YACQJ,gBAAgBE,cAAtB;;sBAEcD,GAAd,GAAoB,CAACC,eAAeV,IAAf,IAAuB,EAAxB,EAA4BW,WAA5B,EAApB;eACOH,aAAP;;CAnBF,CAuBA;;ADhCAT,QAAQQ,QAAKN,MAAb,IAAuBM,OAAvB;AAEA,AACAR,QAAQO,UAAML,MAAd,IAAwBK,SAAxB;AAEA,AACAP,QAAQM,UAAGJ,MAAX,IAAqBI,SAArB;AAEA,AACAN,QAAQK,UAAIH,MAAZ,IAAsBG,SAAtB;AAEA,AACAL,QAAQI,UAAOF,MAAf,IAAyBE,SAAzB;AAEA,AACAJ,QAAQG,UAAID,MAAZ,IAAsBC,SAAtB;AAEA,AACAH,QAAQC,UAAKC,MAAb,IAAuBD,SAAvB,CAEA;;;;;;;;;;;;;;;;;"}