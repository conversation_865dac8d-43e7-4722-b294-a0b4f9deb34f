{"name": "jeps-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "dev": "vite && tailwindcss -i ./src/index.css -o ./dist/output.css --watch", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^41.4.2", "@ckeditor/ckeditor5-react": "^9.5.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@tailwindcss/cli": "^4.1.4", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "formik": "^2.4.6", "framer-motion": "^12.9.2", "import": "^0.0.6", "postcss": "^8.5.3", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-image-gallery": "^1.4.0", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.5.2", "react-select": "^5.10.1", "react-toastify": "^11.0.5", "tailwindcss": "^4.1.4", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.1"}}